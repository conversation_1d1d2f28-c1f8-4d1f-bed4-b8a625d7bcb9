import logging
from .base import StorageInterface
import boto3
from botocore.config import Config

logger = logging.getLogger(__name__)


class S3Storage(StorageInterface):
    def __init__(self, config):
        self.config = config
        self.session = boto3.Session()

        # 创建常规操作的 botocore 配置（适用于轻量级操作如 head_object）
        self.regular_boto_config = Config(
            signature_version="s3v4",
            retries={
                "total_max_attempts": 3,  # 总共尝试3次（1次初始 + 2次重试）
                "mode": "standard",  # 使用标准重试模式
            },
            connect_timeout=5,  # 连接超时5秒
            read_timeout=5,  # 读取超时5秒
            max_pool_connections=10,  # 连接池大小
            proxies={
                "http": None,
                "https": None,
            },  # 明确禁用代理，忽略环境变量中的代理设置
            s3={"addressing_style": "virtual"},  # 使用虚拟寻址样式
        )

        # 创建上传操作的 botocore 配置（适用于大文件上传如 YouTube 转录）
        self.upload_boto_config = Config(
            signature_version="s3v4",
            retries={
                "total_max_attempts": 5,  # 总共尝试5次（1次初始 + 4次重试）
                "mode": "standard",  # 使用标准重试模式
            },
            connect_timeout=10,  # 连接超时10秒
            read_timeout=60,  # 读取超时60秒
            max_pool_connections=5,  # 连接池大小
            proxies={
                "http": None,
                "https": None,
            },  # 明确禁用代理，忽略环境变量中的代理设置
            s3={"addressing_style": "virtual"},  # 使用虚拟寻址样式
        )

        # 创建常规 S3 客户端（默认客户端，用于大多数操作）
        self.client = self.session.client(
            "s3",
            **config.get_client_kwargs(),
            config=self.regular_boto_config,
        )

        # 创建上传专用 S3 客户端
        self.upload_client = self.session.client(
            "s3",
            **config.get_client_kwargs(),
            config=self.upload_boto_config,
        )

    def generate_presigned_url_for_read(self, key, expired):
        return self.client.generate_presigned_url(
            "get_object",
            Params={"Bucket": self.config.get_bucket_name(), "Key": key},
            ExpiresIn=expired,
        )

    def generate_presigned_url_for_upload(
        self, key, expired, content_type=None, content_md5_base_64=None
    ):
        """
        生成用于上传文件的预签名 URL

        :param key: 对象的键（文件路径）
        :param expired: URL 的有效期（秒）
        :param content_type: 文件的 MIME 类型
        :return: 预签名 URL 和需要的额外头部信息
        """
        try:
            params = {"Bucket": self.config.get_bucket_name(), "Key": key}
            if content_type:
                params["ContentType"] = content_type  # 确保 Content-Type 正确

            response = self.client.generate_presigned_url(
                "put_object", Params=params, ExpiresIn=expired, HttpMethod="PUT"
            )

            return response
        except Exception as e:
            logger.exception(f"Error generating presigned URL for upload: {e}")
            return None

    def check_file_exist(self, key):
        return self.client.head_object(Bucket=self.config.get_bucket_name(), Key=key)

    def generate_key(self, user_id, fingerprint, file_type):
        return f"{user_id}-{fingerprint}.{file_type}"

    def generate_file_url(self, key):
        return f"{self.config.get_endpoint_url()}/{key}"

    def upload_file(self, key, file_path):
        """
        上传文件到存储服务

        使用上传专用客户端，适用于大文件上传（如 YouTube 转录文件）

        :param key: 文件的键（路径）
        :param file_path: 本地文件路径
        """
        # 使用上传专用客户端，适合大文件上传
        self.upload_client.upload_file(file_path, self.config.get_bucket_name(), key)

    def permanently_delete_file(self, key):
        """
        删除指定的文件(仅当注销账号时使用)

        :param key: 文件的键（路径）
        """
        try:
            self.client.delete_object(Bucket=self.config.get_bucket_name(), Key=key)
            return True
        except Exception as e:
            logger.exception(f"Error deleting file from S3: {e}")
            return False

    def create_multipart_upload(self, key, content_type=None):
        """
        创建分块上传

        :param key: 文件键
        :param content_type: 文件MIME类型
        :return: 上传ID
        """
        try:
            params = {"Bucket": self.config.get_bucket_name(), "Key": key}
            if content_type:
                params["ContentType"] = content_type

            response = self.client.create_multipart_upload(**params)
            return response["UploadId"]
        except Exception as e:
            logger.exception(f"Error creating multipart upload: {e}")
            raise

    def list_multipart_parts(self, key, upload_id):
        """
        列出已上传的分块

        :param key: 文件键
        :param upload_id: 上传ID
        :return: 已上传分块列表
        """
        try:
            response = self.client.list_parts(
                Bucket=self.config.get_bucket_name(), Key=key, UploadId=upload_id
            )

            parts = []
            if "Parts" in response:
                for part in response["Parts"]:
                    parts.append(
                        {
                            "PartNumber": part["PartNumber"],
                            "ETag": part["ETag"],
                            "Size": part["Size"],
                        }
                    )

            return parts
        except Exception as e:
            logger.exception(f"Error listing multipart parts: {e}")
            raise

    def generate_presigned_url_for_multipart_part(
        self, key, upload_id, part_number, expired
    ):
        """
        为分块生成预签名URL

        :param key: 文件键
        :param upload_id: 上传ID
        :param part_number: 分块编号
        :param expired: URL有效期（秒）
        :return: 预签名URL
        """
        try:
            response = self.client.generate_presigned_url(
                "upload_part",
                Params={
                    "Bucket": self.config.get_bucket_name(),
                    "Key": key,
                    "UploadId": upload_id,
                    "PartNumber": part_number,
                },
                ExpiresIn=expired,
                HttpMethod="PUT",
            )
            return response
        except Exception as e:
            logger.exception(f"Error generating presigned URL for multipart part: {e}")
            raise

    def complete_multipart_upload(self, key, upload_id, parts):
        """
        完成分块上传

        :param key: 文件键
        :param upload_id: 上传ID
        :param parts: 分块列表，格式为 [{"ETag": "...", "PartNumber": 1}, ...]
        :return: 完成结果
        """
        try:
            response = self.client.complete_multipart_upload(
                Bucket=self.config.get_bucket_name(),
                Key=key,
                UploadId=upload_id,
                MultipartUpload={"Parts": parts},
            )
            return response
        except Exception as e:
            logger.exception(f"Error completing multipart upload: {e}")
            raise

    def abort_multipart_upload(self, key, upload_id):
        """
        取消分块上传

        :param key: 文件键
        :param upload_id: 上传ID
        """
        try:
            self.client.abort_multipart_upload(
                Bucket=self.config.get_bucket_name(), Key=key, UploadId=upload_id
            )
        except Exception as e:
            logger.exception(f"Error aborting multipart upload: {e}")
            raise
