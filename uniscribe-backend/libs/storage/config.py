# storage/config.py
from abc import ABC, abstractmethod


class StorageConfig(ABC):
    @abstractmethod
    def get_client_kwargs(self):
        """返回创建客户端所需的参数"""
        pass

    @abstractmethod
    def get_bucket_name(self):
        """返回存储桶名称"""
        pass

    @abstractmethod
    def get_endpoint_url(self):
        """返回存储桶的 endpoint url"""
        pass


class S3Config(StorageConfig):
    def __init__(self, config):
        self.config = config

    def get_client_kwargs(self):
        return {
            "region_name": "auto",
            "endpoint_url": self.config["endpoint_url"],
            "aws_access_key_id": self.config["access_key"],
            "aws_secret_access_key": self.config["secret_key"],
        }

    def get_bucket_name(self):
        return self.config["bucket_name"]

    def get_endpoint_url(self):
        return self.config["endpoint_url"]


class COSConfig(StorageConfig):
    def __init__(self, config):
        self.config = config

    def get_client_kwargs(self):
        return {
            "Region": self.config["region"],
            "SecretId": self.config["secret_id"],
            "SecretKey": self.config["secret_key"],
            "Token": self.config.get("token"),
            "Scheme": self.config.get("scheme", "https"),
        }

    def get_bucket_name(self):
        return self.config["bucket_name"]

    def get_endpoint_url(self):
        return f"{self.config['scheme']}://{self.config['bucket_name']}.cos.{self.config['region']}.myqcloud.com"
