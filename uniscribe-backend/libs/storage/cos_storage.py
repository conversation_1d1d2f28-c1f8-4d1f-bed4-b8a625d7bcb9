from .base import StorageInterface
from qcloud_cos import CosConfig, CosS3Client


class COSStorage(StorageInterface):
    def __init__(self, config):
        self.config = config
        cos_config = CosConfig(
            **config.get_client_kwargs(),
        )
        self.client = CosS3Client(cos_config)

    def generate_presigned_url_for_read(self, key, expired):
        return self.client.get_presigned_download_url(
            Bucket=self.config.get_bucket_name(),
            Key=key,
            Expired=expired,
        )

    def generate_presigned_url_for_upload(
        self, key, expired, content_md5_base_64=None, content_type=None
    ):
        url = self.client.get_presigned_url(
            Bucket=self.config.get_bucket_name(),
            Key=key,
            Method="PUT",
            Expired=expired,
            Headers={"Content-MD5": content_md5_base_64},
        )
        return url

    def check_file_exist(self, key):
        return self.client.object_exists(
            Bucket=self.config.get_bucket_name(),
            Key=key,
        )

    def generate_key(self, user_id, fingerprint, file_type):
        return f"{user_id}-{fingerprint}.{file_type}"

    def generate_file_url(self, key):
        return f"{self.config.get_endpoint_url()}/{key}"

    def permanently_delete_file(self, key):
        """
        删除指定的文件(仅当注销账号时使用)

        :param key: 文件的键（路径）
        """
        pass

    def create_multipart_upload(self, key, content_type=None):
        """创建分块上传 - COS暂不支持"""
        raise NotImplementedError("COS storage does not support multipart upload yet")

    def list_multipart_parts(self, key, upload_id):
        """列出已上传的分块 - COS暂不支持"""
        raise NotImplementedError("COS storage does not support multipart upload yet")

    def generate_presigned_url_for_multipart_part(
        self, key, upload_id, part_number, expired
    ):
        """为分块生成预签名URL - COS暂不支持"""
        raise NotImplementedError("COS storage does not support multipart upload yet")

    def complete_multipart_upload(self, key, upload_id, parts):
        """完成分块上传 - COS暂不支持"""
        raise NotImplementedError("COS storage does not support multipart upload yet")

    def abort_multipart_upload(self, key, upload_id):
        """取消分块上传 - COS暂不支持"""
        raise NotImplementedError("COS storage does not support multipart upload yet")
