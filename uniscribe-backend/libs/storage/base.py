from abc import ABC, abstractmethod


class StorageInterface(ABC):
    @abstractmethod
    def generate_presigned_url_for_read(self, key, expired):
        pass

    @abstractmethod
    def generate_presigned_url_for_upload(
        self, key, expired, content_type=None, content_md5_base_64=None
    ):
        pass

    @abstractmethod
    def check_file_exist(self, key):
        pass

    @abstractmethod
    def generate_key(self, user_id, fingerprint, file_type):
        pass

    @abstractmethod
    def generate_file_url(self, key):
        pass

    @abstractmethod
    def permanently_delete_file(self, key):
        pass

    # Multipart upload methods
    @abstractmethod
    def create_multipart_upload(self, key, content_type=None):
        """
        创建分块上传

        :param key: 文件键
        :param content_type: 文件MIME类型
        :return: 上传ID
        """
        pass

    @abstractmethod
    def list_multipart_parts(self, key, upload_id):
        """
        列出已上传的分块

        :param key: 文件键
        :param upload_id: 上传ID
        :return: 已上传分块列表
        """
        pass

    @abstractmethod
    def generate_presigned_url_for_multipart_part(
        self, key, upload_id, part_number, expired
    ):
        """
        为分块生成预签名URL

        :param key: 文件键
        :param upload_id: 上传ID
        :param part_number: 分块编号
        :param expired: URL有效期（秒）
        :return: 预签名URL
        """
        pass

    @abstractmethod
    def complete_multipart_upload(self, key, upload_id, parts):
        """
        完成分块上传

        :param key: 文件键
        :param upload_id: 上传ID
        :param parts: 分块列表，格式为 [{"ETag": "...", "PartNumber": 1}, ...]
        :return: 完成结果
        """
        pass

    @abstractmethod
    def abort_multipart_upload(self, key, upload_id):
        """
        取消分块上传

        :param key: 文件键
        :param upload_id: 上传ID
        """
        pass
