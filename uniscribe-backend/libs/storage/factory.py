# storage/factory.py
from .s3_storage import S3Storage
from .cos_storage import COSStorage
from .config import S3Config, COSConfig


class StorageFactory:
    @staticmethod
    def get_storage(storage_type, config):
        if storage_type == "s3":
            return S3Storage(S3Config(config))
        elif storage_type == "cos":
            return COSStorage(COSConfig(config))
        else:
            raise ValueError(f"Unsupported storage type: {storage_type}")
