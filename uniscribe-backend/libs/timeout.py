"""
超时控制工具模块

提供线程安全的超时控制机制，用于限制函数执行时间。
"""

import threading
import logging
from typing import Any, Callable, Tuple, TypeVar, Optional, Dict, Union

logger = logging.getLogger(__name__)

# 定义泛型类型变量
T = TypeVar('T')


class TimeoutError(Exception):
    """超时异常类"""
    
    def __init__(self, message="Operation timed out", details=None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


def with_thread_timeout(
    seconds: float,
    func: Callable[..., T],
    *args: Any,
    **kwargs: Any
) -> Tuple[Optional[T], Optional[Exception]]:
    """
    使用线程实现的超时控制函数
    
    在指定的时间内执行函数，如果超时则返回 None 和 TimeoutError 异常。
    这个实现是线程安全的，适用于所有环境。
    
    Args:
        seconds: 超时时间（秒）
        func: 要执行的函数
        *args: 传递给函数的位置参数
        **kwargs: 传递给函数的关键字参数
        
    Returns:
        Tuple[Optional[T], Optional[Exception]]: 
            - 如果函数成功执行，返回 (result, None)
            - 如果函数执行超时，返回 (None, TimeoutError)
            - 如果函数执行出错，返回 (None, Exception)
    
    Example:
        ```python
        def slow_function():
            time.sleep(5)
            return "Done"
            
        result, error = with_thread_timeout(3, slow_function)
        if error:
            print(f"Function timed out or failed: {error}")
        else:
            print(f"Function result: {result}")
        ```
    """
    result_container = []
    exception_container = []

    def worker():
        try:
            result_container.append(func(*args, **kwargs))
        except Exception as e:
            exception_container.append(e)

    thread = threading.Thread(target=worker)
    thread.daemon = True  # 设置为守护线程，主线程结束时会自动终止
    thread.start()
    thread.join(seconds)  # 等待指定的秒数

    if thread.is_alive():
        # 线程仍在运行，表示超时
        return None, TimeoutError(f"Operation timed out after {seconds} seconds")
    elif exception_container:
        # 线程抛出了异常
        return None, exception_container[0]
    else:
        # 线程正常完成
        return result_container[0], None


def with_timeout_handler(
    timeout_seconds: float,
    func: Callable[..., T],
    *args: Any,
    on_timeout: Optional[Callable[[float], Any]] = None,
    on_error: Optional[Callable[[Exception], Any]] = None,
    **kwargs: Any
) -> T:
    """
    带有错误处理的超时控制函数
    
    在指定的时间内执行函数，如果超时或出错则调用相应的处理函数。
    
    Args:
        timeout_seconds: 超时时间（秒）
        func: 要执行的函数
        *args: 传递给函数的位置参数
        on_timeout: 超时时的处理函数，接收超时时间作为参数
        on_error: 出错时的处理函数，接收异常对象作为参数
        **kwargs: 传递给函数的关键字参数
        
    Returns:
        函数的返回值，或者处理函数的返回值
        
    Raises:
        TimeoutError: 如果超时且没有提供 on_timeout 处理函数
        Exception: 如果出错且没有提供 on_error 处理函数
    
    Example:
        ```python
        def slow_function():
            time.sleep(5)
            return "Done"
            
        def handle_timeout(seconds):
            return f"Function timed out after {seconds} seconds"
            
        result = with_timeout_handler(3, slow_function, on_timeout=handle_timeout)
        print(result)  # 输出: "Function timed out after 3 seconds"
        ```
    """
    result, error = with_thread_timeout(timeout_seconds, func, *args, **kwargs)
    
    if error is None:
        return result
    elif isinstance(error, TimeoutError):
        if on_timeout:
            return on_timeout(timeout_seconds)
        raise error
    else:
        if on_error:
            return on_error(error)
        raise error


def with_graceful_degradation(
    timeout_seconds: float,
    func: Callable[..., T],
    *args: Any,
    fallback_value: Any = None,
    log_prefix: str = "",
    **kwargs: Any
) -> T:
    """
    带有优雅降级的超时控制函数
    
    在指定的时间内执行函数，如果超时或出错则返回降级值。
    
    Args:
        timeout_seconds: 超时时间（秒）
        func: 要执行的函数
        *args: 传递给函数的位置参数
        fallback_value: 降级值，默认为 None
        log_prefix: 日志前缀，用于标识日志来源
        **kwargs: 传递给函数的关键字参数
        
    Returns:
        函数的返回值，或者降级值
    
    Example:
        ```python
        def check_file_exist(file_key):
            # 可能耗时的操作
            return storage.client.head_object(Bucket="my-bucket", Key=file_key)
            
        # 如果 3 秒内无法完成检查，则假设文件存在
        result = with_graceful_degradation(
            3, 
            check_file_exist, 
            "my-file.txt",
            fallback_value=True,
            log_prefix="[file_check]"
        )
        ```
    """
    def on_timeout(seconds):
        logger.warning(
            f"{log_prefix} Operation timed out after {seconds} seconds, using fallback value"
        )
        return fallback_value
        
    def on_error(error):
        logger.error(
            f"{log_prefix} Operation failed with error: {str(error)}, using fallback value"
        )
        return fallback_value
    
    return with_timeout_handler(
        timeout_seconds,
        func,
        *args,
        on_timeout=on_timeout,
        on_error=on_error,
        **kwargs
    )
