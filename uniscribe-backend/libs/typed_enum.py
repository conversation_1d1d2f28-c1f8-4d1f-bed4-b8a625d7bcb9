"""Enhanced Enum implementation with ID support and utility methods."""

from enum import Enum
from typing import TypeVar, List, Any

T = TypeVar("T", bound="TypedEnum")


class TypedEnum(Enum):
    """Enhanced Enum class with ID support and additional utility methods.

    Each enum member automatically gets an 'id' attribute that can be used
    for database storage or other purposes where a numeric value is needed.
    """

    id: int  # 添加类型提示，告诉 Pylance 这个类有 id 属性

    def __new__(cls, *_args: Any) -> Any:
        """Create a new enum member with an automatically assigned ID."""
        obj = object.__new__(cls)
        obj._value_ = len(cls.__members__) + 1
        obj.id = obj._value_
        return obj

    @classmethod
    def by_name(cls: type[T], name: str) -> T:
        """Get an enum member by its name.

        Args:
            name: The name of the enum member to find

        Returns:
            The enum member with the specified name

        Raises:
            ValueError: If no member with the given name exists
        """
        try:
            return next(member for member in cls if member.name == name)
        except StopIteration as exc:
            raise ValueError(f"No {cls.__name__} member with name {name}") from exc

    @classmethod
    def by_id(cls: type[T], id_value: int) -> T:
        """Get an enum member by its ID.

        Args:
            id_value: The ID of the enum member to find

        Returns:
            The enum member with the specified ID

        Raises:
            ValueError: If no member with the given ID exists
        """
        try:
            return next(member for member in cls if member.id == id_value)
        except StopIteration as exc:
            raise ValueError(f"No {cls.__name__} member with ID {id_value}") from exc

    @classmethod
    def ids(cls) -> List[int]:
        """Get a list of all member IDs.

        Returns:
            A list containing the ID of each enum member
        """
        return [member.id for member in cls]

    @classmethod
    def names(cls) -> List[str]:
        """Get a list of all member names.

        Returns:
            A list containing the name of each enum member
        """
        return [member.name for member in cls]
