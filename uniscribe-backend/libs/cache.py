import json
import logging
from typing import Any, Optional, Union, cast
import redis
from config import CONFIG

logger = logging.getLogger(__name__)


class RedisCache:
    """使用Redis实现的缓存，支持自动过期"""

    _redis_client = None

    @classmethod
    def get_client(cls):
        """获取Redis客户端连接"""
        if cls._redis_client is None:
            try:
                cls._redis_client = redis.Redis(
                    host=CONFIG.REDIS["host"],
                    port=CONFIG.REDIS["port"],
                    password=CONFIG.REDIS["password"],
                    ssl=CONFIG.REDIS["ssl"],
                    decode_responses=True,  # 自动将响应解码为字符串
                )
                # 测试连接
                cls._redis_client.ping()
                logger.info("Redis connection established successfully")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {str(e)}")
                raise
        return cls._redis_client

    def __init__(self, prefix: str = ""):
        """
        初始化Redis缓存

        Args:
            prefix: 缓存键前缀，用于区分不同的缓存命名空间
        """
        self.prefix = prefix
        self.client = self.get_client()

    def _get_full_key(self, key: str) -> str:
        """获取带前缀的完整键名"""
        return f"{self.prefix}:{key}" if self.prefix else key

    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值

        Args:
            key: 缓存键

        Returns:
            缓存的值，如果不存在或已过期则返回None
        """
        try:
            full_key = self._get_full_key(key)
            value = self.client.get(full_key)
            if value:
                # 确保value是字符串类型
                value_str = str(value) if not isinstance(value, str) else value
                return json.loads(value_str)
            return None
        except Exception as e:
            logger.error("Cache get error: %s", str(e))
            return None

    def set(self, key: str, value: Any, ttl: int) -> bool:
        """
        设置缓存

        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间(秒)

        Returns:
            设置是否成功
        """
        try:
            full_key = self._get_full_key(key)
            serialized_value = json.dumps(value)
            result = self.client.setex(full_key, ttl, serialized_value)
            # Redis返回OK表示成功
            return bool(result) and str(result).upper() == "OK"
        except Exception as e:
            logger.error("Cache set error: %s", str(e))
            return False

    def delete(self, key: str) -> bool:
        """
        删除缓存

        Args:
            key: 缓存键

        Returns:
            删除是否成功
        """
        try:
            full_key = self._get_full_key(key)
            result = self.client.delete(full_key)
            # 转换为布尔值，如果删除了至少一个键则返回True
            return bool(result)
        except Exception as e:
            logger.error("Cache delete error: %s", str(e))
            return False

    def exists(self, key: str) -> bool:
        """
        检查键是否存在

        Args:
            key: 缓存键

        Returns:
            键是否存在
        """
        try:
            full_key = self._get_full_key(key)
            result = self.client.exists(full_key)
            # 转换为布尔值
            return bool(result)
        except Exception as e:
            logger.error("Cache exists error: %s", str(e))
            return False

    def ttl(self, key: str) -> int:
        """
        获取键的剩余生存时间

        Args:
            key: 缓存键

        Returns:
            剩余生存时间(秒)，-1表示永不过期，-2表示键不存在
        """
        try:
            full_key = self._get_full_key(key)
            # 先检查键是否存在
            if not self.exists(full_key):
                return -2

            # 获取TTL
            result = self.client.ttl(full_key)

            # 安全地处理结果
            if result is None:
                return -2

            # 尝试将结果转换为字符串，然后转为整数
            try:
                return int(str(result))
            except (TypeError, ValueError):
                return -2
        except Exception as e:
            logger.error("Cache ttl error: %s", str(e))
            return -2


# 为了保持向后兼容，将DBCache重命名为Cache
Cache = RedisCache
