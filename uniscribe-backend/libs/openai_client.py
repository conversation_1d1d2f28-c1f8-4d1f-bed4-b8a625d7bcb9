from openai import OpenAI

from config import CONFIG

client = OpenAI(
    # base_url="https://api.gptsapi.net/v1",  # wildcard service
    api_key=CONFIG.OPENAI["api_key"],
)


def transcribe_audio(file_path, language, prompt):
    response = client.audio.transcriptions.create(
        file=file_path,
        model="whisper-1",
        response_format="verbose_json",
        language=language,
        prompt=prompt,
        temperature=0,
    )
    return response


if __name__ == "__main__":
    file_path = "/Users/<USER>/downloads/slice_one_minute.mp3"
    with open(file_path, "rb") as f:
        language = "zh"
        prompt = "以下是普通话句子"
        # prompt = "转录为简体中文"
        response = transcribe_audio(f, language, prompt)
        print(response)
