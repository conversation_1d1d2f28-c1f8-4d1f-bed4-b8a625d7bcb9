#!/usr/bin/env python3
"""
ffmpeg preprocessing related utility classes
"""

import os
import json
import time
import shutil
import tempfile
import subprocess
import logging
from typing import Tuple, Optional, Dict, Any

from constants.transcription import (
    FFMPEG_PREPROCESSING_SIZE_THRESHOLD,
    VIDEO_FORMATS_REQUIRING_PREPROCESSING,
    AUDIO_FORMATS_REQUIRING_PREPROCESSING
)

logger = logging.getLogger(__name__)

# 默认音频输出格式
DEFAULT_AUDIO_FORMAT = "wav"


class FFmpegPreprocessingTrigger:
    """ffmpeg preprocessing trigger condition detection"""
    
    @staticmethod
    def should_preprocess(transcription_file) -> Tuple[bool, Optional[str]]:
        """
        Determine if ffmpeg preprocessing is needed

        Args:
            transcription_file: TranscriptionFile instance

        Returns:
            Tuple[bool, Optional[str]]: (whether preprocessing is needed, reason)
        """
        # 1. File size check
        if transcription_file.file_size and transcription_file.file_size > FFMPEG_PREPROCESSING_SIZE_THRESHOLD:
            logger.info(f"File size {transcription_file.file_size} exceeds threshold {FFMPEG_PREPROCESSING_SIZE_THRESHOLD}, preprocessing required")
            return True, "large_file"
        
        # 2. File format check
        if transcription_file.filename:
            file_ext = os.path.splitext(transcription_file.filename)[1].lower()
            if file_ext in VIDEO_FORMATS_REQUIRING_PREPROCESSING:
                logger.info(f"Video format {file_ext} requires preprocessing")
                return True, "video_format"
            elif file_ext in AUDIO_FORMATS_REQUIRING_PREPROCESSING:
                logger.info(f"Complex audio format {file_ext} requires preprocessing")
                return True, "complex_audio_format"

        return False, None


class FFmpegProcessor:
    """ffmpeg audio processor"""
    
    def __init__(self, storage, parent_dir=None):
        """
        Initialize ffmpeg processor

        Args:
            storage: Storage service instance
            parent_dir: Optional parent directory to create temp dir in. If None, uses system temp.
        """
        self.storage = storage
        if parent_dir:
            # 在指定的父目录下创建 ffmpeg 子目录
            self.temp_dir = tempfile.mkdtemp(prefix="ffmpeg_", dir=parent_dir)
            logger.info(f"Created ffmpeg temporary directory in parent: {self.temp_dir}")
        else:
            # 在系统临时目录下创建 ffmpeg 目录
            self.temp_dir = tempfile.mkdtemp(prefix="ffmpeg_")
            logger.info(f"Created ffmpeg temporary directory: {self.temp_dir}")
    
    def extract_audio(self, input_file_path: str, output_format: str = 'wav') -> str:
        """
        Extract audio using ffmpeg

        Args:
            input_file_path: Input file path
            output_format: Output format, default wav

        Returns:
            str: Output file path

        Raises:
            Exception: ffmpeg processing failed
        """
        output_file = os.path.join(
            self.temp_dir, 
            f"extracted_audio.{output_format}"
        )
        
        # ffmpeg command: extract audio, choose encoding based on format
        if output_format.lower() == 'mp3':
            # MP3 format: lossy compression, smaller file size
            cmd = [
                'ffmpeg', '-i', input_file_path,
                '-vn',  # No video
                '-acodec', 'libmp3lame',  # MP3 encoding
                '-b:a', '128k',  # 128kbps bitrate
                '-ar', '16000',  # Sample rate 16kHz
                '-ac', '1',  # Mono
                '-y',  # Overwrite output file
                output_file
            ]
        else:
            # WAV format (default): lossless, best transcription quality
            cmd = [
                'ffmpeg', '-i', input_file_path,
                '-vn',  # No video
                '-acodec', 'pcm_s16le',  # PCM lossless encoding
                '-ar', '16000',  # Sample rate 16kHz
                '-ac', '1',  # Mono
                '-y',  # Overwrite output file
                output_file
            ]
        
        logger.info(f"Executing ffmpeg command: {' '.join(cmd)}")
        
        try:
            start_time = time.time()
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=3600  # 1 hour timeout
            )
            
            processing_time = time.time() - start_time
            logger.info(f"ffmpeg processing time: {processing_time:.2f} seconds")
            
            if result.returncode != 0:
                logger.error(f"ffmpeg stderr: {result.stderr}")
                raise Exception(f"ffmpeg failed with return code {result.returncode}: {result.stderr}")
            
            if not os.path.exists(output_file):
                raise Exception(f"Output file does not exist: {output_file}")
            
            output_size = os.path.getsize(output_file)
            logger.info(f"Audio extraction completed, output file size: {output_size} bytes")
            
            return output_file
            
        except subprocess.TimeoutExpired:
            logger.error("ffmpeg processing timeout")
            raise Exception("ffmpeg processing timeout")
        except Exception as e:
            logger.error(f"ffmpeg processing failed: {e}")
            raise
    
    def get_media_info(self, file_path: str) -> Dict[str, Any]:
        """
        Get media file information

        Args:
            file_path: File path

        Returns:
            Dict[str, Any]: Media information

        Raises:
            Exception: ffprobe execution failed
        """
        cmd = [
            'ffprobe', '-v', 'quiet',
            '-print_format', 'json',
            '-show_format', '-show_streams',
            file_path
        ]
        
        logger.info(f"Executing ffprobe command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                logger.error(f"ffprobe stderr: {result.stderr}")
                raise Exception(f"ffprobe failed with return code {result.returncode}: {result.stderr}")
            
            media_info = json.loads(result.stdout)
            logger.info("Successfully obtained media information")
            return media_info
            
        except subprocess.TimeoutExpired:
            logger.error("ffprobe execution timeout")
            raise Exception("ffprobe timeout")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse ffprobe output: {e}")
            raise Exception(f"Failed to parse ffprobe output: {e}")
        except Exception as e:
            logger.error(f"ffprobe execution failed: {e}")
            raise
    
    def extract_duration_from_media_info(self, media_info: Dict[str, Any]) -> float:
        """
        Extract duration from media information

        Args:
            media_info: Media information returned by ffprobe

        Returns:
            float: Duration (seconds)
        """
        try:
            # Prefer to get duration from format
            if 'format' in media_info and 'duration' in media_info['format']:
                duration = float(media_info['format']['duration'])
                logger.info(f"Duration obtained from format: {duration} seconds")
                return duration
            
            # Get duration from audio stream
            if 'streams' in media_info:
                for stream in media_info['streams']:
                    if stream.get('codec_type') == 'audio' and 'duration' in stream:
                        duration = float(stream['duration'])
                        logger.info(f"Duration obtained from audio stream: {duration} seconds")
                        return duration
            
            logger.warning("Unable to extract duration from media information, returning 0")
            return 0.0
            
        except (ValueError, KeyError) as e:
            logger.error(f"Failed to extract duration: {e}")
            return 0.0
    
    def cleanup(self):
        """Clean up temporary files"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.error(f"Failed to clean up temporary directory: {e}")


def generate_processed_file_key(original_file_key: str, output_format: str = 'wav') -> str:
    """
    Generate storage key for processed file

    Args:
        original_file_key: Original file key
        output_format: Output format, default wav

    Returns:
        str: Processed file key
    """
    # Add _processed suffix to original file key
    name, _ = os.path.splitext(original_file_key)
    return f"{name}_processed.{output_format}"


def check_ffmpeg_availability() -> bool:
    """
    检查 ffmpeg 是否可用
    
    Returns:
        bool: ffmpeg 是否可用
    """
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, timeout=10)
        if result.returncode == 0:
            logger.info("ffmpeg available")
            return True
        else:
            logger.error("ffmpeg not available")   
            return False
    except Exception as e:
        logger.error(f"Failed to check ffmpeg availability: {e}")
        return False
