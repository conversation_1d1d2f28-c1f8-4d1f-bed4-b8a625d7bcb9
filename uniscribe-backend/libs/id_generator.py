import threading
import time


class SnowflakeIdGenerator:
    def __init__(self, datacenter_id, worker_id, epoch=0):
        # 5 bits max value: 31
        self.datacenter_id = datacenter_id
        self.worker_id = worker_id

        # 12 bits max value: 4095
        self.sequence = 0

        self.epoch = epoch

        # bit shift amounts
        self.timestamp_shift = 22
        self.datacenter_shift = 17
        self.worker_shift = 12

        self.last_timestamp = -1
        self.lock = threading.Lock()

    def _get_timestamp(self):
        return int(time.time() * 1000) - self.epoch

    def _wait_next_millis(self, last_timestamp):
        timestamp = self._get_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._get_timestamp()
        return timestamp

    def get_id(self):
        with self.lock:
            timestamp = self._get_timestamp()

            if timestamp < self.last_timestamp:
                raise Exception(
                    f"Clock moved backwards. Refusing to generate id for {self.last_timestamp - timestamp} milliseconds"
                )

            if self.last_timestamp == timestamp:
                self.sequence = (self.sequence + 1) & 0xFFF
                if self.sequence == 0:
                    timestamp = self._wait_next_millis(self.last_timestamp)
            else:
                self.sequence = 0

            self.last_timestamp = timestamp

            return (
                (timestamp << self.timestamp_shift)
                | (self.datacenter_id << self.datacenter_shift)
                | (self.worker_id << self.worker_shift)
                | self.sequence
            )


id_generator = SnowflakeIdGenerator(1, 1)


if __name__ == "__main__":
    ids = []
    for i in range(10000):
        ids.append(id_generator.get_id())
    print(len(ids), len(set(ids)))
    print(ids[:100])
