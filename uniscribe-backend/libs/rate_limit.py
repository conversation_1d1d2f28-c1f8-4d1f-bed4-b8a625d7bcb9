"""
通用的频率限制模块

支持多种限制策略和装饰器模式，可用于API调用、函数执行等场景的频率控制。
"""

import time
import logging
import functools
from typing import Any, Callable, Optional, Union, Dict, List
from abc import ABC, abstractmethod
from libs.cache import RedisCache

logger = logging.getLogger(__name__)


class RateLimitBackend(ABC):
    """频率限制后端抽象基类"""

    @abstractmethod
    def get_requests(self, key: str) -> List[float]:
        """获取请求时间列表"""
        pass

    @abstractmethod
    def set_requests(self, key: str, requests: List[float], ttl: int) -> bool:
        """设置请求时间列表"""
        pass

    @abstractmethod
    def increment_counter(self, key: str, ttl: int) -> int:
        """增加计数器并返回当前值"""
        pass


class RedisRateLimitBackend(RateLimitBackend):
    """基于Redis的频率限制后端"""

    def __init__(self, prefix: str = "rate_limit"):
        self.cache = RedisCache(prefix=prefix)

    def get_requests(self, key: str) -> List[float]:
        """获取请求时间列表"""
        requests = self.cache.get(key)
        return requests if isinstance(requests, list) else []

    def set_requests(self, key: str, requests: List[float], ttl: int) -> bool:
        """设置请求时间列表"""
        return self.cache.set(key, requests, ttl)

    def increment_counter(self, key: str, ttl: int) -> int:
        """增加计数器并返回当前值"""
        try:
            client = self.cache.client
            full_key = self.cache._get_full_key(key)

            # 使用Redis的INCR命令原子性地增加计数器
            count = client.incr(full_key)

            # 如果是第一次设置，设置过期时间
            if count == 1:
                client.expire(full_key, ttl)

            return count
        except Exception as e:
            logger.error(f"Error incrementing counter: {e}")
            return 1


class MemoryRateLimitBackend(RateLimitBackend):
    """基于内存的频率限制后端（用于测试或单机场景）"""

    def __init__(self):
        self._storage: Dict[str, Any] = {}

    def get_requests(self, key: str) -> List[float]:
        """获取请求时间列表"""
        data = self._storage.get(key, {})
        if "expire_time" in data and time.time() > data["expire_time"]:
            # 数据已过期，删除
            self._storage.pop(key, None)
            return []
        return data.get("requests", [])

    def set_requests(self, key: str, requests: List[float], ttl: int) -> bool:
        """设置请求时间列表"""
        try:
            self._storage[key] = {
                "requests": requests,
                "expire_time": time.time() + ttl,
            }
            return True
        except Exception:
            return False

    def increment_counter(self, key: str, ttl: int) -> int:
        """增加计数器并返回当前值"""
        now = time.time()
        data = self._storage.get(key, {})

        # 检查是否过期
        if "expire_time" in data and now > data["expire_time"]:
            data = {}

        count = data.get("count", 0) + 1
        self._storage[key] = {"count": count, "expire_time": now + ttl}
        return count


class RateLimitStrategy(ABC):
    """频率限制策略抽象基类"""

    @abstractmethod
    def is_allowed(
        self, backend: RateLimitBackend, key: str
    ) -> tuple[bool, Optional[float]]:
        """
        检查是否允许请求

        Returns:
            (is_allowed, wait_time): 是否允许和需要等待的时间（秒）
        """
        pass


class SlidingWindowStrategy(RateLimitStrategy):
    """滑动窗口策略"""

    def __init__(self, max_requests: int, window_size: float):
        self.max_requests = max_requests
        self.window_size = window_size

    def is_allowed(
        self, backend: RateLimitBackend, key: str
    ) -> tuple[bool, Optional[float]]:
        """检查滑动窗口内的请求是否超限"""
        now = time.time()

        # 获取最近的请求时间列表
        recent_requests = backend.get_requests(key)

        # 过滤掉超过时间窗口的请求
        valid_requests = [
            req_time
            for req_time in recent_requests
            if now - req_time < self.window_size
        ]

        # 检查是否超过限制
        if len(valid_requests) >= self.max_requests:
            # 计算需要等待的时间
            oldest_request = min(valid_requests)
            wait_time = self.window_size - (now - oldest_request)
            return False, max(0, wait_time)

        # 记录这次请求时间
        valid_requests.append(now)

        # 只保留最近的请求记录，避免列表无限增长
        if len(valid_requests) > self.max_requests:
            valid_requests = valid_requests[-self.max_requests :]

        # 更新缓存
        ttl = max(1, int(self.window_size * 2))  # 确保TTL至少为1秒
        backend.set_requests(key, valid_requests, ttl)

        return True, None


class FixedWindowStrategy(RateLimitStrategy):
    """固定窗口策略"""

    def __init__(self, max_requests: int, window_size: float):
        self.max_requests = max_requests
        self.window_size = window_size

    def is_allowed(
        self, backend: RateLimitBackend, key: str
    ) -> tuple[bool, Optional[float]]:
        """检查固定窗口内的请求是否超限"""
        now = time.time()

        # 计算当前窗口的开始时间
        window_start = int(now // self.window_size) * self.window_size
        window_key = f"{key}:{window_start}"

        # 获取当前窗口的请求计数
        count = backend.increment_counter(window_key, int(self.window_size))

        if count > self.max_requests:
            # 计算到下一个窗口的等待时间
            next_window = window_start + self.window_size
            wait_time = next_window - now
            return False, wait_time

        return True, None


class RateLimiter:
    """通用频率限制器"""

    def __init__(
        self,
        strategy: RateLimitStrategy,
        backend: Optional[RateLimitBackend] = None,
        key_prefix: str = "default",
    ):
        self.strategy = strategy
        self.backend = backend or RedisRateLimitBackend()
        self.key_prefix = key_prefix

    def _get_key(self, identifier: str = "global") -> str:
        """生成缓存键"""
        return f"{self.key_prefix}:{identifier}"

    def is_allowed(self, identifier: str = "global") -> tuple[bool, Optional[float]]:
        """检查是否允许请求"""
        key = self._get_key(identifier)
        return self.strategy.is_allowed(self.backend, key)

    def wait_if_needed(self, identifier: str = "global") -> bool:
        """如果需要则等待，返回是否等待了"""
        allowed, wait_time = self.is_allowed(identifier)

        if not allowed and wait_time and wait_time > 0:
            logger.info(
                f"Rate limit reached for {identifier}, waiting {wait_time:.3f} seconds"
            )
            time.sleep(wait_time)
            return True

        return False


def rate_limit(
    max_requests: int,
    window_size: float,
    strategy: str = "sliding_window",
    key_prefix: str = "rate_limit",
    backend: Optional[RateLimitBackend] = None,
    identifier_func: Optional[Callable[..., str]] = None,
    wait: bool = True,
):
    """
    频率限制装饰器

    Args:
        max_requests: 最大请求数
        window_size: 时间窗口大小（秒）
        strategy: 限制策略 ("sliding_window" 或 "fixed_window")
        key_prefix: 缓存键前缀
        backend: 后端存储，默认使用Redis
        identifier_func: 用于生成标识符的函数，接收被装饰函数的参数
        wait: 是否等待，False时超限直接抛出异常
    """

    def decorator(func: Callable) -> Callable:
        # 创建策略
        if strategy == "sliding_window":
            rate_strategy = SlidingWindowStrategy(max_requests, window_size)
        elif strategy == "fixed_window":
            rate_strategy = FixedWindowStrategy(max_requests, window_size)
        else:
            raise ValueError(f"Unknown strategy: {strategy}")

        # 创建限制器
        limiter = RateLimiter(rate_strategy, backend, key_prefix)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成标识符
            if identifier_func:
                identifier = identifier_func(*args, **kwargs)
            else:
                identifier = func.__name__

            if wait:
                # 等待模式：如果超限则等待
                limiter.wait_if_needed(identifier)
            else:
                # 非等待模式：如果超限则抛出异常
                allowed, wait_time = limiter.is_allowed(identifier)
                if not allowed:
                    raise RateLimitExceeded(
                        f"Rate limit exceeded for {identifier}. "
                        f"Try again in {wait_time:.3f} seconds."
                    )

            return func(*args, **kwargs)

        return wrapper

    return decorator


class RateLimitExceeded(Exception):
    """频率限制超出异常"""

    pass
