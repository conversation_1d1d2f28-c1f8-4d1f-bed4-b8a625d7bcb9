"""
Unified API response utilities for OpenAPI endpoints
"""

from datetime import datetime
from typing import Any, Optional, Dict


def success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """
    Create a standardized success response
    
    Args:
        data: Response data
        message: Success message
        
    Returns:
        Dict containing success response format
    """
    return {
        "success": True,
        "data": data,
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }


def error_response(message: str, code: Optional[str] = None, details: Any = None) -> Dict[str, Any]:
    """
    Create a standardized error response
    
    Args:
        message: Error message
        code: Error code
        details: Additional error details
        
    Returns:
        Dict containing error response format
    """
    error_data = {
        "message": message
    }
    
    if code:
        error_data["code"] = code
        
    if details:
        error_data["details"] = details
    
    return {
        "success": False,
        "error": error_data,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }


def paginated_response(items: list, has_more: bool = False, next_cursor: Optional[str] = None, 
                      total: Optional[int] = None) -> Dict[str, Any]:
    """
    Create a standardized paginated response
    
    Args:
        items: List of items
        has_more: Whether there are more items
        next_cursor: Cursor for next page
        total: Total count (optional)
        
    Returns:
        Dict containing paginated response format
    """
    data = {
        "items": items,
        "has_more": has_more
    }
    
    if next_cursor:
        data["next_cursor"] = next_cursor
        
    if total is not None:
        data["total"] = total
    
    return success_response(data)
