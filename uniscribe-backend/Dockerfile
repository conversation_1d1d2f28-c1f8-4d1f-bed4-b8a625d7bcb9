# ===== Stage 1: Base system with all system dependencies =====
FROM python:3.12.3 AS base-system

ARG http_proxy
ARG https_proxy
ARG all_proxy

# Set environment variables from build args
ENV http_proxy=$http_proxy \
    https_proxy=$https_proxy \
    all_proxy=$all_proxy

# Set timezone to UTC to match production
RUN ln -sf /usr/share/zoneinfo/UTC /etc/localtime

# Configure apt to use faster mirrors for Chinese users
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources

# Install system dependencies including ffmpeg
RUN apt-get update --fix-missing && \
    apt-get install -y --no-install-recommends \
        curl \
        wget \
        ca-certificates \
        gnupg \
        lsb-release && \
    # Add multimedia repository for ffmpeg
    echo "deb http://deb.debian.org/debian $(lsb_release -cs) main non-free" >> /etc/apt/sources.list && \
    echo "deb http://deb.debian.org/debian $(lsb_release -cs)-updates main non-free" >> /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends ffmpeg && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy and run WeasyPrint + CJK fonts installation script
COPY install_weasyprint.sh /tmp/install_weasyprint.sh
RUN chmod +x /tmp/install_weasyprint.sh && \
    /tmp/install_weasyprint.sh && \
    rm /tmp/install_weasyprint.sh

# ===== Stage 2: Application with Python dependencies =====
FROM base-system AS app

WORKDIR /app

# For Chinese users to accelerate the installation of Python packages
ENV PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple/ \
    PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install Python dependencies
COPY requirements.txt .

# === 网络和代理诊断 ===
# 1. 检查环境变量
RUN echo "=== Environment Variables ===" && \
    echo "http_proxy: $http_proxy" && \
    echo "https_proxy: $https_proxy" && \
    echo "all_proxy: $all_proxy" && \
    env | grep -i proxy || echo "No proxy env vars found" && \
    echo "PIP_INDEX_URL: $PIP_INDEX_URL" && \
    echo "PIP_TRUSTED_HOST: $PIP_TRUSTED_HOST"

# 2. 安装网络工具并测试基本连接
RUN apt-get update && apt-get install -y curl && \
    echo "=== Basic Network Test ===" && \
    curl -I --connect-timeout 10 https://pypi.tuna.tsinghua.edu.cn/simple/ || echo "Tsinghua mirror failed" && \
    curl -I --connect-timeout 10 https://pypi.org/simple/ || echo "PyPI official failed"

# 3. 测试 pip 连接
RUN echo "=== PIP Connectivity Test ===" && \
    pip --version && \
    pip search --help || echo "pip search not available" && \
    timeout 30 pip install --dry-run --no-deps requests || echo "pip dry-run failed"

# 4. Install uv for faster package installation
RUN echo "=== Installing UV ===" && \
    pip install --no-cache-dir uv && \
    echo "UV installed successfully, version:" && \
    uv --version

# 5. Install dependencies using uv (much faster than pip)
RUN echo "=== Installing dependencies with UV ===" && \
    uv pip install --system -r requirements.txt && \
    uv pip install --system gunicorn && \
    echo "=== Dependencies installed successfully with UV ==="

COPY . .

# Verify basic installations (WeasyPrint verification is already done in install script)
# RUN ffmpeg -version

EXPOSE 8000

CMD ["gunicorn", "--worker-class", "gevent", "--workers", "4", "--timeout", "30", "--bind", "0.0.0.0:8000", "app:app"]
