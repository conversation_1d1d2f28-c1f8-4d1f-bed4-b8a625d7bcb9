#!/bin/bash

# WeasyPrint and CJK Fonts Installation Script
# This script installs WeasyPrint, its system dependencies, and comprehensive CJK fonts

echo "Starting WeasyPrint and CJK fonts installation..."

# Detect operating system
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "Detected Linux system"

    # Ubuntu/Debian
    if command -v apt-get &> /dev/null; then
        echo "Installing Ubuntu/Debian dependencies..."
        apt-get update

        # Try to install WeasyPrint directly from system packages (Ubuntu 24+)
        if apt-cache show weasyprint &> /dev/null; then
            echo "Installing WeasyPrint from system packages..."
            apt-get install -y \
                weasyprint \
                fonts-noto \
                fonts-noto-cjk \
                fonts-noto-color-emoji
            echo "WeasyPrint installed from system packages!"

            # Install additional CJK fonts for better coverage
            echo "Installing comprehensive CJK fonts..."
            apt-get install -y \
                fonts-noto-cjk-extra \
                fonts-wqy-zenhei \
                fonts-wqy-microhei \
                fonts-arphic-ukai \
                fonts-arphic-uming \
                fonts-unfonts-core \
                fonts-takao \
                fonts-ipafont \
                fonts-ipaexfont \
                fonts-nanum \
                fonts-nanum-coding \
                fonts-nanum-extra

            # Refresh font cache
            echo "Refreshing font cache..."
            fc-cache -fv

            # Skip pip installation since system package is available
            SKIP_PIP_INSTALL=true
        else
            echo "WeasyPrint not available in system packages, installing dependencies for pip installation..."
            apt-get install -y \
                python3-dev \
                python3-pip \
                python3-cffi \
                python3-brotli \
                libpango-1.0-0 \
                libpangoft2-1.0-0 \
                libfontconfig1 \
                libcairo2 \
                libgdk-pixbuf2.0-0 \
                libffi-dev \
                shared-mime-info

            # Install CJK fonts
            echo "Installing CJK fonts..."
            apt-get install -y \
                fonts-noto \
                fonts-noto-cjk \
                fonts-noto-cjk-extra \
                fonts-noto-color-emoji \
                fonts-wqy-zenhei \
                fonts-wqy-microhei \
                fonts-arphic-ukai \
                fonts-arphic-uming \
                fonts-unfonts-core \
                fonts-takao \
                fonts-ipafont \
                fonts-ipaexfont \
                fonts-nanum \
                fonts-nanum-coding \
                fonts-nanum-extra

            # Refresh font cache
            echo "Refreshing font cache..."
            fc-cache -fv
        fi
    
    # CentOS/RHEL/Fedora
    elif command -v yum &> /dev/null; then
        echo "Installing CentOS/RHEL dependencies..."
        yum install -y \
            python3-devel \
            python3-pip \
            python3-cffi \
            pango \
            fontconfig \
            cairo \
            gdk-pixbuf2 \
            libffi-devel

        # Install CJK fonts for CentOS/RHEL
        echo "Installing CJK fonts for CentOS/RHEL..."
        yum install -y \
            google-noto-cjk-fonts \
            wqy-zenhei-fonts \
            wqy-microhei-fonts \
            takao-fonts \
            ipa-gothic-fonts \
            ipa-mincho-fonts

    # Alpine Linux (commonly used in Docker)
    elif command -v apk &> /dev/null; then
        echo "Installing Alpine Linux dependencies..."
        apk add --no-cache \
            python3-dev \
            py3-pip \
            py3-cffi \
            py3-brotli \
            pango \
            fontconfig \
            cairo \
            gdk-pixbuf \
            libffi-dev \
            jpeg-dev \
            openjpeg-dev \
            zlib-dev

        # Install CJK fonts for Alpine
        echo "Installing CJK fonts for Alpine..."
        apk add --no-cache \
            font-noto-cjk \
            font-wqy-zenhei
    fi

elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Detected macOS system"

    # Check if Homebrew is installed
    if command -v brew &> /dev/null; then
        echo "Installing dependencies using Homebrew..."
        brew install \
            pango \
            cairo \
            gdk-pixbuf \
            libffi

        # Install CJK fonts for macOS
        echo "Installing CJK fonts for macOS..."
        brew install --cask \
            font-noto-sans-cjk \
            font-noto-serif-cjk
    else
        echo "Please install Homebrew first: https://brew.sh/"
        exit 1
    fi

else
    echo "Unsupported operating system: $OSTYPE"
    echo "Please manually install WeasyPrint dependencies and CJK fonts"
fi

# Install Python package (skip if system package was installed)
if [[ "$SKIP_PIP_INSTALL" != "true" ]]; then
    echo "Installing WeasyPrint Python package..."
    pip install weasyprint==62.3
fi

# Verify installation
echo "Verifying WeasyPrint installation..."
python3 -c "
try:
    from weasyprint import HTML, CSS
    from weasyprint.text.fonts import FontConfiguration
    print('✅ WeasyPrint installation successful!')

    # Test basic functionality with CJK characters
    html_content = '''
    <html>
    <head>
        <meta charset=\"utf-8\">
        <style>
            body {
                font-family:
                    'Noto Sans CJK SC',
                    'Noto Sans CJK JP',
                    'Noto Sans CJK KR',
                    'WenQuanYi Zen Hei',
                    'Takao PGothic',
                    'Nanum Gothic',
                    sans-serif;
            }
        </style>
    </head>
    <body>
        <h1>Multilingual Test</h1>
        <p>English: Hello World</p>
        <p>Chinese: 你好世界</p>
        <p>Japanese: こんにちは</p>
        <p>Korean: 안녕하세요</p>
    </body>
    </html>
    '''

    font_config = FontConfiguration()
    html = HTML(string=html_content)
    css = CSS(string='', font_config=font_config)
    pdf = html.write_pdf(stylesheets=[css], font_config=font_config)
    print(f'✅ Multilingual PDF generation test successful! Size: {len(pdf)} bytes')

except ImportError as e:
    print(f'❌ WeasyPrint import failed: {e}')
    exit(1)
except Exception as e:
    print(f'❌ WeasyPrint test failed: {e}')
    exit(1)
"

# Check available CJK fonts
echo "Checking available CJK fonts..."
python3 -c "
import subprocess
import sys

def check_font(font_name):
    try:
        result = subprocess.run(['fc-list', ':', 'family'], capture_output=True, text=True)
        if font_name.lower() in result.stdout.lower():
            print(f'✅ {font_name} is available')
            return True
        else:
            print(f'❌ {font_name} is NOT available')
            return False
    except Exception as e:
        print(f'❌ Error checking {font_name}: {e}')
        return False

fonts_to_check = [
    'Noto Sans CJK',
    'Noto Serif CJK',
    'WenQuanYi',
    'Takao',
    'Nanum'
]

available_count = 0
for font in fonts_to_check:
    if check_font(font):
        available_count += 1

print(f'\\nSummary: {available_count}/{len(fonts_to_check)} CJK fonts are available')

if available_count > 0:
    print('🎉 CJK fonts are installed! WeasyPrint should support Chinese, Japanese, and Korean.')
else:
    print('⚠️  No CJK fonts found. Multilingual PDF may not display correctly.')
"

echo ""
echo "🎉 WeasyPrint and CJK fonts installation completed!"
echo "You can now generate multilingual PDFs with proper CJK character support."
