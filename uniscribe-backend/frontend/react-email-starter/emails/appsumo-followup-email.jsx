import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
  Img,
  Hr,
  Link,
  Section,
} from "@react-email/components";
import * as React from "react";

export const AppSumoFollowupEmail = ({
  firstName = "AppSumo User",
}) => (
  <Html>
    <Head />
    <Preview>How's it going with UniScribe?</Preview>
    <Body style={main}>
      <Container style={container}>
        {/* Header with Logo and Title */}
        <Section style={header}>
          <Img
            src="https://www.uniscribe.co/uniscribe-logo-square-480px.png"
            width="80"
            height="80"
            alt="AppSumo Logo"
            style={logo}
          />
          <Heading style={heroTitle}>UniScribe Check-in</Heading>
        </Section>

        {/* Hero Section */}
        <Section style={heroSection}>
          <Text style={greeting}>Hi {firstName},</Text>
          <Text style={mainText}>
            We just wanted to check in and see how your experience with UniScribe has been so far. We hope you're finding it helpful and making the most of its features!
          </Text>
          <Text style={mainText}>
            If you've been enjoying UniScribe, we'd love for you to share your thoughts with the AppSumo community. Your honest feedback helps us grow and improve while also guiding other Sumolings.
          </Text>
        </Section>

        {/* Main CTA */}
        <Section style={ctaSection}>
          <Button style={primaryButton} href="https://appsumo.com/products/uniscribe/#reviews">
            Leave a Review on AppSumo
          </Button>
        </Section>

        {/* Support Section */}
        <Section style={supportSection}>
          <Text style={supportText}>
            And of course, if you have any questions or need assistance, we're always here to help—just reply to this email!
          </Text>
          <Text style={supportText}>
            Thanks again for being part of our journey!
          </Text>
        </Section>

        {/* Footer */}
        <Hr style={hr} />
        <Section style={footer}>
          <Text style={signatureText}>Best regards,</Text>
          <Text style={signatureText}>David Chen</Text>
          <Text style={signatureText}>UniScribe</Text>
          <Text style={signatureText}>
            <a href="mailto:<EMAIL>" style={link}><EMAIL></a>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#f5f7fa",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0",
  maxWidth: "600px",
};

const header = {
  backgroundColor: "#6366F1",
  padding: "20px 0",
  textAlign: "center",
  borderRadius: "8px 8px 0 0",
};

const logo = {
  margin: "0 auto 15px",
  display: "block",
};

const heroTitle = {
  color: "#ffffff",
  fontSize: "22px",
  fontWeight: "bold",
  margin: "0",
  textAlign: "center",
};

const heroSection = {
  backgroundColor: "#ffffff",
  padding: "30px",
  borderBottom: "1px solid #E5E5E5",
};

const greeting = {
  color: "#111111",
  fontSize: "18px",
  fontWeight: "500",
  margin: "0 0 20px",
  lineHeight: "1.5",
};

const mainText = {
  color: "#111111",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 16px",
};

const ctaSection = {
  backgroundColor: "#ffffff",
  padding: "20px 30px 30px",
  textAlign: "center",
  borderBottom: "1px solid #E5E5E5",
};

const supportSection = {
  backgroundColor: "#ffffff",
  padding: "30px",
  borderBottom: "1px solid #E5E5E5",
};

const supportText = {
  color: "#111111",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 16px",
};

const footer = {
  backgroundColor: "#ffffff",
  padding: "30px",
  borderRadius: "0 0 8px 8px",
};

const signatureText = {
  color: "#666666",
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0 0 8px",
};

const primaryButton = {
  backgroundColor: "#6366F1",
  borderRadius: "6px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "500",
  textDecoration: "none",
  textAlign: "center",
  display: "inline-block",
  padding: "12px 24px",
  margin: "10px 0",
};

const hr = {
  borderColor: "#E5E5E5",
  margin: "0",
};

const link = {
  color: "#6366F1",
  textDecoration: "underline",
};

export default AppSumoFollowupEmail;
