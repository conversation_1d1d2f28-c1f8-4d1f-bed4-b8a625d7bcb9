import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
  Img,
  Hr,
  Section,
} from "@react-email/components";
import * as React from "react";

export const AppSumoOnboardingEmail = ({
  firstName = "AppSumo User",
}) => (
  <Html>
    <Head />
    <Preview>Welcome to UniScribe – Let's Get Started! 🚀</Preview>
    <Body style={main}>
      <Container style={container}>
        {/* Header with Logo and Title */}
        <Section style={header}>
          <Img
            src="https://www.uniscribe.co/uniscribe-logo-square-480px.png"
            width="80"
            height="80"
            alt="AppSumo Logo"
            style={logo}
          />
          <Heading style={heroTitle}>Welcome to UniScribe!</Heading>
        </Section>

        {/* Hero Section */}
        <Section style={heroSection}>
          <Text style={greeting}>Hi {firstName},</Text>
          <Text style={heroText}>
            Thank you so much for purchasing UniScribe on AppSumo! 🎉
          </Text>
          <Text style={mainText}>
            We're absolutely thrilled to have you on board and can't wait for you to experience all the ways our tool can help you transform your audio and video content into accurate transcriptions.
          </Text>
        </Section>

        {/* Main CTA */}
        <Section style={ctaSection}>
          <Button style={primaryButton} href="https://www.uniscribe.co/dashboard?utm_source=email&utm_medium=appsumo-onboarding">
            Get Started Now
          </Button>
        </Section>

        {/* Feedback Section */}
        <Section style={feedbackSection}>
          <Text style={feedbackText}>
            As a growing company, your feedback means everything to us! Once you've had a chance to explore UniScribe, we'd love to hear your honest thoughts in a review on AppSumo.
          </Text>
          <Button style={secondaryButton} href="https://appsumo.com/products/uniscribe/#reviews">
            Leave a Review on AppSumo
          </Button>
        </Section>

        {/* Footer */}
        <Hr style={hr} />
        <Section style={footer}>
          <Text style={footerText}>
            Thank you again for your support—we're excited to see what you create! Let us know if we can help in any way.
          </Text>
          <Text style={signatureText}>Best regards,</Text>
          <Text style={signatureText}>David Chen</Text>
          <Text style={signatureText}>UniScribe</Text>
          <Text style={signatureText}>
            <a href="mailto:<EMAIL>" style={link}><EMAIL></a>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#f5f7fa",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0",
  maxWidth: "600px",
};

const header = {
  backgroundColor: "#6366F1",
  padding: "20px 0",
  textAlign: "center",
  borderRadius: "8px 8px 0 0",
};

const logo = {
  margin: "0 auto 15px",
  display: "block",
};

const heroTitle = {
  color: "#ffffff",
  fontSize: "22px",
  fontWeight: "bold",
  margin: "0",
  textAlign: "center",
};

const heroSection = {
  backgroundColor: "#ffffff",
  padding: "30px",
  borderBottom: "1px solid #E5E5E5",
};

const greeting = {
  color: "#111111",
  fontSize: "18px",
  fontWeight: "500",
  margin: "0 0 20px",
  lineHeight: "1.5",
};



const heroText = {
  color: "#111111",
  fontSize: "18px",
  fontWeight: "500",
  lineHeight: "1.5",
  margin: "0 0 16px",
};

const mainText = {
  color: "#111111",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 16px",
};

const ctaSection = {
  backgroundColor: "#ffffff",
  padding: "20px 30px 30px",
  textAlign: "center",
  borderBottom: "1px solid #E5E5E5",
};

const feedbackSection = {
  backgroundColor: "#ffffff",
  padding: "30px",
  textAlign: "center",
  borderBottom: "1px solid #E5E5E5",
};

const feedbackText = {
  color: "#111111",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 20px",
};

const footer = {
  backgroundColor: "#ffffff",
  padding: "30px",
  borderRadius: "0 0 8px 8px",
};

const footerText = {
  color: "#111111",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 20px",
};

const signatureText = {
  color: "#666666",
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0 0 8px",
};

const primaryButton = {
  backgroundColor: "#6366F1",
  borderRadius: "6px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "500",
  textDecoration: "none",
  textAlign: "center",
  display: "inline-block",
  padding: "12px 24px",
  margin: "10px 0",
};

const secondaryButton = {
  backgroundColor: "#ffffff",
  borderRadius: "6px",
  border: "1px solid #6366F1",
  color: "#6366F1",
  fontSize: "16px",
  fontWeight: "500",
  textDecoration: "none",
  textAlign: "center",
  display: "inline-block",
  padding: "12px 24px",
  margin: "10px 0",
};

const hr = {
  borderColor: "#E5E5E5",
  margin: "0",
};

const link = {
  color: "#6366F1",
  textDecoration: "underline",
};

export default AppSumoOnboardingEmail;
