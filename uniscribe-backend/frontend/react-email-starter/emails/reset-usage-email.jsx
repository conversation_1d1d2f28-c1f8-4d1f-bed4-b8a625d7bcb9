import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Html,
  Preview,
  Text,
  Img,
  Hr,
} from "@react-email/components";
import * as React from "react";

export const ResetUsageEmail = ({
  fullName = "User",
  availableMinutes = 60,
}) => (
  <Html>
    <Head />
    <Preview>Hi {fullName}, your transcription minutes have been reset!</Preview>
    <Body style={main}>
      <Container style={container}>
        <Img
          src="https://pub-93b2caf408384907974c14b3f3746ea9.r2.dev/icon.png"
          width="80"
          height="80"
          alt="Uniscribe Logo"
          style={logo}
        />
        <Heading style={h1}>Hi {fullName},</Heading>
        <Text style={mainText}>
          Great news! Your transcription minutes have been reset. You now have{" "}
          <strong>{availableMinutes} minutes</strong> of transcription time available.
        </Text>
        <Text style={mainText}>
          Ready to start transcribing? Click the button below to access your dashboard.
        </Text>
        <Button style={button} href="https://www.uniscribe.co/dashboard?utm_source=email&utm_medium=reset-usage">
          Go to Dashboard
        </Button>
        <Text style={mainText}>Enjoy your transcriptions!</Text>
        <Text style={grayText}>
          If you have any questions or need assistance, please don't hesitate to reach out to us at{" "}
          <a href="mailto:<EMAIL>" style={link}><EMAIL></a>
        </Text>
        <Hr style={hr} />
        <Text style={grayText}>Best regards,</Text>
        <Text style={grayText}>The UniScribe Team</Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "40px 20px",
  maxWidth: "580px",
};

const logo = {
  margin: "0 auto 40px",
  display: "block",
};

const h1 = {
  color: "#111111",
  fontSize: "16px",
  fontWeight: "bold",
  margin: "0 0 20px",
};

const mainText = {
  color: "#111111",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 16px",
};

const grayText = {
  color: "#666666",
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "0 0 12px",
};

const button = {
  backgroundColor: "#6366F1",
  borderRadius: "6px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "500",
  textDecoration: "none",
  textAlign: "center",
  display: "block",
  padding: "12px 24px",
  margin: "24px auto 32px",
  width: "fit-content",
};

const hr = {
  borderColor: "#E5E5E5",
  margin: "32px 0 24px",
};

const link = {
  color: "#6366F1",
  textDecoration: "underline",
};

export default ResetUsageEmail; 