#!/bin/bash

# Development setup script for fast dependency management
# This script helps you quickly add new dependencies without rebuilding the entire Docker image

set -e

CONTAINER_NAME="uniscribe-dev"
BASE_IMAGE_NAME="uniscribe-base"
DEV_IMAGE_NAME="uniscribe-dev-image"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${GREEN}[STEP]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to build base image (only needed once or when system deps change)
build_base() {
    print_step "Building base system image (this may take a while)..."

    # Check if proxy environment variables are set and pass them as build args
    BUILD_ARGS=""
    if [ ! -z "$http_proxy" ]; then
        BUILD_ARGS="$BUILD_ARGS --build-arg http_proxy=$http_proxy"
        print_step "Using http_proxy: $http_proxy"
    fi
    if [ ! -z "$https_proxy" ]; then
        BUILD_ARGS="$BUILD_ARGS --build-arg https_proxy=$https_proxy"
        print_step "Using https_proxy: $https_proxy"
    fi
    if [ ! -z "$all_proxy" ]; then
        BUILD_ARGS="$BUILD_ARGS --build-arg all_proxy=$all_proxy"
        print_step "Using all_proxy: $all_proxy"
    fi

    docker build --target base-system $BUILD_ARGS -t $BASE_IMAGE_NAME .
    print_step "Base image built successfully!"
}

# Function to build development image (full app)
build_dev() {
    print_step "Building development image (full app)..."

    # Check if proxy environment variables are set and pass them as build args
    BUILD_ARGS=""
    if [ ! -z "$http_proxy" ]; then
        BUILD_ARGS="$BUILD_ARGS --build-arg http_proxy=$http_proxy"
        print_step "Using http_proxy: $http_proxy"
    fi
    if [ ! -z "$https_proxy" ]; then
        BUILD_ARGS="$BUILD_ARGS --build-arg https_proxy=$https_proxy"
        print_step "Using https_proxy: $https_proxy"
    fi
    if [ ! -z "$all_proxy" ]; then
        BUILD_ARGS="$BUILD_ARGS --build-arg all_proxy=$all_proxy"
        print_step "Using all_proxy: $all_proxy"
    fi

    docker build $BUILD_ARGS -t $DEV_IMAGE_NAME .
    print_step "Development image built successfully!"
}

# Function to add a new pip dependency
add_dependency() {
    local package=$1
    if [ -z "$package" ]; then
        print_error "Please specify a package name"
        echo "Usage: $0 add <package_name>"
        exit 1
    fi
    
    print_step "Adding dependency: $package"
    
    # Add to requirements.txt
    echo "$package" >> requirements.txt
    print_step "Added $package to requirements.txt"
    
    # Rebuild dev image
    build_dev
    
    # Restart container if it's running
    if docker ps | grep -q $CONTAINER_NAME; then
        print_step "Restarting container..."
        docker stop $CONTAINER_NAME
        docker rm $CONTAINER_NAME
        start_container
    fi
}

# Function to start development container
start_container() {
    print_step "Starting development container..."
    docker run -d \
        --name $CONTAINER_NAME \
        -p 8000:8000 \
        -v $(pwd):/app \
        --restart unless-stopped \
        $DEV_IMAGE_NAME
    
    print_step "Container started! Access your app at http://localhost:8000"
    print_step "To view logs: docker logs -f $CONTAINER_NAME"
    print_step "To exec into container: docker exec -it $CONTAINER_NAME bash"
}

# Function to install dependency in running container (fastest method)
install_in_container() {
    local package=$1
    if [ -z "$package" ]; then
        print_error "Please specify a package name"
        echo "Usage: $0 install <package_name>"
        exit 1
    fi
    
    if ! docker ps | grep -q $CONTAINER_NAME; then
        print_error "Container $CONTAINER_NAME is not running. Start it first with: $0 start"
        exit 1
    fi
    
    print_step "Installing $package in running container..."
    docker exec $CONTAINER_NAME uv pip install --system $package
    
    # Add to requirements.txt for persistence
    echo "$package" >> requirements.txt
    print_step "Added $package to requirements.txt for persistence"
    
    print_step "Package installed! Container will use it immediately."
}

# Function to show container logs
logs() {
    docker logs -f $CONTAINER_NAME
}

# Function to exec into container
shell() {
    docker exec -it $CONTAINER_NAME bash
}

# Function to stop and remove container
stop() {
    print_step "Stopping container..."
    docker stop $CONTAINER_NAME 2>/dev/null || true
    docker rm $CONTAINER_NAME 2>/dev/null || true
    print_step "Container stopped and removed"
}

# Function to show status
status() {
    echo "=== Docker Images ==="
    docker images | grep -E "(uniscribe|REPOSITORY)" || echo "No uniscribe images found"
    echo ""
    echo "=== Running Containers ==="
    docker ps | grep -E "(uniscribe|CONTAINER)" || echo "No uniscribe containers running"
    echo ""
    echo "=== Docker Compose Status ==="
    if [ -f "docker-compose-holys.yml" ]; then
        echo "--- docker-compose-holys.yml ---"
        docker-compose -f docker-compose-holys.yml ps
    fi
    if [ -f "docker-compose.yml" ]; then
        echo "--- docker-compose.yml ---"
        docker-compose ps
    fi
}

# Function to start with docker-compose (holys version)
compose_up() {
    print_step "Starting services with docker-compose-holys.yml..."
    docker-compose -f docker-compose-holys.yml up -d
    print_step "Services started! Check status with: $0 status"
}

# Function to stop docker-compose services (holys version)
compose_down() {
    print_step "Stopping docker-compose-holys.yml services..."
    docker-compose -f docker-compose-holys.yml down
    print_step "Services stopped"
}

# Function to start with standard docker-compose
compose_up_standard() {
    print_step "Starting services with docker-compose.yml..."
    docker-compose up -d
    print_step "Services started! Check status with: $0 status"
}

# Function to stop standard docker-compose services
compose_down_standard() {
    print_step "Stopping docker-compose.yml services..."
    docker-compose down
    print_step "Services stopped"
}

# Main script logic
case "$1" in
    "build-base")
        build_base
        ;;
    "build-dev")
        build_dev
        ;;
    "add")
        add_dependency "$2"
        ;;
    "install")
        install_in_container "$2"
        ;;
    "start")
        start_container
        ;;
    "stop")
        stop
        ;;
    "restart")
        stop
        start_container
        ;;
    "logs")
        logs
        ;;
    "shell")
        shell
        ;;
    "status")
        status
        ;;
    "compose-up")
        compose_up
        ;;
    "compose-down")
        compose_down
        ;;
    "compose-up-std")
        compose_up_standard
        ;;
    "compose-down-std")
        compose_down_standard
        ;;
    "help"|"")
        echo "Development setup script for Uniscribe Backend"
        echo ""
        echo "Usage: $0 <command> [arguments]"
        echo ""
        echo "Commands:"
        echo "  build-base          Build the base system image (run once)"
        echo "  build-dev           Build the full development image"
        echo "  add <package>       Add a new pip dependency and rebuild"
        echo "  install <package>   Install dependency in running container (fastest)"
        echo "  start               Start the development container"
        echo "  stop                Stop and remove the container"
        echo "  restart             Restart the container"
        echo "  logs                Show container logs"
        echo "  shell               Open a shell in the container"
        echo "  status              Show images and container status"
        echo "  compose-up          Start all services with docker-compose-holys.yml"
        echo "  compose-down        Stop all docker-compose-holys.yml services"
        echo "  compose-up-std      Start all services with docker-compose.yml"
        echo "  compose-down-std    Stop all docker-compose.yml services"
        echo "  help                Show this help message"
        echo ""
        echo "Quick start (single container):"
        echo "  1. $0 build-base    # Build base image (only once)"
        echo "  2. $0 build-dev     # Build full app image"
        echo "  3. $0 start         # Start container"
        echo "  4. $0 install <pkg> # Add new dependencies quickly"
        echo ""
        echo "Quick start (full stack with docker-compose):"
        echo "  1. $0 build-base    # Build base image (only once)"
        echo "  2. $0 build-dev     # Build full app image"
        echo "  3. $0 compose-up    # Start all services (app + mysql + redis)"
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac
