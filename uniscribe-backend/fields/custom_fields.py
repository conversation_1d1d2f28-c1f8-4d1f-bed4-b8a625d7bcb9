from flask_restful import fields
from datetime import datetime
from constants.task import TaskStatus, TaskType


class ISO8601DateTime(fields.Raw):
    """
    自定义字段类，将 datetime 对象格式化为 ISO 8601 格式的字符串
    
    ISO 8601 格式示例: 2023-04-15T08:30:45.123Z
    """
    def format(self, value):
        if value is None:
            return None
        
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            except (ValueError, TypeError):
                return value
                
        if isinstance(value, datetime):
            # 转换为 ISO 8601 格式，包含毫秒并使用 Z 表示 UTC
            return value.strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
            
        return str(value)


class TaskStatusField(fields.Raw):
    """
    自定义字段类，将任务状态 ID 转换为状态名称

    例如: 1 -> "pending", 2 -> "processing"
    """
    def format(self, value):
        if value is None:
            return None

        try:
            return TaskStatus.by_id(value).name
        except ValueError:
            return "unknown"


class TaskTypeField(fields.Raw):
    """
    自定义字段类，将任务类型 ID 转换为类型名称

    例如: 1 -> "transcription", 3 -> "summary"
    """
    def format(self, value):
        if value is None:
            return None

        try:
            return TaskType.by_id(value).name
        except ValueError:
            return "unknown"
