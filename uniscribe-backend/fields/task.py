from flask_restful import fields
from fields.custom_fields import ISO8601DateTime, TaskStatusField, TaskTypeField
from constants.task import MAX_RETRY_COUNT

# 任务状态查询的字段定义
task_status_fields = {
    "taskId": fields.String(attribute="id"),
    "status": TaskStatusField(attribute="status"),
    "error": fields.String(attribute="error_message"),
    "startedTime": ISO8601DateTime(attribute="started_time"),
    "updatedTime": ISO8601DateTime(attribute="updated_time"),
    "completedTime": ISO8601DateTime(attribute="completed_time"),
    "retryCount": fields.Integer(attribute="retry_count"),
    "maxRetryCount": fields.Integer(default=MAX_RETRY_COUNT),
    "taskType": TaskTypeField(attribute="task_type"),
}
