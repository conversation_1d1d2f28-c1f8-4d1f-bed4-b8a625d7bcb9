from flask_restful import fields

usage_fields = {
    "userId": fields.String(attribute="user_id"),
    "transcriptionMinutesUsed": fields.Integer(attribute="transcription_minutes_used"),
    "transcriptionMinutesQuota": fields.Integer(
        attribute="transcription_minutes_quota"
    ),
    "transcriptionRemainingMinutes": fields.Integer(
        attribute="transcription_remaining_minutes"
    ),
    "lastResetTime": fields.String(attribute="last_reset_time"),
    "nextResetTime": fields.String(attribute="next_reset_time"),
    "createdTime": fields.String(attribute="created_time"),
    "updatedTime": fields.String(attribute="updated_time"),
}

# 用户限制查询字段
user_limits_fields = {
    "transcriptionMinutesRemaining": fields.Integer(attribute="transcription_minutes_remaining"),
    "dailyTranscriptionCountRemaining": fields.Integer(attribute="daily_transcription_count_remaining"),
}
