from flask_restful import fields

# 文件夹基本字段
folder_fields = {
    "id": fields.String(attribute="id"),
    "name": fields.String(attribute="name"),
    "isDefault": fields.Boolean(attribute="is_default"),
    "transcriptionCount": fields.Integer(attribute="transcription_count"),
    "createdTime": fields.DateTime(attribute="created_time", dt_format="iso8601"),
    "updatedTime": fields.DateTime(attribute="updated_time", dt_format="iso8601"),
}

# 文件夹列表字段
folder_list_fields = {
    "folders": fields.List(fields.Nested(folder_fields)),
}
