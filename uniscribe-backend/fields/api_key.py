"""
API Key management response fields
"""

from flask_restful import fields

# Common response fields
success_response_fields = {
    "success": fields.<PERSON><PERSON><PERSON>,
    "message": fields.String,
    "timestamp": fields.String,
}


# Basic API key fields (for list view - no full key)
api_key_fields = {
    "id": fields.String,
    "name": fields.String,
    "keyPreview": fields.String,
    "isActive": fields.Boolean,
    "createdTime": fields.String,
    "lastUsedTime": fields.String,
    "expiresAt": fields.String,
    "rateLimitPerMinute": fields.Integer,
    "rateLimitPerDay": fields.Integer,
}

# API key fields with full key (for creation/reset responses)
api_key_with_full_key_fields = {
    "id": fields.String,
    "name": fields.String,
    "apiKey": fields.String,  # Full key only shown during creation/reset
    "keyPreview": fields.String,
    "isActive": fields.Boolean,
    "createdTime": fields.String,
    "lastUsedTime": fields.String,
    "expiresAt": fields.String,
    "rateLimitPerMinute": fields.Integer,
    "rateLimitPerDay": fields.Integer,
}

# List API keys response
api_key_list_fields = {
    **success_response_fields,
    "data": fields.Nested({
        "apiKeys": fields.List(fields.Nested(api_key_fields))
    })
}

# Single API key response (for get, update)
api_key_response_fields = {
    **success_response_fields,
    "data": fields.Nested(api_key_fields)
}

# API key creation/reset response (includes full key)
api_key_creation_response_fields = {
    **success_response_fields,
    "data": fields.Nested(api_key_with_full_key_fields)
}

# Delete response (no data, just success message)
api_key_delete_response_fields = success_response_fields
