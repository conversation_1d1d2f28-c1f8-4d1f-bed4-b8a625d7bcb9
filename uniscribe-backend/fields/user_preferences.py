from flask_restful import fields

# 用户偏好设置字段
user_preferences_fields = {
    "userId": fields.String(attribute="user_id"),
    "timezone": fields.String(attribute="timezone"),
    "notifyTranscriptionSuccess": fields.<PERSON><PERSON>an(
        attribute="notify_transcription_success"
    ),
    "notifyQuotaReset": fields.<PERSON><PERSON><PERSON>(attribute="notify_quota_reset"),
    "notifyProductUpdates": fields.<PERSON><PERSON><PERSON>(attribute="notify_product_updates"),
}
