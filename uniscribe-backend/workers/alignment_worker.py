#!/usr/bin/env python3
"""
说话人对齐 Worker

使用 Redis Streams 消费对齐任务，处理转录结果和说话人识别结果的对齐
"""

import sys
import os
import time
import signal
import logging
from typing import Dict, Any

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models.task_result import TaskResult
from models import db
from services.alignment_queue_service import AlignmentQueueService
from services.alignment_service import AlignmentService
from sqlalchemy.orm.attributes import flag_modified

logger = logging.getLogger(__name__)

# 全局 worker 实例，用于信号处理
worker_instance = None


def signal_handler(signum, frame):
    """处理信号，优雅关闭 worker"""
    global worker_instance
    logger.info("Received signal %s, shutting down worker...", signum)

    if worker_instance:
        worker_instance.stop()

    sys.exit(0)


class AlignmentWorker:
    """对齐任务处理器"""

    def __init__(self):
        self.running = True

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    @staticmethod
    def _is_word_level_segments(segments: list) -> bool:
        """判断segments是否是词级别的

        Args:
            segments: 转录段列表

        Returns:
            是否是词级别的segments
        """

        if not segments:
            return False

        # 检查前几个segments的特征
        sample_size = min(10, len(segments))
        word_like_count = 0
        for segment in segments[:sample_size]:
            text = segment.get("text", "").strip()
            if text:
                # 中文：单个字符或很短的词
                # 英文：单个单词（不包含空格）
                if len(text) <= 3 or (len(text.split()) == 1 and len(text) <= 15):
                    word_like_count += 1

        # 如果大部分segments都像单词，则认为是词级别
        return word_like_count / sample_size >= 0.7

    def process_alignment_task(self, task_data: Dict[str, Any]) -> bool:
        """处理单个对齐任务

        Args:
            task_data: 任务数据，包含 file_id, transcription_task_id, diarization_task_id

        Returns:
            是否处理成功
        """
        try:
            file_id_raw = task_data.get("file_id")
            if file_id_raw is None:
                logger.error("Missing file_id in task data: %s", task_data)
                return False

            file_id = int(file_id_raw)
            transcription_task_id = task_data.get("transcription_task_id")
            diarization_task_id = task_data.get("diarization_task_id")

            logger.info(f"Processing alignment task for file {file_id}")

            # 获取任务结果，强制从数据库刷新以避免缓存问题
            task_result = TaskResult.get_by_file_id(file_id, refresh=True)
            if not task_result:
                logger.error(f"No task result found for file {file_id}")
                return False

            # 检查是否已经有对齐结果
            if task_result.is_aligned:
                logger.info(f"File {file_id} already has aligned segments, skipping")
                return True

            # 检查必要数据是否存在
            if not task_result.segments:
                logger.error(f"No transcription segments found for file {file_id}")
                return False

            if not task_result.diarization_segments:
                logger.error(f"No diarization segments found for file {file_id}")
                return False

            # 验证说话人识别数据格式
            if not AlignmentService.validate_diarization_data(
                task_result.diarization_segments
            ):
                logger.error(f"Invalid diarization data format for file {file_id}")
                return False

            # 执行对齐
            logger.info(f"Starting alignment for file {file_id}")

            # 使用 WhisperX 算法进行对齐
            logger.info(f"Using WhisperX alignment algorithm for file {file_id}")
            aligned_segments = AlignmentService.align_segments_whisperx(
                task_result.segments, task_result.diarization_segments
            )

            if not aligned_segments:
                logger.error(f"Alignment failed for file {file_id}")
                return False

            # 保存原始转录结果到 original_segments（如果还没有保存）
            if not task_result.original_segments:
                task_result.original_segments = task_result.segments
                flag_modified(task_result, "original_segments")

            # 将对齐结果保存到 segments 字段
            task_result.segments = aligned_segments
            flag_modified(task_result, "segments")

            # 标记为已对齐
            task_result.is_aligned = True
            flag_modified(task_result, "is_aligned")

            db.session.commit()

            # 更新文件状态
            from controllers.task import update_file_status

            update_file_status(file_id)

            logger.info(
                f"Successfully aligned {len(aligned_segments)} segments for file {file_id}"
            )
            return True

        except Exception as e:
            logger.error(f"Error processing alignment task {task_data}: {e}")
            return False

    def run(self):
        """运行 Worker 主循环"""
        logger.info("Starting alignment worker...")

        while self.running:
            try:
                # 从队列获取任务
                task_data = AlignmentQueueService.dequeue_alignment_task(timeout=5000)

                if task_data:
                    message_id = task_data.get("message_id")
                    logger.info(f"Received alignment task: {task_data}")

                    # 处理任务
                    success = self.process_alignment_task(task_data)

                    if success and message_id:
                        # 确认任务完成
                        AlignmentQueueService.acknowledge_task(message_id)
                        logger.info(f"Task {message_id} completed successfully")
                    else:
                        logger.error(f"Task {message_id} failed, will retry later")
                        # 注意：失败的任务会保留在 pending 列表中，可以通过其他机制重试

                    # 清理 SQLAlchemy session 缓存，避免长期运行进程的缓存问题
                    db.session.remove()
                    logger.debug(
                        "Cleared SQLAlchemy session cache after task processing"
                    )
                else:
                    # 没有任务，短暂休息
                    time.sleep(1)

            except KeyboardInterrupt:
                logger.info("Received interrupt signal, shutting down...")
                self.running = False
            except Exception as e:
                logger.error(f"Unexpected error in worker loop: {e}")
                # 出错时也清理 session，避免缓存问题
                db.session.remove()
                time.sleep(5)  # 出错后等待一段时间再继续

        logger.info("Alignment worker stopped")

    def stop(self):
        """停止 Worker"""
        self.running = False


def main():
    """主函数"""
    global worker_instance

    logger.info("Starting alignment worker...")

    try:
        # 创建并启动 worker
        worker_instance = AlignmentWorker()
        worker_instance.run()

    except Exception as e:
        logger.error("Worker failed with error: %s", e)
        sys.exit(1)


if __name__ == "__main__":
    # 在 app context 中运行，就像 stripe_consumer 一样
    with app.app_context():
        main()
