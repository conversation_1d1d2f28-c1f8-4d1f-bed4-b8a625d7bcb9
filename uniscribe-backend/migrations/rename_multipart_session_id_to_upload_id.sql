-- 重命名 multipart_session_id 字段为 upload_id
-- 这个迁移脚本用于将现有的 multipart_session_id 字段重命名为 upload_id

-- 1. 首先删除旧的唯一约束
ALTER TABLE `multipart_uploads` DROP INDEX `uk_multipart_session_id`;

-- 2. 重命名字段
ALTER TABLE `multipart_uploads` CHANGE COLUMN `multipart_session_id` `upload_id` varchar(512) NOT NULL COMMENT 'S3分块上传会话ID';

-- 3. 添加新的唯一约束
ALTER TABLE `multipart_uploads` ADD UNIQUE KEY `uk_upload_id` (`upload_id`); 