-- 创建文件夹表
CREATE TABLE folders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '文件夹ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(40) NOT NULL COMMENT '文件夹名称，最大40个Unicode字符',
    is_default BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为默认文件夹',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已删除',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_id (user_id),
    UNIQUE KEY uix_user_folder_name (user_id, name)
) COMMENT='用户文件夹表';

-- 为 transcription_file 表添加 folder_id 字段
ALTER TABLE transcription_file 
ADD COLUMN folder_id BIGINT NULL COMMENT '所属文件夹ID，NULL表示在默认文件夹';

-- 现有的转录记录保持 folder_id 为 NULL，表示未分类状态
-- 用户可以后续手动将文件移动到自己创建的文件夹中
