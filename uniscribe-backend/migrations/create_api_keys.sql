-- 创建 API Keys 表
CREATE TABLE api_keys (
    id VARCHAR(20) PRIMARY KEY COMMENT 'API Key ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(100) NOT NULL COMMENT 'API Key 名称',
    key_hash VARCHAR(64) NOT NULL UNIQUE COMMENT 'API Key 的 SHA256 哈希值',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_used_time TIMESTAMP NULL COMMENT '最后使用时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间，NULL表示永不过期',
    rate_limit_per_minute INT NOT NULL DEFAULT 60 COMMENT '每分钟请求限制',
    rate_limit_per_day INT NOT NULL DEFAULT 1000 COMMENT '每天请求限制',
    
    INDEX idx_user_id (user_id),
    INDEX idx_key_hash (key_hash),
    INDEX idx_active (is_active),
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES user(id) ON DELETE CASCADE
) COMMENT='API Keys 表';

-- 创建 Webhook 日志表
CREATE TABLE webhook_logs (
    id VARCHAR(20) PRIMARY KEY COMMENT 'Webhook 日志ID',
    transcription_id VARCHAR(20) NOT NULL COMMENT '转录文件ID',
    webhook_url VARCHAR(500) NOT NULL COMMENT 'Webhook URL',
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型',
    status_code INT NULL COMMENT 'HTTP 状态码',
    response_body TEXT NULL COMMENT '响应内容',
    error_message TEXT NULL COMMENT '错误信息',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_transcription_id (transcription_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_time (created_time)
) COMMENT='Webhook 发送日志表';

-- 为 transcription_file 表添加 webhook_url 字段
ALTER TABLE transcription_file 
ADD COLUMN webhook_url VARCHAR(500) NULL COMMENT 'Webhook 回调URL';
