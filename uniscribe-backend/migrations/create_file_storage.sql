-- 创建文件存储表，用于管理文件引用计数和生命周期
CREATE TABLE `file_storage` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `fingerprint` varchar(32) NOT NULL COMMENT '文件MD5指纹',
  `file_type` varchar(20) NOT NULL,
  `file_key` varchar(255) NOT NULL COMMENT '存储系统中的文件键',
  `file_size` int NOT NULL,
  `reference_count` int NOT NULL DEFAULT '1' COMMENT '引用计数',
  `state` enum('active','pending_deletion','deleted') NOT NULL DEFAULT 'active' COMMENT '文件状态',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_access_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后访问时间，用于判断文件是否过期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_fingerprint` (`user_id`,`fingerprint`),
  KEY `idx_userId` (`user_id`),
  KEY `idx_fingerprint` (`fingerprint`),
  KEY `idx_createdTime` (`created_time`),
  KEY `idx_state` (`state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;