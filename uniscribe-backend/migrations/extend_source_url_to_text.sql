-- Extend source_url field to TEXT type for very long URLs
-- Some S3 URLs can exceed 1600 characters, VARCHAR(512) is insufficient
-- Execution date: 2025-01-XX

-- Modify source_url column from VARCHAR(512) to TEXT
ALTER TABLE transcription_file 
MODIFY COLUMN source_url TEXT NULL COMMENT '媒体来源的URL，根据source_type不同而有不同含义';

-- Verify the change
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'transcription_file' 
    AND COLUMN_NAME = 'source_url';
