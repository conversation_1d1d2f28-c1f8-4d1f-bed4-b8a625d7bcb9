-- Add retry_count column to task table
-- Migration: add_retry_count_to_task
-- Date: 2024-01-12

-- Add retry_count column with default value 0
ALTER TABLE task ADD COLUMN retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数';

-- Add index for retry_count to optimize queries
CREATE INDEX idx_task_retry_count ON task(retry_count);

-- Update existing tasks to have retry_count = 0 (should already be default, but just to be safe)
UPDATE task SET retry_count = 0 WHERE retry_count IS NULL;
