-- 添加消费模式字段的数据库迁移脚本
-- 执行时间：预计 < 1 分钟（取决于数据量）

-- 1. 添加 consumption_mode 字段
ALTER TABLE task 
ADD COLUMN consumption_mode VARCHAR(20) NOT NULL DEFAULT 'http_polling' 
COMMENT '消费方式: http_polling, redis_queue';

-- 2. 为新字段添加索引（可选，用于优化查询性能）
CREATE INDEX idx_task_consumption_mode ON task(consumption_mode);

-- 3. 创建复合索引优化任务查询
CREATE INDEX idx_task_status_mode_priority ON task(status, consumption_mode, priority DESC, created_time ASC);

-- 4. 验证数据
-- 检查所有现有任务都有默认值
SELECT 
    consumption_mode,
    COUNT(*) as count
FROM task 
GROUP BY consumption_mode;

-- 5. 显示表结构确认
DESCRIBE task;
