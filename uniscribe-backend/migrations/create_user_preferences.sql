-- 创建用户偏好设置表
CREATE TABLE `user_preferences` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `timezone` varchar(50) DEFAULT NULL COMMENT '用户时区',
  `notify_transcription_success` tinyint(1) NOT NULL DEFAULT '1' COMMENT '转录成功通知',
  `notify_quota_reset` tinyint(1) NOT NULL DEFAULT '1' COMMENT '转录配额重置通知',
  `notify_product_updates` tinyint(1) NOT NULL DEFAULT '1' COMMENT '产品更新和新功能通知',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `user_preferences_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户偏好设置表';