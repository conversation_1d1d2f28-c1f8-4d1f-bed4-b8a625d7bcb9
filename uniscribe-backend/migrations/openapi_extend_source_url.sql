-- Extend source_url field length for OpenAPI external URLs
-- External URLs can be very long, especially with query parameters
-- Execution date: 2024-12-XX

-- Modify source_url column to support longer URLs
ALTER TABLE transcription_file 
MODIFY COLUMN source_url VARCHAR(512) NULL COMMENT '媒体来源的URL，根据source_type不同而有不同含义';

-- Verify the change
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'transcription_file' 
    AND COLUMN_NAME = 'source_url';