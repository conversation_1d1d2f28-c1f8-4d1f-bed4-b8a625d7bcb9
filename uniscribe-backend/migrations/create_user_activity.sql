-- 创建用户活跃记录表
CREATE TABLE `user_activity` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `activity_date` date NOT NULL COMMENT '活跃日期（UTC时区）',
  `last_activity_time` timestamp NOT NULL COMMENT '最后活跃时间（UTC时区）',
  `is_anonymous` boolean NOT NULL COMMENT '活跃时是否为匿名用户',
  `has_free_plan` boolean NOT NULL DEFAULT FALSE COMMENT '是否拥有免费计划',
  `has_subscription` boolean NOT NULL DEFAULT FALSE COMMENT '是否拥有订阅权益',
  `has_ltd` boolean NOT NULL DEFAULT FALSE COMMENT '是否拥有LTD权益',
  `has_one_time` boolean NOT NULL DEFAULT FALSE COMMENT '是否拥有一次性付费权益',
  `is_merged` boolean NOT NULL DEFAULT FALSE COMMENT '是否已合并到注册用户',
  `merged_to_user_id` bigint NULL COMMENT '合并到的目标用户ID',
  `merged_time` timestamp NULL COMMENT '合并时间',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_activity_date` (`user_id`, `activity_date`),
  INDEX `idx_activity_date` (`activity_date`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_last_activity_time` (`last_activity_time`),
  INDEX `idx_subscription` (`activity_date`, `has_subscription`),
  INDEX `idx_ltd` (`activity_date`, `has_ltd`),
  INDEX `idx_one_time` (`activity_date`, `has_one_time`),
  INDEX `idx_anonymous` (`activity_date`, `is_anonymous`),
  INDEX `idx_is_merged` (`is_merged`),
  INDEX `idx_merged_to_user_id` (`merged_to_user_id`),
  INDEX `idx_merged_time` (`merged_time`),
  INDEX `idx_activity_date_is_merged` (`activity_date`, `is_merged`)
) COMMENT='用户活跃记录表，记录用户每日活跃情况及当时的用户状态快照';
