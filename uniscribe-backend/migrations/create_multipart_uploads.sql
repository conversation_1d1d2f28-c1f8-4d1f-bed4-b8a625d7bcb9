-- 创建分块上传表，用于跟踪大文件的分块上传进度
CREATE TABLE `multipart_uploads` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `upload_id` varchar(512) NOT NULL COMMENT 'S3分块上传会话ID',
  `transcription_file_id` bigint NOT NULL COMMENT '关联的转录文件ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `file_key` varchar(255) NOT NULL COMMENT '存储系统中的文件键',
  `filename` varchar(100) NOT NULL COMMENT '原始文件名',
  `file_type` varchar(20) NOT NULL COMMENT '文件类型',
  `file_size` bigint NOT NULL COMMENT '文件总大小',
  `fingerprint` varchar(32) NOT NULL COMMENT '文件MD5指纹',
  `status` enum('active','completed','aborted') NOT NULL DEFAULT 'active' COMMENT '上传状态',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_upload_id` (`upload_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transcription_file_id` (`transcription_file_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分块上传会话表';
