-- Add original file deletion related columns to transcription_file table
-- This migration adds support for tracking original file deletion with reasons and timestamps
-- This field indicates whether the original media file has been deleted while preserving the transcription record

ALTER TABLE transcription_file
ADD COLUMN original_file_deleted BOOLEAN NOT NULL DEFAULT FALSE
COMMENT '原文件是否已被删除，用于标识只保留转录记录的情况';

-- Add delete reason and timestamp fields
ALTER TABLE transcription_file
ADD COLUMN original_file_delete_reason ENUM(
    'user_manual',
    'auto_cleanup',
    'admin_operation'
) NULL COMMENT 'Original file deletion reason';

ALTER TABLE transcription_file
ADD COLUMN original_file_deleted_at TIMESTAMP NULL COMMENT 'Original file deletion timestamp';

-- Add indexes for efficient querying
CREATE INDEX idx_original_file_deleted ON transcription_file(original_file_deleted);
CREATE INDEX idx_delete_reason ON transcription_file(original_file_delete_reason);
CREATE INDEX idx_deleted_at ON transcription_file(original_file_deleted_at);

-- Update strategy for existing deleted records (is_deleted = 1):
-- For completed transcriptions that were deleted, we assume the original files were also deleted
-- since the file storage reference counting system would have marked them for deletion

UPDATE transcription_file
SET original_file_deleted = TRUE,
    original_file_delete_reason = 'user_manual',  
    original_file_deleted_at = updated_time  -- Use the last update time as approximation
WHERE is_deleted = 1
  AND status IN (5, 7, 4)  -- completed, completed_with_errors, partially_completed
  AND fingerprint IS NOT NULL
  AND fingerprint != '';

-- Note: We don't update records with status 1 (uploading), 2 (uploaded), 6 (failed), etc.
-- because these may not have had their original files properly processed or uploaded
