# export sitemap to csv
# https://www.uniscribe.co/sitemap.xml


def main():
    import requests
    import xml.etree.ElementTree as ET
    import csv

    # Download sitemap XML
    sitemap_url = "https://www.uniscribe.co/sitemap.xml"
    response = requests.get(sitemap_url)

    # Parse XML
    root = ET.fromstring(response.content)

    # Extract URLs
    urls = []
    for url in root.findall(".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc"):
        urls.append(url.text)

    # Write to CSV
    with open("sitemap.csv", "w", newline="") as f:
        writer = csv.writer(f)
        for url in urls:
            print(url)
            writer.writerow([url])

    print(f"Exported {len(urls)} URLs to sitemap.csv")


if __name__ == "__main__":
    main()
