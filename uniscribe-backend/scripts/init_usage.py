from models import db
from models.usage import Usage
from models.user import User
from app import app


def init_usage():
    users = User.query.all()
    for user in users:
        usage = Usage.get_by_user_id(user.id)
        if not usage:
            usage = Usage(user_id=user.id)
            db.session.add(usage)
            db.session.commit()


if __name__ == "__main__":
    # app context
    with app.app_context():
        init_usage()
