from models import insert_record, db_transaction
from models.plan import Plan
from config import CONFIG
from services.stripe_service import StripeService
from app import app


def init_plan():
    plans = [
        {
            "name": "basic_monthly",
            "tier": "Basic",
            "interval": "month",
            "stripe_price_id": "",
            "amount": 1000,  # 10 USD
            "currency": "USD",
            "description": "",
            "is_active": True,
        },
        {
            "name": "pro_monthly",
            "tier": "Pro",
            "interval": "month",
            "stripe_price_id": "",
            "amount": 2000,  # 20 USD
            "currency": "USD",
            "description": "",
            "is_active": True,
        },
        {
            "name": "basic_yearly",
            "tier": "Basic",
            "interval": "year",
            "stripe_price_id": "",
            "amount": 7200,  # 72 USD
            "currency": "USD",
            "description": "",
            "is_active": True,
        },
        {
            "name": "pro_yearly",
            "tier": "Pro",
            "interval": "year",
            "stripe_price_id": "",
            "amount": 14400,  # 144 USD
            "currency": "USD",
            "description": "",
            "is_active": True,
        },
    ]

    for plan in plans:
        price_lookup_key = CONFIG.STRIPE_PLAN_PRICE_LOOKUP_KEYS[plan["name"]]
        price_id = StripeService.get_price_id_by_lookup_key(price_lookup_key)
        plan["stripe_price_id"] = price_id
        plan = Plan(**plan)
        insert_record(plan)


if __name__ == "__main__":
    # app context
    with app.app_context():
        with db_transaction() as session:
            init_plan()
