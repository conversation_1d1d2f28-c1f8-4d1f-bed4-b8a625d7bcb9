import click
from datetime import datetime, timedelta
import logging

from app import app
from constants.transcription import TranscriptionFileStatus
from controllers.task import create_transcription_task
from controllers.transcription import complete_upload
from models.transcription_file import TranscriptionFile
from models import db
from exceptions.storage import FileNotFoundError

logger = logging.getLogger(__name__)


@click.command()
@click.option("--file-id", "-f", type=int, help="单个转录文件ID")
@click.option("--batch", "-b", is_flag=True, help="批量处理卡住的文件")
@click.option("--days", "-d", type=int, default=1, help="处理几天之内的文件，默认1天")
def main(file_id, batch, days):
    """修复卡住的转录文件并创建新的转录任务。

    可以通过 --file-id 指定单个文件，或使用 --batch 进行批量处理
    """
    if not file_id and not batch:
        raise click.ClickException("请指定 --file-id 或 --batch 参数")

    if file_id and batch:
        raise click.ClickException("--file-id 和 --batch 参数不能同时使用")

    if file_id:
        # 处理单个文件
        process_single_file(file_id)
    else:
        # 批量处理
        min_created_time = datetime.now() - timedelta(days=days)
        stuck_files = TranscriptionFile.get_files_by_status(
            TranscriptionFileStatus.uploading.id, min_created_time
        )

        if not stuck_files:
            logger.info("[auto_fix_stuck_files] 没有需要处理的文件")
            return

        logger.info("[auto_fix_stuck_files] 找到 %d 个需要处理的文件", len(stuck_files))
        for tf in stuck_files:
            try:
                process_single_file(tf.id)
            except FileNotFoundError as e:
                # 检查文件的创建时间，如果超过1小时，则标记为失败
                if tf.created_time < datetime.now() - timedelta(hours=1):
                    tf.status = TranscriptionFileStatus.failed.id
                    db.session.commit()
                    logger.info("[auto_fix_stuck_files] 文件 %d 标记为失败", tf.id)
                else:
                    logger.info("[auto_fix_stuck_files] 文件 %d 未找到，跳过", tf.id)
            except Exception as e:
                logging.exception("处理文件 %d 失败: %s", tf.id, str(e))


def process_single_file(file_id):
    """处理单个文件的逻辑"""
    tf = TranscriptionFile.get_by_id(file_id)
    if not tf:
        raise click.ClickException(f"文件 {file_id} 未找到")

    if tf.status != TranscriptionFileStatus.uploading.id:
        logger.info("[auto_fix_stuck_files] 无需处理 %d", file_id)
        return

    logger.info("[auto_fix_stuck_files] 正在处理文件 %d", file_id)
    user_id = tf.user_id
    transcription_file = complete_upload(user_id, file_id)
    logger.info("[auto_fix_stuck_files] 完成上传: %s", transcription_file)
    task = create_transcription_task(user_id, file_id)
    logger.info("[auto_fix_stuck_files] 创建任务: %s", task)


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    try:
        logger.info("[auto_fix_stuck_files] Cron job started")
        with app.app_context():
            main()
        logger.info("[auto_fix_stuck_files] Cron job completed successfully")
    except Exception as e:
        logger.exception("[auto_fix_stuck_files] Cron job failed with error")
