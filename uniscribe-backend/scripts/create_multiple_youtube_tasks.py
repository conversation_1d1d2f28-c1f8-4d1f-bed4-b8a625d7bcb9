#!/usr/bin/env python3
"""
批量创建 YouTube 转录任务脚本

使用方法:
    python create_multiple_youtube_tasks.py --url "https://www.youtube.com/watch?v=VIDEO_ID" --count 5 --user-id 123

参数:
    --url: YouTube URL
    --count: 要创建的任务数量
    --user-id: 用户ID
    --title: 可选，自定义标题（默认使用视频标题）
    --duration: 可选，自定义时长（默认从视频信息获取）
    --transcription-type: 可选，转录类型（transcript/subtitle，默认transcript）
    --language-code: 可选，语言代码
    --enable-speaker-diarization: 可选，是否启用说话人识别（默认False）
    --folder-id: 可选，文件夹ID
"""

import argparse
import logging
import sys
import os

sys.path.insert(0, os.path.abspath(os.path.dirname(__file__) + "/../"))
from flask import current_app
from app import app
from models import db
from models.user import User
from controllers.youtube import YoutubeTranscriber, YoutubeInfoExtractor
from controllers.task import create_media_preprocessing_task
from constants.task import TranscriptionType

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def validate_youtube_url(url):
    """验证 YouTube URL 格式"""
    import re

    patterns = [
        r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/watch\?(?=.*v=([A-Za-z0-9_-]{11,}))(?:\S+)?)$",
        r"^(?:https?://)?(?:www\.)?youtu\.be/([A-Za-z0-9_-]{11,})(?:\?.*)?$",
        r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/embed/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
        r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/shorts/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
        r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/live/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
    ]
    for pattern in patterns:
        match = re.match(pattern, url)
        if match:
            video_id = match.group(1)
            if video_id and re.match(r"^[A-Za-z0-9_-]{11,}$", video_id):
                return True
    return False


def get_video_info(url):
    """获取视频信息"""
    try:
        extractor = YoutubeInfoExtractor(url)
        info = extractor.extract()
        return info
    except Exception as e:
        logger.error(f"获取视频信息失败: {str(e)}")
        return None


def create_youtube_transcription_task(user_id, url, title, duration, **kwargs):
    """创建单个 YouTube 转录任务"""
    try:
        # 验证用户是否存在
        user = User.get_by_id(user_id)
        if not user:
            logger.error(f"用户不存在: {user_id}")
            return None
        # 获取存储实例
        storage = current_app.storage
        # 创建转录器
        transcriber = YoutubeTranscriber(user_id, url, title, duration, storage)
        # 创建转录文件
        transcription_type = kwargs.get(
            "transcription_type", TranscriptionType.TRANSCRIPT
        )
        language_code = kwargs.get("language_code")
        enable_speaker_diarization = kwargs.get("enable_speaker_diarization", False)
        folder_id = kwargs.get("folder_id")
        tf = transcriber.create_transcription_file(
            transcription_type, language_code, enable_speaker_diarization, folder_id
        )
        # 创建媒体预处理任务
        media_task = create_media_preprocessing_task(
            user_id, tf.id, "youtube", "youtube_download", url
        )
        logger.info(
            f"成功创建转录任务: file_id={tf.id}, task_id={media_task.id if media_task else 'N/A'}"
        )
        return tf
    except Exception as e:
        logger.error(f"创建转录任务失败: {str(e)}")
        return None


def main():
    parser = argparse.ArgumentParser(description="批量创建 YouTube 转录任务")
    parser.add_argument("--url", required=True, help="YouTube URL")
    parser.add_argument("--count", type=int, required=True, help="要创建的任务数量")
    parser.add_argument("--user-id", type=int, required=True, help="用户ID")
    parser.add_argument("--title", help="自定义标题（可选）")
    parser.add_argument("--duration", type=int, help="自定义时长（秒，可选）")
    parser.add_argument(
        "--transcription-type",
        choices=["transcript", "subtitle"],
        default="transcript",
        help="转录类型（默认: transcript）",
    )
    parser.add_argument("--language-code", help="语言代码（可选）")
    parser.add_argument(
        "--enable-speaker-diarization",
        action="store_true",
        help="启用说话人识别（默认: False）",
    )
    parser.add_argument("--folder-id", type=int, help="文件夹ID（可选）")
    args = parser.parse_args()
    # 验证 YouTube URL
    if not validate_youtube_url(args.url):
        logger.error("无效的 YouTube URL")
        sys.exit(1)
    # 获取视频信息
    logger.info(f"正在获取视频信息: {args.url}")
    with app.app_context():
        video_info = get_video_info(args.url)
        if not video_info:
            logger.error("无法获取视频信息")
            sys.exit(1)
        # 确定标题和时长
        title = args.title or video_info.get("title", "Unknown Title")
        duration = args.duration or video_info.get("duration", 0)
        logger.info(f"视频信息: 标题='{title}', 时长={duration}秒")
        # 准备任务参数
        task_kwargs = {
            "transcription_type": TranscriptionType(args.transcription_type),
            "language_code": args.language_code,
            "enable_speaker_diarization": args.enable_speaker_diarization,
            "folder_id": args.folder_id,
        }
        # 批量创建任务
        logger.info(f"开始创建 {args.count} 个转录任务...")
        successful_tasks = []
        failed_tasks = []
        for i in range(args.count):
            logger.info(f"创建第 {i+1}/{args.count} 个任务...")
            # 为每个任务添加序号到标题
            task_title = f"{title} (任务 {i+1})" if args.count > 1 else title
            task = create_youtube_transcription_task(
                args.user_id, args.url, task_title, duration, **task_kwargs
            )
            if task:
                successful_tasks.append(task)
                logger.info(f"✅ 任务 {i+1} 创建成功: file_id={task.id}")
            else:
                failed_tasks.append(i + 1)
                logger.error(f"❌ 任务 {i+1} 创建失败")
        # 输出结果统计
        logger.info("=" * 50)
        logger.info("批量创建任务完成!")
        logger.info(f"成功创建: {len(successful_tasks)} 个任务")
        logger.info(f"失败任务: {len(failed_tasks)} 个")
        if successful_tasks:
            logger.info("成功创建的任务:")
            for task in successful_tasks:
                logger.info(f"  - file_id: {task.id}, filename: {task.filename}")
        if failed_tasks:
            logger.info("失败的任务编号:")
            for task_num in failed_tasks:
                logger.info(f"  - 任务 {task_num}")
        logger.info("=" * 50)


if __name__ == "__main__":
    main()
