#!/usr/bin/env python3
"""
Redis Streams 一次性清理脚本
用于清理历史消息，节省内存

注意：Go consumer 已经修改为自动删除已确认的消息，
这个脚本主要用于清理历史遗留的消息。
"""
import time
import click
from flask.cli import with_appcontext

from services.task_queue_service import TaskQueueService

@click.command("cleanup-redis-streams")
@click.option("--keep", default=1000, help="每个队列保留的消息数量")
@click.option("--dry-run", is_flag=True, help="预览模式，不实际删除消息")
@click.option("--show-memory", is_flag=True, help="显示 Redis 内存使用情况")
@with_appcontext
def cleanup_streams(keep, dry_run, show_memory):
    """清理 Redis Streams 历史消息，节省内存"""

    if show_memory:
        show_memory_usage()
        click.echo()
        return 

    click.echo("=== Redis Streams 清理工具 ===")
    click.echo(f"保留消息数: {keep}")
    click.echo(f"模式: {'预览模式' if dry_run else '执行模式'}")
    click.echo()

    queue_service = TaskQueueService()

    # 1. 显示当前状态
    click.echo("📊 清理前状态:")
    stats = queue_service.get_all_queue_stats()
    total_before = 0
    for task_type, stat in stats.items():
        if stat['total'] > 0:
            click.echo(f"  {task_type}: {stat['total']} 条消息")
            total_before += stat['total']
    click.echo(f"  总计: {total_before} 条消息")
    click.echo()

    if dry_run:
        click.echo("🔍 预览清理结果:")
        for task_type, stat in stats.items():
            if stat['total'] > keep:
                to_delete = stat['total'] - keep
                click.echo(f"  {task_type}: 将删除 {to_delete} 条消息 (保留 {keep} 条)")
        return

    # 2. 执行清理
    click.echo("🧹 开始清理...")
    start_time = time.time()

    results = queue_service.cleanup_all_old_messages(keep)

    end_time = time.time()

    # 3. 显示清理结果
    click.echo("✅ 清理完成!")
    total_deleted = 0
    for task_type, deleted in results.items():
        click.echo(f"  {task_type}: 删除了 {deleted} 条消息")
        total_deleted += deleted

    if total_deleted == 0:
        click.echo("  没有需要清理的消息")
    else:
        click.echo(f"  总计删除: {total_deleted} 条消息")

    click.echo(f"  耗时: {end_time - start_time:.2f} 秒")
    click.echo()

    # 4. 显示清理后状态
    click.echo("📊 清理后状态:")
    stats_after = queue_service.get_all_queue_stats()
    total_after = 0
    for task_type, stat in stats_after.items():
        if stat['total'] > 0:
            click.echo(f"  {task_type}: {stat['total']} 条消息")
            total_after += stat['total']
    click.echo(f"  总计: {total_after} 条消息")

    if total_before > 0:
        saved_pct = (total_deleted / total_before) * 100
        click.echo(f"  节省: {saved_pct:.1f}% 内存空间")

def show_memory_usage():
    """显示 Redis 内存使用情况"""
    click.echo("=== Redis 内存使用情况 ===")

    queue_service = TaskQueueService()
    try:
        # 获取 Redis 内存信息
        info = queue_service.redis_client.info('memory')

        # 安全地获取内存信息
        try:
            used_memory = info.get('used_memory', 0) if hasattr(info, 'get') else info.get('used_memory', 0)
            used_memory_human = info.get('used_memory_human', 'N/A') if hasattr(info, 'get') else 'N/A'
            used_memory_peak = info.get('used_memory_peak', 0) if hasattr(info, 'get') else 0
            used_memory_peak_human = info.get('used_memory_peak_human', 'N/A') if hasattr(info, 'get') else 'N/A'
        except:
            # 如果获取失败，使用默认值
            used_memory = 0
            used_memory_human = 'N/A'
            used_memory_peak = 0
            used_memory_peak_human = 'N/A'

        click.echo(f"当前内存使用: {used_memory_human} ({used_memory:,} bytes)")
        click.echo(f"峰值内存使用: {used_memory_peak_human} ({used_memory_peak:,} bytes)")

        # 计算使用率（假设总内存 1GB）
        if used_memory > 0:
            total_memory = 1024 * 1024 * 1024  # 1GB
            usage_pct = (used_memory / total_memory) * 100
            click.echo(f"内存使用率: {usage_pct:.1f}% (基于 1GB 总内存)")

            if usage_pct > 90:
                click.echo("🚨 内存使用率过高，需要立即清理！")
            elif usage_pct > 80:
                click.echo("⚠️  内存使用率较高，建议清理")
            else:
                click.echo("✅ 内存使用正常")
        else:
            click.echo("⚠️  无法获取内存使用率")

    except Exception as e:
        click.echo(f"❌ 获取内存信息失败: {e}")


if __name__ == '__main__':
    cleanup_streams()
