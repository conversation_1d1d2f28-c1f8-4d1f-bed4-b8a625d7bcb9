"""
文件存储迁移脚本

该脚本用于将现有的转录文件记录迁移到新的FileStorage表中，
实现文件引用计数管理。

脚本不会删除远稏存储中的文件，文件清理工作由定时任务处理。
"""

from datetime import datetime, timedelta
from constants.file_storage import FileState

import click
from flask.cli import with_appcontext
from sqlalchemy import func


from models import db, db_transaction
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage


@click.command()
@click.option("--dryrun", is_flag=True, help="Run without making actual changes")
@click.option(
    "--batch-size", default=100, help="Number of records to process in each batch"
)
@with_appcontext
def migrate_file_storage(dryrun, batch_size):
    """Migrate existing files to the new FileStorage table."""
    click.echo("Starting migration of existing files to FileStorage table...")

    # 获取所有未删除的文件记录，按user_id和fingerprint分组，计算引用计数
    query = (
        db.session.query(
            TranscriptionFile.user_id,
            TranscriptionFile.fingerprint,
            func.any_value(TranscriptionFile.file_type).label("file_type"),
            func.any_value(TranscriptionFile.file_key).label("file_key"),
            func.any_value(TranscriptionFile.file_size).label("file_size"),
            func.count(TranscriptionFile.id).label("ref_count"),
        )
        .filter(
            TranscriptionFile.is_deleted == False,
            TranscriptionFile.fingerprint != "",  # 排除没有指纹的记录
        )
        .group_by(TranscriptionFile.user_id, TranscriptionFile.fingerprint)
    )

    total_groups = query.count()
    click.echo(f"Found {total_groups} unique file groups to migrate")

    if dryrun:
        click.echo("Dry run mode - no changes will be made")
        # 显示部分数据示例
        for group in query.limit(5).all():
            click.echo(
                f"Would create FileStorage: user_id={group.user_id}, "
                f"fingerprint={group.fingerprint}, refs={group.ref_count}"
            )
        return

    # 批量处理
    processed = 0
    created = 0
    skipped = 0
    errors = 0

    for offset in range(0, total_groups, batch_size):
        batch = query.limit(batch_size).offset(offset).all()

        with db_transaction():
            for group in batch:
                try:
                    # 检查该组合是否已存在FileStorage记录
                    existing = FileStorage.get_by_user_id_and_fingerprint(
                        group.user_id, group.fingerprint
                    )

                    if existing:
                        # 已存在，更新引用计数
                        existing.reference_count = group.ref_count
                        existing.last_access_time = datetime.now()
                        skipped += 1
                    else:
                        # 对于未删除的转录记录，设置文件状态为活跃
                        file_state = FileState.ACTIVE.value

                        # 获取该文件的最早和最近转录记录时间
                        earliest_record = (
                            TranscriptionFile.query.filter(
                                TranscriptionFile.user_id == group.user_id,
                                TranscriptionFile.fingerprint == group.fingerprint,
                            )
                            .order_by(TranscriptionFile.created_time.asc())
                            .first()
                        )

                        latest_record = (
                            TranscriptionFile.query.filter(
                                TranscriptionFile.user_id == group.user_id,
                                TranscriptionFile.fingerprint == group.fingerprint,
                            )
                            .order_by(TranscriptionFile.created_time.desc())
                            .first()
                        )

                        # 使用最早记录的创建时间和最近记录的更新时间
                        created_time = (
                            earliest_record.created_time
                            if earliest_record
                            else datetime.now()
                        )
                        updated_time = (
                            latest_record.updated_time
                            if latest_record
                            else datetime.now()
                        )
                        last_access_time = (
                            latest_record.created_time
                            if latest_record
                            else datetime.now()
                        )

                        # 检查记录是否已存在
                        existing_record = FileStorage.query.filter_by(
                            user_id=group.user_id, fingerprint=group.fingerprint
                        ).first()

                        if existing_record:
                            # 记录已存在，更新它
                            click.echo(
                                f"Record already exists for {group.user_id}-{group.fingerprint}, updating it"
                            )
                            # 只有当现有记录为活跃状态且新记录为删除状态时，保留现有状态
                            if (
                                existing_record.state == FileState.ACTIVE.value
                                and file_state == FileState.DELETED.value
                            ):
                                click.echo(
                                    f"Keeping active state for {group.user_id}-{group.fingerprint}"
                                )
                                # 保留活跃状态，但更新引用计数
                                existing_record.reference_count = max(
                                    existing_record.reference_count, group.ref_count
                                )
                            else:
                                # 否则更新状态和引用计数
                                existing_record.state = file_state
                                existing_record.reference_count = group.ref_count
                        else:
                            # 创建新记录
                            new_file_storage = FileStorage(
                                user_id=group.user_id,
                                fingerprint=group.fingerprint,
                                file_type=group.file_type,
                                file_key=group.file_key,
                                file_size=group.file_size,
                                reference_count=group.ref_count,
                                state=file_state,
                                created_time=created_time,
                                updated_time=updated_time,
                                last_access_time=last_access_time,
                            )
                            db.session.add(new_file_storage)
                        created += 1

                except Exception as e:
                    click.echo(
                        f"Error processing group {group.user_id}-{group.fingerprint}: {str(e)}"
                    )
                    errors += 1

                processed += 1

        # 显示进度
        click.echo(
            f"Processed {processed}/{total_groups} groups "
            f"(created: {created}, skipped: {skipped}, errors: {errors})"
        )

    # 处理已删除的转录文件记录
    click.echo("Processing deleted transcription files...")

    deleted_files_query = (
        db.session.query(
            TranscriptionFile.user_id,
            TranscriptionFile.fingerprint,
            func.any_value(TranscriptionFile.file_type).label("file_type"),
            func.any_value(TranscriptionFile.file_key).label("file_key"),
            func.any_value(TranscriptionFile.file_size).label("file_size"),
            func.count(TranscriptionFile.id).label("ref_count"),
        )
        .filter(
            TranscriptionFile.is_deleted == True, TranscriptionFile.fingerprint != ""
        )
        .group_by(TranscriptionFile.user_id, TranscriptionFile.fingerprint)
    )

    total_deleted = deleted_files_query.count()
    click.echo(f"Found {total_deleted} deleted file groups to process")

    processed_deleted = 0

    for offset in range(0, total_deleted, batch_size):
        batch = deleted_files_query.limit(batch_size).offset(offset).all()

        with db_transaction():
            for group in batch:
                try:
                    # 检查是否有未删除的记录引用同一文件
                    active_count = TranscriptionFile.query.filter(
                        TranscriptionFile.user_id == group.user_id,
                        TranscriptionFile.fingerprint == group.fingerprint,
                        TranscriptionFile.is_deleted == False,
                    ).count()

                    if active_count == 0:
                        # 没有活跃引用，在FileStorage中标记为已删除
                        # 检查记录是否已存在（不考虑状态）
                        existing_record = FileStorage.query.filter_by(
                            user_id=group.user_id, fingerprint=group.fingerprint
                        ).first()

                        if existing_record:
                            # 记录已存在，更新它
                            click.echo(
                                f"Record already exists for {group.user_id}-{group.fingerprint}, updating it"
                            )
                            # 只有当现有记录为活跃状态时，才保留活跃状态
                            if existing_record.state == FileState.ACTIVE.value:
                                click.echo(
                                    f"Keeping active state for {group.user_id}-{group.fingerprint}"
                                )
                                # 保留活跃状态，不做任何更改
                                pass
                            else:
                                # 否则更新为删除状态
                                existing_record.state = (
                                    FileState.PENDING_DELETION.value
                                )  # 标记为待删除而非已删除
                                existing_record.reference_count = 0
                        else:
                            # 创建已删除状态的记录
                            # 对于已删除的记录，使用过去的时间，确保它们能被及时清理
                            past_time = datetime.now() - timedelta(days=31)  # 超过30天
                            new_file_storage = FileStorage(
                                user_id=group.user_id,
                                fingerprint=group.fingerprint,
                                file_type=group.file_type,
                                file_key=group.file_key,
                                file_size=group.file_size,
                                reference_count=0,
                                state=FileState.PENDING_DELETION.value,  # 标记为待删除而非已删除，因为文件实际上还未从 R2 中删除
                                created_time=past_time,
                                updated_time=past_time,
                                last_access_time=past_time,
                            )
                            db.session.add(new_file_storage)

                        # 注意：不再从存储中删除文件，交由定时任务处理
                        click.echo(
                            f"File {group.file_key} will be cleaned up by scheduled task"
                        )
                except Exception as e:
                    click.echo(
                        f"Error processing deleted group {group.user_id}-{group.fingerprint}: {str(e)}"
                    )

                processed_deleted += 1

        # 显示进度
        click.echo(f"Processed {processed_deleted}/{total_deleted} deleted groups")

    click.echo("Migration completed!")
    click.echo(f"Total processed: {processed}")
    click.echo(f"Created: {created}")
    click.echo(f"Skipped: {skipped}")
    click.echo(f"Errors: {errors}")
    click.echo(f"Deleted groups processed: {processed_deleted}")


if __name__ == "__main__":
    migrate_file_storage(dryrun=False, batch_size=100)
