import time
import logging
from datetime import datetime

import click
from dateutil.relativedelta import relativedelta

from flask.cli import with_appcontext

from models import db, db_transaction
from models.user import User
from models.plan import Plan, PlanType
from models.subscription import Subscription
from models.entitlement import Entitlement, EntitlementSource
from constants.subscription import SubscriptionStatus
from services.entitlement_service import EntitlementService

logger = logging.getLogger(__name__)


@click.command("add-subscription")
@click.option("--user-id", type=int, required=True, help="User ID")
@click.option(
    "--tier-name",
    type=click.Choice(["Basic", "Pro"]),
    required=True,
    help="Plan tier, options: Basic, Pro",
)
@click.option(
    "--purpose",
    type=str,
    required=True,
    help="Subscription purpose, used to generate unique stripe_subscription_id",
)
@click.option(
    "--duration",
    type=int,
    default=12,
    help="Subscription duration in months, default is 12",
)
@click.option(
    "--dry-run", is_flag=True, help="Simulate execution without modifying the database"
)
@with_appcontext
def add_subscription(user_id, tier_name, purpose, duration, dry_run):
    """
    Add annual subscription plan for a specific user (for testing or gifting purposes)

    Example:
    flask admin add-subscription --user-id=123456789 --tier-name=Pro --purpose=gift_for_friend
    """
    try:
        # 1. Check if user exists
        user = User.get_by_id(user_id)
        if not user:
            logger.error("User %s not found", user_id)
            return

        logger.info(
            "User info: ID=%s, Email=%s, Name=%s", user.id, user.email, user.full_name
        )

        # 2. Find matching plan
        plan = Plan.query.filter_by(
            tier=tier_name,
            plan_type=PlanType.SUBSCRIPTION,
            interval="year",
            is_active=True,
        ).first()

        if not plan:
            logger.error("No matching plan found: tier=%s, interval=year", tier_name)
            return

        logger.info(
            "Plan info: ID=%s, Name=%s, Credits=%s",
            plan.id,
            plan.name,
            plan.credit_amount,
        )

        # 3. Generate unique stripe_subscription_id
        timestamp = int(time.time())
        stripe_subscription_id = f"fake_sub_{purpose}_{timestamp}"

        # 4. Set subscription period
        current_time = datetime.now()
        period_end = current_time + relativedelta(
            months=duration
        )  # Using relativedelta for more accurate month calculation

        # 5. Create subscription record
        subscription = Subscription(
            user_id=user_id,
            plan_id=plan.id,
            stripe_subscription_id=stripe_subscription_id,
            status=SubscriptionStatus.ACTIVE.value,
            current_period_start=current_time,
            current_period_end=period_end,
            cancel_at_period_end=False,
        )

        if dry_run:
            # Check for free entitlements that would be terminated
            current_time = datetime.now()
            free_entitlements = Entitlement.query.filter(
                Entitlement.user_id == user_id,
                Entitlement.source_type == EntitlementSource.FREE_PLAN,
                Entitlement.valid_until > current_time,
                Entitlement.is_recurring.is_(True),
            ).all()

            logger.info("DRY RUN - Would create the following subscription:")
            logger.info("  User ID: %s", subscription.user_id)
            logger.info("  Plan ID: %s", subscription.plan_id)
            logger.info(
                "  Stripe Subscription ID: %s", subscription.stripe_subscription_id
            )
            logger.info("  Status: %s", subscription.status)
            logger.info("  Period Start: %s", subscription.current_period_start)
            logger.info("  Period End: %s", subscription.current_period_end)

            if free_entitlements:
                logger.info(
                    "DRY RUN - Would terminate the following free plan entitlements:"
                )
                for free_entitlement in free_entitlements:
                    logger.info(
                        "  Entitlement ID: %s, Valid until: %s",
                        free_entitlement.id,
                        free_entitlement.valid_until,
                    )

            logger.info("DRY RUN - No changes made to database")
            return

        # 6. Save to database and create entitlement
        with db_transaction():
            # First, terminate any existing free plan entitlements
            current_time = datetime.now()
            free_entitlements = Entitlement.query.filter(
                Entitlement.user_id == user_id,
                Entitlement.source_type == EntitlementSource.FREE_PLAN,
                Entitlement.valid_until > current_time,
                Entitlement.is_recurring.is_(True),
            ).all()

            for free_entitlement in free_entitlements:
                EntitlementService.terminate_entitlement(
                    free_entitlement, termination_time=current_time
                )
                logger.info(
                    "Terminated free plan entitlement %s for user %s",
                    free_entitlement.id,
                    user_id,
                )

            # Save subscription record
            db.session.add(subscription)
            db.session.flush()  # Ensure ID is generated

            # Create entitlement
            entitlement = EntitlementService.create_from_subscription(subscription)
            db.session.add(entitlement)

            logger.info(
                "Successfully created %s annual subscription for user %s",
                tier_name,
                user.email,
            )
            logger.info("Subscription ID: %s", subscription.id)
            logger.info("Entitlement ID: %s", entitlement.id)
            logger.info(
                "Valid period: %s to %s",
                entitlement.valid_from,
                entitlement.valid_until,
            )

    except Exception as e:
        logger.error("Failed to add subscription: %s", str(e))
        raise click.ClickException(str(e))
