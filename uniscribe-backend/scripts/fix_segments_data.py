from app import app
from models.task_result import TaskResult
from models import db_transaction, db


@db_transaction()
def main():
    task_results = TaskResult.query.all()
    print(task_results)
    for task_result in task_results:
        task_result.segments = [
            {
                "id": i,
                "start_time": segment["start_time"],
                "end_time": segment["end_time"],
                "text": segment["text"],
            }
            for i, segment in enumerate(task_result.segments)
        ]
        db.session.add(task_result)


if __name__ == "__main__":
    with app.app_context():
        main()
