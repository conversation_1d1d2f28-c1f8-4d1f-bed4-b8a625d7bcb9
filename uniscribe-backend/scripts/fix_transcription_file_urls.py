import click
import logging
from datetime import datetime

from flask import current_app

from app import app
from constants.transcription import TranscriptionFileStatus
from controllers.transcription import verify_file_integrity
from models import db, db_transaction
from models.transcription_file import TranscriptionFile

logger = logging.getLogger(__name__)


@click.command()
@click.option("--dry-run", is_flag=True, help="Run without making actual changes")
@click.option(
    "--limit", type=int, default=None, help="Limit the number of records to process"
)
def fix_transcription_file_urls(dry_run, limit):
    """
    Fix transcription files where file_url and file_key are missing the fingerprint after the '-.' pattern.

    This script identifies records where:
    1. The language field is NULL
    2. The file_key contains '-.' pattern
    3. The fingerprint is not NULL

    It then reconstructs the correct file_url and file_key by appending the fingerprint.
    """
    # Find affected records
    query = TranscriptionFile.query.filter(
        TranscriptionFile.language.is_(None),
        TranscriptionFile.file_key.like("%-.%"),
        TranscriptionFile.fingerprint.isnot(None),
        TranscriptionFile.fingerprint != "",
        TranscriptionFile.status == TranscriptionFileStatus.completed.id,
    )

    if limit:
        query = query.limit(limit)

    affected_files = query.all()

    if not affected_files:
        logger.info("No affected files found.")
        return

    logger.info(f"Found {len(affected_files)} files to fix")

    fixed_count = 0

    with db_transaction() as session:
        for tf in affected_files:
            # Extract the base part of the URL (before the "-.extension")
            file_url_parts = tf.file_url.split("-.")
            file_key_parts = tf.file_key.split("-.")

            if len(file_url_parts) != 2 or len(file_key_parts) != 2:
                logger.warning(f"Unexpected URL format for file ID {tf.id}, skipping")
                continue

            # Get the extension part
            url_extension = file_url_parts[1]
            key_extension = file_key_parts[1]

            # Construct the correct URLs with fingerprint
            correct_file_url = f"{file_url_parts[0]}-{tf.fingerprint}.{url_extension}"
            correct_file_key = f"{file_key_parts[0]}-{tf.fingerprint}.{key_extension}"

            logger.info(f"File ID: {tf.id}")
            logger.info(f"  Old file_url: {tf.file_url}")
            logger.info(f"  New file_url: {correct_file_url}")
            logger.info(f"  Old file_key: {tf.file_key}")
            logger.info(f"  New file_key: {correct_file_key}")

            # 如果 old file key 存在或者 new file key 不存在，则不进行修复
            if check_remote_file_integrity(tf, tf.file_key):
                logger.info(f"Skipping file ID {tf.id} because old file key exists")
                continue
            if not check_remote_file_integrity(tf, correct_file_key):
                logger.info(
                    f"Skipping file ID {tf.id} because new file key is not valid"
                )
                continue
            logger.info(f"Fixing file ID {tf.id}")
            if not dry_run:
                tf.file_url = correct_file_url
                tf.file_key = correct_file_key
                tf.updated_time = datetime.now()
                session.add(tf)
                fixed_count += 1

    if dry_run:
        logger.info(f"DRY RUN: Would have fixed {len(affected_files)} files")
    else:
        logger.info(f"Successfully fixed {fixed_count} files")


def check_remote_file_integrity(tf, file_key):
    storage = current_app.storage
    try:
        return verify_file_integrity(storage, tf, file_key)
    except Exception as e:
        # logger.exception(f"Error checking remote file integrity: {e}")
        return False


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    try:
        logger.info("Starting transcription file URL fix script")
        with app.app_context():
            fix_transcription_file_urls()
        logger.info("Script completed successfully")
    except Exception as e:
        logger.exception("Script failed with error")
