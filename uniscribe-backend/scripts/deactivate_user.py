import click
import logging
from datetime import datetime

from controllers.user import deactivate_account
from models.user import User

logger = logging.getLogger(__name__)


@click.command()
@click.argument("user_id", type=int)
@click.option(
    "--dry-run", is_flag=True, help="Show what would happen without making changes"
)
@click.option("--force", is_flag=True, help="Skip confirmation prompt")
def deactivate_user(user_id: int, dry_run: bool, force: bool):
    """
    注销指定用户的账号

    参数：
    user_id: 要注销的用户ID
    """
    try:
        # 检查用户是否存在
        user = User.get_by_id(user_id)
        if not user:
            logger.error(f"User {user_id} not found")
            return

        # 显示用户信息
        logger.info(f"User ID: {user.id}")
        logger.info(f"Email: {user.email}")
        logger.info(f"Full Name: {user.full_name}")
        logger.info(f"Provider: {user.provider}")
        logger.info(f"Is Anonymous: {user.is_anonymous}")
        logger.info(f"Created Time: {user.created_time}")

        if dry_run:
            logger.info("DRY RUN - No changes will be made")
            result = deactivate_account(user_id, dry_run=True)
            return

        # 如果没有使用 --force，请求确认
        if not force:
            click.confirm(
                f"Are you sure you want to deactivate user {user.email}?",
                abort=True,  # 如果用户选择 No，会自动中止程序
            )

        # 执行注销操作
        result = deactivate_account(user_id, dry_run=False)

        if result:
            logger.info(f"Successfully deactivated user {user_id}")
        else:
            logger.error(f"Failed to deactivate user {user_id}")

    except Exception as e:
        logger.exception(f"Error deactivating user: {str(e)}")
        raise click.ClickException(str(e))


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )
    deactivate_user()
