import xml.etree.ElementTree as ET
from google.oauth2 import service_account
from google.auth.transport.requests import Request
import requests
import time
import logging

logger = logging.getLogger(__name__)

# Constants
SERVICE_ACCOUNT_FILE = "cert/uniscribe_indexing_api_service_account.json"
SITEMAP_URL = "https://www.uniscribe.co/sitemap.xml"
API_KEY = "AIzaSyAAOZxjyzKsQCa_JcK_2gmS82yOPAvrTfk"
SCOPES = ["https://www.googleapis.com/auth/indexing"]
INDEXING_API_URL = "https://indexing.googleapis.com/v3/urlNotifications:publish"


def get_credentials():
    """Set up and return authentication credentials."""
    credentials = service_account.Credentials.from_service_account_file(
        SERVICE_ACCOUNT_FILE, scopes=SCOPES
    )
    credentials.refresh(Request())
    logger.info(
        f"Token state: {credentials.token_state}, Expired: {credentials.expired}"
    )
    return credentials


def parse_sitemap(sitemap_url):
    """Parse sitemap and return list of URLs."""
    response = requests.get(sitemap_url)
    if response.status_code != 200:
        logger.error(f"Failed to fetch sitemap: {response.status_code}")
        return []

    root = ET.fromstring(response.content)
    urls = [
        url.text
        for url in root.findall(".//{http://www.sitemaps.org/schemas/sitemap/0.9}loc")
    ]
    logger.info(f"Parsed {len(urls)} URLs from sitemap")
    return urls


def request_indexing(url, access_token):
    """Request indexing for a single URL."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}",
    }
    data = {
        "url": url,
        "type": "URL_UPDATED",
    }
    response = requests.post(
        f"{INDEXING_API_URL}?key={API_KEY}",
        headers=headers,
        json=data,
    )
    if response.status_code == 200:
        logger.info(f"Indexing requested for {url}")
    else:
        logger.error(f"Failed to request indexing for {url}: {response.content}")


def main():
    credentials = get_credentials()
    access_token = credentials.token
    print(access_token)
    urls = parse_sitemap(SITEMAP_URL)

    for url in urls:
        request_indexing(url, access_token)
        time.sleep(1)  # Rate limiting


if __name__ == "__main__":
    main()
