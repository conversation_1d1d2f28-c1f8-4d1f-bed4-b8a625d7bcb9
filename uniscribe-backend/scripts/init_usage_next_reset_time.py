import logging
import click
from datetime import datetime, timedelta
from models import db
from models.user import User
from models.usage import Usage
from models.subscription import Subscription
from models.plan import Plan
from controllers.usage import reset_periodic_usage
from app import app

logger = logging.getLogger(__name__)


def process_users(dry_run=False, user_id=None):
    """初始化用户的使用量重置时间，可选择处理单个用户或所有用户"""
    try:
        if user_id:
            # 处理单个用户
            users = [User.query.get(user_id)]
            if not users[0]:
                raise click.ClickException(f"User with ID {user_id} not found")
            logger.info(f"Processing single user with ID {user_id}")
        else:
            # 处理所有用户
            users = User.query.all()
            logger.info("Processing all users")

        now = datetime.now()
        processed_count = 0

        for user in users:
            # 检查用户的订阅状态
            active_subscription = Subscription.get_active_subscription_by_user_id(
                user.id
            )

            # 如果没有活跃订阅，说明是免费用户
            # 如果有活跃订阅，检查是否是年付费计划
            needs_monthly_reset = False
            if not active_subscription:
                needs_monthly_reset = True  # 免费用户
                logger.info(
                    f"User {user.id} has no active subscription, needs_monthly_reset set to True"
                )
            else:
                plan = Plan.get_by_id(active_subscription.plan_id)
                if plan.interval.lower() == "year":
                    logger.info(
                        f"User {user.id} has a yearly subscription, needs_monthly_reset set to True"
                    )
                    needs_monthly_reset = True  # 年付费用户
                else:
                    logger.info(
                        f"User {user.id} has a subscription with interval {plan.interval}, needs_monthly_reset set to False"
                    )

            if needs_monthly_reset:
                usage = Usage.get_by_user_id(user.id)
                if not usage:
                    logger.warning(f"No usage record found for user {user.id}")
                    continue

                # 如果没有 next_reset_time，根据用户类型计算
                if not usage.next_reset_time:
                    # 确定重置时间的基准点
                    if active_subscription and plan.interval.lower() == "year":
                        # 年付费用户：基于订阅周期开始时间计算
                        base_time = active_subscription.current_period_start
                    else:
                        # 免费用户：基于创建时间计算
                        base_time = usage.created_time

                    # 计算第一次重置时间
                    first_reset_time = base_time + timedelta(days=30)

                    # 计算下一次重置时间
                    if first_reset_time <= now:
                        months_passed = ((now - first_reset_time).days // 30) + 1
                        next_reset = first_reset_time + timedelta(
                            days=30 * months_passed
                        )
                    else:
                        next_reset = first_reset_time

                    if not dry_run:
                        if first_reset_time <= now:
                            # 如果第一次重置时间已过，先重置当前使用量
                            reset_periodic_usage(user.id)
                        # 设置下次重置时间
                        usage.next_reset_time = next_reset
                        usage.updated_time = now
                        db.session.commit()

                    logger.info(
                        f"{'[DRY RUN] Would set' if dry_run else 'Set'} next_reset_time to {next_reset} for user {user.id}"
                        f" ({'yearly subscription' if active_subscription and plan.interval.lower() == 'year' else 'free user'})"
                    )
                else:
                    logger.info(
                        f"next_reset_time for user {user.id} is already set to {usage.next_reset_time}"
                    )

                processed_count += 1
                logger.info(f"Processed user {user.id}")

        return processed_count

    except Exception as e:
        logger.exception(f"Error initializing usage reset times: {str(e)}")
        if not dry_run:
            db.session.rollback()
        raise
    finally:
        if not dry_run:
            db.session.close()


@click.command()
@click.option("--dry-run", is_flag=True, help="Run in dry-run mode (no actual changes)")
@click.option("--user-id", type=int, help="Process specific user ID only")
def main(dry_run, user_id):
    """初始化用户使用量重置时间的脚本，支持处理所有用户或单个用户"""
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    logger.info(
        f"Starting usage reset time initialization {'(DRY RUN)' if dry_run else ''}"
        f"{f' for user {user_id}' if user_id else ' for all users'}"
    )

    try:
        processed_count = process_users(dry_run, user_id)
        logger.info(
            f"Successfully processed {processed_count} users {'(DRY RUN)' if dry_run else ''}"
        )
    except Exception as e:
        logger.error(f"Script failed: {str(e)}")
        raise click.ClickException(str(e))


if __name__ == "__main__":
    with app.app_context():
        main()
