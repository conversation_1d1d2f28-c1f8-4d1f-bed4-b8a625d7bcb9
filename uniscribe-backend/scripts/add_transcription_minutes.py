#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用于给用户添加转录分钟数的脚本。
可以通过用户ID或邮箱来指定用户。
"""

import logging
from datetime import datetime, timedelta

import click
from flask.cli import with_appcontext

from models import db_transaction
from models.user import User
from models.entitlement import Entitlement, EntitlementSource
from libs.id_generator import id_generator

logger = logging.getLogger(__name__)


@click.command(help="给用户添加转录分钟数")
@click.option("--user-id", type=int, help="用户ID")
@click.option("--email", type=str, help="用户邮箱")
@click.argument("minutes", type=int)
@click.option(
    "--dry-run", is_flag=True, help="Show what would happen without making changes"
)
@click.option("--force", is_flag=True, help="跳过确认提示")
@with_appcontext
def add_minutes(user_id=None, email=None, minutes=0, dry_run=False, force=False):
    """
    给指定用户增加转录分钟数配额

    参数：
    --user-id: 用户ID
    --email: 用户邮箱
    minutes: 要增加的分钟数
    --dry-run: 模拟运行，不实际修改数据
    --force: 跳过确认提示直接执行
    """
    try:
        # 检查用户是否存在
        user = None
        if user_id:
            user = User.get_by_id(user_id)
            if not user:
                logger.error(f"User ID {user_id} not found")
                return
        elif email:
            user = User.get_by_email(email)
            if not user:
                logger.error(f"User with email {email} not found")
                return
        else:
            logger.error("Either user_id or email must be provided")
            return

        user_id = user.id

        # 计算有效期 - 默认30天
        valid_until = datetime.now() + timedelta(days=30)

        logger.info(f"User: {user_id} ({user.email})")
        logger.info(f"Adding: {minutes} minutes")
        logger.info(f"Valid until: {valid_until}")

        if dry_run:
            logger.info("DRY RUN - No changes made")
            return

        # 如果没有使用 --force，请求确认
        if not force and not dry_run:
            if not click.confirm(f"确定要给用户 {user.email} 添加 {minutes} 分钟吗?"):
                logger.info("操作已取消")
                return

        with db_transaction() as session:
            # 添加 Entitlement 记录
            new_entitlement = Entitlement(
                id=id_generator.get_id(),
                user_id=user_id,
                total_credits=minutes,
                consumed_credits=0,
                valid_from=datetime.now(),
                valid_until=valid_until,
                source_type=EntitlementSource.BONUS,  # 使用 BONUS 类型
                source_id=user_id,  # 对于奖励积分，可以使用用户ID作为来源ID
                is_recurring=False,
            )
            session.add(new_entitlement)

        logger.info(
            f"Successfully added {minutes} minutes to user {user_id} ({user.email})"
        )

    except Exception as e:
        logger.exception(f"Error adding minutes: {str(e)}")
        raise click.ClickException(str(e))


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )
    # 使用 Flask CLI 运行，不需要在这里调用
    # 使用方式：flask admin add-minutes --user-id=123 60
    # 或者：flask  admin add-minutes --email=<EMAIL> 60
