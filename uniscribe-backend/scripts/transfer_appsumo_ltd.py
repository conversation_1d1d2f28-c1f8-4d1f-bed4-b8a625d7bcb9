"""
AppSumo LTD 权益转移脚本

用于将 A 用户的 AppSumo LTD 权益转移到 B 用户，并恢复 A 用户的免费权益。
"""

import logging
from datetime import datetime
import click
from flask.cli import with_appcontext

from models import db, db_transaction
from models.user import User
from models.appsumo_license import AppSumoLicense
from models.appsumo_oauth_token import AppSumoOAuthToken
from models.purchase import Purchase
from models.entitlement import Entitlement, EntitlementSource
from services.entitlement_service import EntitlementService

logger = logging.getLogger(__name__)


def _display_user_status(user_id, label):
    """显示用户当前状态"""
    user = User.get_by_id(user_id)
    if not user:
        click.echo(f"❌ {label} 用户 {user_id} 不存在")
        return False

    click.echo(f"\n📋 {label} 用户信息:")
    click.echo(f"  用户ID: {user.id}")
    click.echo(f"  邮箱: {user.email}")
    click.echo(f"  姓名: {user.full_name}")
    click.echo(f"  注册时间: {user.created_time}")

    # 显示 AppSumo License 信息
    licenses = AppSumoLicense.get_by_user_id(user_id)
    if licenses:
        click.echo(f"  📄 AppSumo Licenses ({len(licenses)} 个):")
        for license_obj in licenses:
            click.echo(f"    - License Key: {license_obj.license_key}")
            click.echo(f"      Tier: {license_obj.tier}, Status: {license_obj.status}")
            click.echo(f"      激活时间: {license_obj.activation_time}")
    else:
        click.echo("  📄 AppSumo Licenses: 无")

    # 显示 OAuth Token 信息
    oauth_token = AppSumoOAuthToken.get_by_user_id(user_id)
    if oauth_token:
        click.echo("  🔑 AppSumo OAuth Token:")
        click.echo(f"    - License Key: {oauth_token.license_key}")
        click.echo(f"    - 过期时间: {oauth_token.expires_at}")
    else:
        click.echo("  🔑 AppSumo OAuth Token: 无")

    # 显示 Purchase 信息
    purchases = Purchase.query.filter_by(user_id=user_id).all()
    appsumo_purchases = [
        p
        for p in purchases
        if p.stripe_payment_id and p.stripe_payment_id.startswith("appsumo_")
    ]
    if appsumo_purchases:
        click.echo(f"  💰 AppSumo Purchases ({len(appsumo_purchases)} 个):")
        for purchase in appsumo_purchases:
            click.echo(f"    - Purchase ID: {purchase.id}")
            click.echo(f"      Plan ID: {purchase.plan_id}")
            click.echo(f"      Stripe Payment ID: {purchase.stripe_payment_id}")
            click.echo(f"      创建时间: {purchase.created_time}")
    else:
        click.echo("  💰 AppSumo Purchases: 无")

    # 显示当前权益信息
    active_entitlements = Entitlement.get_active_entitlements(user_id)
    if active_entitlements:
        click.echo(f"  🎫 当前有效权益 ({len(active_entitlements)} 个):")
        for ent in active_entitlements:
            remaining = ent.total_credits - ent.consumed_credits
            click.echo(f"    - 权益ID: {ent.id}")
            click.echo(f"      来源: {ent.source_type}, 来源ID: {ent.source_id}")
            click.echo(f"      积分: {remaining}/{ent.total_credits} (剩余/总计)")
            click.echo(f"      有效期: {ent.valid_from} ~ {ent.valid_until}")
            click.echo(f"      自动续期: {ent.is_recurring}")
    else:
        click.echo("  🎫 当前有效权益: 无")

    return True


def _get_appsumo_data_for_user(user_id):
    """获取用户的 AppSumo 相关数据"""
    licenses = AppSumoLicense.get_by_user_id(user_id)
    oauth_token = AppSumoOAuthToken.get_by_user_id(user_id)
    purchases = Purchase.query.filter_by(user_id=user_id).all()
    appsumo_purchases = [
        p
        for p in purchases
        if p.stripe_payment_id and p.stripe_payment_id.startswith("appsumo_")
    ]

    # 获取 LTD 权益
    ltd_entitlements = Entitlement.query.filter(
        Entitlement.user_id == user_id,
        Entitlement.source_type == EntitlementSource.LTD,
        Entitlement.valid_until > datetime.now(),
    ).all()

    return {
        "licenses": licenses,
        "oauth_token": oauth_token,
        "purchases": appsumo_purchases,
        "entitlements": ltd_entitlements,
    }


@click.command("transfer-appsumo-ltd")
@click.option("--from-user-id", type=int, required=True, help="源用户ID (A用户)")
@click.option("--to-user-id", type=int, required=True, help="目标用户ID (B用户)")
@click.option("--dry-run", is_flag=True, help="预览模式，不实际执行修改")
@click.option("--force", is_flag=True, help="跳过确认提示")
@with_appcontext
def transfer_appsumo_ltd(from_user_id, to_user_id, dry_run, force):
    """
    转移 AppSumo LTD 权益从 A 用户到 B 用户

    该命令会：
    1. 终结 B 用户（目标用户）的现有免费权益
    2. 将 A 用户的 AppSumo 相关数据转移到 B 用户
    3. 恢复 A 用户的免费权益

    涉及的数据表：
    - appsumo_license: 更新 user_id
    - appsumo_oauth_token: 更新 user_id
    - purchase: 更新 user_id
    - entitlement: 更新 user_id 和终结免费权益

    示例:
    flask admin transfer-appsumo-ltd --from-user-id=123 --to-user-id=456 --dry-run
    flask admin transfer-appsumo-ltd --from-user-id=123 --to-user-id=456
    """
    try:
        logger.info(
            "开始 AppSumo LTD 权益转移: %s -> %s (dry_run=%s)",
            from_user_id,
            to_user_id,
            dry_run,
        )

        # 1. 验证用户存在
        if not _display_user_status(from_user_id, "源"):
            return

        if not _display_user_status(to_user_id, "目标"):
            return

        # 2. 检查源用户是否有 AppSumo 数据
        from_data = _get_appsumo_data_for_user(from_user_id)
        if not any(
            [
                from_data["licenses"],
                from_data["oauth_token"],
                from_data["purchases"],
                from_data["entitlements"],
            ]
        ):
            click.echo(f"❌ 源用户 {from_user_id} 没有 AppSumo 相关数据")
            return

        # 3. 检查目标用户是否已有 AppSumo 数据
        to_data = _get_appsumo_data_for_user(to_user_id)
        if any(
            [
                to_data["licenses"],
                to_data["oauth_token"],
                to_data["purchases"],
                to_data["entitlements"],
            ]
        ):
            click.echo(
                f"⚠️  目标用户 {to_user_id} 已有 AppSumo 相关数据，可能会产生冲突"
            )
            if not force and not dry_run:
                click.confirm("是否继续？", abort=True)

        # 4. 显示将要执行的操作
        click.echo("\n🔄 将要执行的操作:")
        click.echo(f"  📄 转移 {len(from_data['licenses'])} 个 AppSumo License")
        if from_data["oauth_token"]:
            click.echo("  🔑 转移 1 个 AppSumo OAuth Token")
        click.echo(f"  💰 转移 {len(from_data['purchases'])} 个 AppSumo Purchase")
        click.echo(f"  🎫 转移 {len(from_data['entitlements'])} 个 LTD 权益")

        # 检查目标用户的免费权益
        to_free_entitlements = Entitlement.query.filter(
            Entitlement.user_id == to_user_id,
            Entitlement.source_type == EntitlementSource.FREE_PLAN,
            Entitlement.valid_until > datetime.now(),
        ).all()

        if to_free_entitlements:
            click.echo(f"  ❌ 终结目标用户的 {len(to_free_entitlements)} 个免费权益")

        click.echo("  🆓 为源用户创建免费权益")

        if dry_run:
            click.echo("\n🔍 预览模式 - 不会实际修改数据")
            return

        # 5. 确认操作
        if not force:
            click.confirm(
                f"\n确认将用户 {from_user_id} 的 AppSumo LTD 权益转移到用户 {to_user_id}？",
                abort=True,
            )

        # 6. 执行转移操作
        _perform_transfer(from_user_id, to_user_id, from_data)

        click.echo("\n✅ AppSumo LTD 权益转移完成")

        # 7. 显示转移后的状态
        click.echo("\n📊 转移后状态:")
        _display_user_status(from_user_id, "源")
        _display_user_status(to_user_id, "目标")

    except Exception as e:
        logger.exception("AppSumo LTD 权益转移失败: %s", str(e))
        raise click.ClickException(str(e))


def _perform_transfer(from_user_id, to_user_id, from_data):
    """执行实际的转移操作"""
    with db_transaction():
        current_time = datetime.now()

        # 1. 终结目标用户的免费权益
        to_free_entitlements = Entitlement.query.filter(
            Entitlement.user_id == to_user_id,
            Entitlement.source_type == EntitlementSource.FREE_PLAN,
            Entitlement.valid_until > current_time,
        ).all()

        for entitlement in to_free_entitlements:
            logger.info("终结目标用户免费权益: %s", entitlement.id)
            EntitlementService.terminate_entitlement(
                entitlement, termination_time=current_time
            )

        if to_free_entitlements:
            logger.info(
                "已终结目标用户 %s 的 %s 个免费权益",
                to_user_id,
                len(to_free_entitlements),
            )

        # 2. 转移 AppSumo Licenses
        for license_obj in from_data["licenses"]:
            logger.info("转移 AppSumo License: %s", license_obj.license_key)
            license_obj.user_id = to_user_id

        # 3. 转移 AppSumo OAuth Token
        if from_data["oauth_token"]:
            logger.info(
                "转移 AppSumo OAuth Token: %s", from_data["oauth_token"].license_key
            )
            from_data["oauth_token"].user_id = to_user_id

        # 4. 转移 AppSumo Purchases
        for purchase in from_data["purchases"]:
            logger.info("转移 AppSumo Purchase: %s", purchase.id)
            purchase.user_id = to_user_id

        # 5. 转移 LTD 权益
        for entitlement in from_data["entitlements"]:
            logger.info("转移 LTD 权益: %s", entitlement.id)
            entitlement.user_id = to_user_id

        # 6. 为源用户创建免费权益
        logger.info("为源用户 %s 创建免费权益", from_user_id)
        free_entitlement = EntitlementService.create_free_plan_entitlement(from_user_id)
        if free_entitlement:
            db.session.add(free_entitlement)
            logger.info("成功创建免费权益: %s", free_entitlement.id)
        else:
            logger.error("创建免费权益失败")
            raise RuntimeError("创建免费权益失败")

        logger.info("AppSumo LTD 权益转移完成: %s -> %s", from_user_id, to_user_id)


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )
    # 这里只是为了测试导入，实际使用时通过 Flask CLI 调用
    print("请使用 Flask CLI 调用此命令: flask admin transfer-appsumo-ltd --help")
