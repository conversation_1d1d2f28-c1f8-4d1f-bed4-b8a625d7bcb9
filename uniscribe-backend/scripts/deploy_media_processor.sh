#!/bin/bash

# 媒体预处理消费者部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以 root 权限运行
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# 项目路径
PROJECT_ROOT="/home/<USER>/uniscribe-mono/uniscribe-backend"
SERVICE_NAME="uniscribe-media-processor"
SERVICE_FILE="$PROJECT_ROOT/deploy/systemd/$SERVICE_NAME.service"

log_info "Starting deployment of Uniscribe Media Processor..."

# 1. 检查项目目录
if [ ! -d "$PROJECT_ROOT" ]; then
    log_error "Project directory not found: $PROJECT_ROOT"
    exit 1
fi

# 2. 检查 Python 虚拟环境
if [ ! -f "$PROJECT_ROOT/venv/bin/python" ]; then
    log_error "Python virtual environment not found: $PROJECT_ROOT/venv"
    exit 1
fi

# 3. 检查消费者脚本
if [ ! -f "$PROJECT_ROOT/workers/media_preprocessing_consumer.py" ]; then
    log_error "Media preprocessing consumer script not found"
    exit 1
fi

# 4. 检查服务配置文件
if [ ! -f "$SERVICE_FILE" ]; then
    log_error "Service file not found: $SERVICE_FILE"
    exit 1
fi

# 5. 停止现有服务（如果存在）
if systemctl is-active --quiet $SERVICE_NAME; then
    log_info "Stopping existing $SERVICE_NAME service..."
    systemctl stop $SERVICE_NAME
fi

# 6. 复制服务文件
log_info "Installing systemd service file..."
cp "$SERVICE_FILE" "/etc/systemd/system/"

# 7. 重新加载 systemd
log_info "Reloading systemd daemon..."
systemctl daemon-reload

# 8. 启用服务
log_info "Enabling $SERVICE_NAME service..."
systemctl enable $SERVICE_NAME

# 9. 启动服务
log_info "Starting $SERVICE_NAME service..."
systemctl start $SERVICE_NAME

# 10. 检查服务状态
sleep 2
if systemctl is-active --quiet $SERVICE_NAME; then
    log_info "✅ $SERVICE_NAME service is running successfully!"
    
    # 显示服务状态
    echo ""
    log_info "Service status:"
    systemctl status $SERVICE_NAME --no-pager -l
    
    echo ""
    log_info "Recent logs:"
    journalctl -u $SERVICE_NAME --no-pager -l -n 10
    
else
    log_error "❌ Failed to start $SERVICE_NAME service"
    echo ""
    log_error "Service status:"
    systemctl status $SERVICE_NAME --no-pager -l
    exit 1
fi

echo ""
log_info "🎉 Deployment completed successfully!"
echo ""
log_info "Useful commands:"
echo "  - Check status: sudo systemctl status $SERVICE_NAME"
echo "  - View logs: sudo journalctl -u $SERVICE_NAME -f"
echo "  - Restart: sudo systemctl restart $SERVICE_NAME"
echo "  - Stop: sudo systemctl stop $SERVICE_NAME"
