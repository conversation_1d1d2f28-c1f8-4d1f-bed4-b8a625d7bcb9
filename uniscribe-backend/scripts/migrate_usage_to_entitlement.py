import logging
from datetime import datetime
import click

from models import db
from models.usage import Usage
from models.subscription import Subscription
from services.entitlement_service import EntitlementService
from models.entitlement import Entitlement
from app import app

logger = logging.getLogger(__name__)


def validate_migration():
    """验证迁移结果"""
    try:
        all_usages = Usage.query.all()
        mismatch_count = 0

        for usage in all_usages:
            try:
                # 获取用户的所有有效权益（不考虑是否用完）
                active_entitlements = Entitlement.query.filter(
                    Entitlement.user_id == usage.user_id,
                    Entitlement.valid_until > datetime.now(),
                ).all()

                if not active_entitlements:
                    logger.error(
                        f"No active entitlements found for user {usage.user_id}"
                    )
                    mismatch_count += 1
                    continue

                # 计算总的可用额度
                total_available = sum(
                    entitlement.total_credits - entitlement.consumed_credits
                    for entitlement in active_entitlements
                )

                # 比较新旧系统的额度
                usage_available = (
                    usage.transcription_minutes_quota - usage.transcription_minutes_used
                )

                if total_available != usage_available:
                    mismatch_count += 1
                    logger.error(
                        f"Data mismatch for user {usage.user_id}: "
                        f"usage_data={{"
                        f"used={usage.transcription_minutes_used}, "
                        f"quota={usage.transcription_minutes_quota}, "
                        f"available={usage_available}"
                        f"}}, "
                        f"entitlement_data={{"
                        f"total_available={total_available}"
                        f"}}"
                    )

            except Exception as e:
                logger.error(f"Validation failed for user {usage.user_id}: {str(e)}")
                mismatch_count += 1
                continue

        return mismatch_count

    except Exception as e:
        logger.exception("Validation failed")
        raise


def migrate_usage_to_entitlement():
    """迁移存量 usage 数据到 entitlement 系统"""
    try:
        all_usages = Usage.query.all()
        logger.info(f"Found {len(all_usages)} usage records to migrate")

        success_count = 0
        error_count = 0
        skip_count = 0

        for usage in all_usages:
            try:
                user_id = usage.user_id

                # 检查用户是否已有权益记录
                existing = Entitlement.query.filter_by(user_id=user_id).first()
                if existing:
                    logger.info(f"User {user_id} already has entitlement, skipping")
                    skip_count += 1
                    continue

                # 创建权益
                reference_time = usage.last_reset_time or datetime.now()
                active_subscription = Subscription.get_active_subscription_by_user_id(
                    user_id
                )
                if active_subscription:
                    entitlement = EntitlementService.create_from_subscription(
                        active_subscription, reference_time=reference_time
                    )
                else:
                    entitlement = EntitlementService.create_free_plan_entitlement(
                        user_id, reference_time=reference_time
                    )

                if entitlement:
                    entitlement.consumed_credits = usage.transcription_minutes_used
                    entitlement.updated_time = datetime.now()
                    db.session.add(entitlement)

                    try:
                        db.session.commit()
                        success_count += 1
                        logger.info(
                            f"Migrated usage for user {user_id}: "
                            f"type={'subscription' if active_subscription else 'free'}, "
                            f"usage_data={{"
                            f"used={usage.transcription_minutes_used}, "
                            f"quota={usage.transcription_minutes_quota}, "
                            f"last_reset={usage.last_reset_time}, "
                            f"next_reset={usage.next_reset_time}"
                            f"}}, "
                            f"entitlement_data={{"
                            f"total={entitlement.total_credits}, "
                            f"consumed={entitlement.consumed_credits}, "
                            f"valid_from={entitlement.valid_from}, "
                            f"valid_until={entitlement.valid_until}"
                            f"}}"
                        )
                    except Exception as e:
                        db.session.rollback()
                        error_count += 1
                        logger.error(
                            f"Failed to commit entitlement for user {user_id}: {str(e)}"
                        )

            except Exception as e:
                error_count += 1
                logger.error(f"Failed to migrate user {usage.user_id}: {str(e)}")
                continue

        logger.info(
            f"Migration completed. Success: {success_count}, Failed: {error_count}, Skipped: {skip_count}"
        )

        return {
            "total": len(all_usages),
            "success": success_count,
            "failed": error_count,
            "skipped": skip_count,
            "mismatches": validate_migration(),
        }

    except Exception as e:
        logger.exception("Migration failed")
        db.session.rollback()
        raise


def fix_user_entitlement(user_id):
    """修复指定用户的权益数据（以 usage 为准）"""
    try:
        usage = Usage.get_by_user_id(user_id)
        if not usage:
            logger.error(f"Usage not found for user {user_id}")
            return False

        # 获取当前有效的权益
        active_entitlements = Entitlement.get_active_entitlements(user_id)
        if not active_entitlements:
            logger.error(f"No active entitlements found for user {user_id}")
            return False

        # 以 usage 数据为准进行修复
        for entitlement in active_entitlements:
            entitlement.total_credits = usage.transcription_minutes_quota
            entitlement.consumed_credits = usage.transcription_minutes_used
            entitlement.updated_time = datetime.now()
            logger.info(
                f"Fixed entitlement {entitlement.id} for user {user_id}: "
                f"consumed_credits={usage.transcription_minutes_used}"
            )

        db.session.commit()
        return True

    except Exception as e:
        logger.error(f"Failed to fix entitlement for user {user_id}: {str(e)}")
        db.session.rollback()
        return False


@click.command()
@click.option("--migrate", is_flag=True, help="Migrate all usage data to entitlement")
@click.option("--fix-user", type=int, help="Fix specific user's entitlement data")
def main(migrate, fix_user):
    with app.app_context():
        if migrate:
            logger.info("Starting full migration")
            result = migrate_usage_to_entitlement()
            logger.info("Migration completed")

            # 输出最终报告
            logger.info(
                f"Migration summary:\n"
                f"Total records: {result['total']}\n"
                f"Successfully migrated: {result['success']}\n"
                f"Failed to migrate: {result['failed']}\n"
                f"Skipped: {result['skipped']}\n"
                f"Data mismatches: {result['mismatches']}"
            )

            if result["mismatches"] > 0:
                logger.warning(
                    "Found data mismatches. Please check the logs and use --fix-user option "
                    "to fix specific users if needed."
                )

        elif fix_user:
            logger.info(f"Fixing entitlement for user {fix_user}")
            if fix_user_entitlement(fix_user):
                logger.info(f"Successfully fixed user {fix_user}")
            else:
                logger.error(f"Failed to fix user {fix_user}")


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )
    main()
