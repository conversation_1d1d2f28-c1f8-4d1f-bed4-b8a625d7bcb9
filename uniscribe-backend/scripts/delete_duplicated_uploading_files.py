import click
from models import db, db_transaction
from flask.cli import with_appcontext
from typing import List, <PERSON>ple
from sqlalchemy import text


def get_duplicates() -> List[Tuple[int, str, int]]:
    query = text(
        """
    SELECT user_id, fingerprint, id
    FROM transcription_file 
    WHERE status = 1 AND (user_id, fingerprint) IN (
        SELECT user_id, fingerprint
        FROM transcription_file
        WHERE status = 1
        GROUP BY user_id, fingerprint
        HAVING COUNT(*) > 1
    )
    ORDER BY user_id, fingerprint, id
    """
    )
    return db.session.execute(query).fetchall()


def group_duplicates(rows: List[Tuple[int, str, int]]) -> List[List[int]]:
    # 用字典来按 user_id + fingerprint 分组
    groups_dict = {}

    # 将所有文件按 (user_id, fingerprint) 分组
    for user_id, fingerprint, file_id in rows:
        key = (user_id, fingerprint)
        if key not in groups_dict:
            groups_dict[key] = []
        groups_dict[key].append(file_id)

    # 对每组文件，保留第一个，返回其余的文件ID
    files_to_delete = []
    for file_ids in groups_dict.values():
        # 第一个文件保留，其余的都要删除
        files_to_delete.append(file_ids[1:])

    return files_to_delete


@click.command()
@click.option("--dryrun", is_flag=True, help="Run without making actual changes")
@with_appcontext
def delete_duplicates(dryrun: bool):
    """Delete duplicate files keeping only one file per user_id + fingerprint combination."""
    duplicates = get_duplicates()

    if not duplicates:
        click.echo("No duplicates found.")
        return

    duplicate_groups = group_duplicates(duplicates)
    total_to_delete = sum(len(group) for group in duplicate_groups)

    click.echo(f"Found {total_to_delete} duplicate files to delete")

    if dryrun:
        click.echo("Dry run mode - no changes will be made")
        for group in duplicate_groups:
            click.echo(f"Would delete file IDs: {group}")
        return

    with db_transaction() as session:
        delete_query = text(
            """
        UPDATE transcription_file 
        SET status = 0 
        WHERE id = :file_id
        """
        )
        for group in duplicate_groups:
            for file_id in group:
                session.execute(delete_query, {"file_id": file_id})
                click.echo(f"Deleted file ID: {file_id}")

    click.echo(f"Successfully deleted {total_to_delete} duplicate files")


if __name__ == "__main__":
    delete_duplicates()
