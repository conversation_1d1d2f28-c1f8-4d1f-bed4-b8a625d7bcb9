from enum import Enum
from libs.typed_enum import TypedEnum


class TaskStatus(TypedEnum):
    # TODO: 改成大写
    pending = 1  # 任务已创建，等待执行
    processing = 2  # 处理中
    completed = 3  # 处理完成
    failed = 4  # 处理失败


class TaskType(TypedEnum):
    """
    任务的依赖关系：
    1. 转录任务依赖于音频文件
    2. 其他任务依赖于转录任务，这些任务可以并行执行

    注意：校对任务和关键词任务已废弃
    """

    # TODO: 改成大写

    transcription = 1  # 转录任务
    correction = 2  # 校对任务 (已废弃)
    summary = 3  # 摘要任务
    keywords = 4  # 关键词任务 (已废弃)
    outline = 5  # 大纲任务
    qa_extraction = 6  # 问答任务
    translation = 7  # 翻译任务
    youtube_download = 8  # YouTube 下载任务 (已废弃，有存量数据，保留定义)
    speaker_diarization = 9  # 说话人识别任务
    media_preprocessing = 10  # 媒体预处理任务 (后端队列处理用)


class TaskFeature(TypedEnum):
    text_summary = 1  # 文本摘要任务
    mind_map = 2  # 思维导图任务
    qa_extraction = 3  # 问答任务


class TranscriptionType(str, Enum):
    TRANSCRIPT = "transcript"  # 转录文本
    SUBTITLE = "subtitle"  # 字幕文本


# Task retry constants
MAX_RETRY_COUNT = 3  # Maximum number of retries allowed
SUPPORT_EMAIL = "<EMAIL>"  # Support email for users who need help
