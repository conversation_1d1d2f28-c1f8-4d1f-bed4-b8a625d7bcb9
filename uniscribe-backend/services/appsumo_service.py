import logging
from datetime import datetime, timedelta


from models import db, db_transaction
from models.plan import Plan
from models.purchase import Purchase
from models.entitlement import Entitlement, EntitlementSource
from models.appsumo_license import AppSumoLicense
from models.appsumo_oauth_token import AppSumoOAuthToken
from models.user import User
from services.appsumo_client import AppSumoClient
from services.entitlement_service import EntitlementService
from exceptions.appsumo import (
    InvalidAppSumoLicenseError,
    AppSumoLicenseAlreadyActivatedError,
    AppSumoLicenseActivationError,
    AppSumoMultipleLicensesError,
)
from config import CONFIG
from libs.id_generator import id_generator
from libs.cache import RedisCache
from libs.send_email import send_appsumo_onboarding_email


logger = logging.getLogger(__name__)


class AppSumoService:

    @staticmethod
    @db_transaction()
    def handle_purchase_event(event_data):
        """处理购买事件"""

        license_key = event_data.get("license_key")
        event = event_data.get("event")
        license_status = event_data.get("license_status")
        event_timestamp = event_data.get("event_timestamp")
        created_at = event_data.get("created_at")
        tier = event_data.get("tier", 1)  # 默认为 tier 1

        # 检查 license 是否已存在
        existing_license = AppSumoLicense.get_by_license_key(license_key)
        if existing_license:
            logger.info(f"License already exists: {license_key}")
            return {"success": True, "event": event}

        # 创建新的 license 记录
        license_record = AppSumoLicense(
            id=id_generator.get_id(),
            license_key=license_key,
            tier=tier,
            status=license_status,
            event=event,
            event_timestamp=event_timestamp,
            created_at=created_at,
        )
        db.session.add(license_record)

        return {"success": True, "event": event}

    @staticmethod
    @db_transaction()
    def handle_activate_event(event_data):
        """处理激活事件"""

        license_key = event_data.get("license_key")
        event = event_data.get("event")
        license_status = event_data.get("license_status")
        event_timestamp = event_data.get("event_timestamp")
        created_at = event_data.get("created_at")
        tier = event_data.get("tier", 1)

        # 查找 license 记录
        license_record = AppSumoLicense.get_by_license_key(license_key)
        if not license_record:
            # 如果不存在，创建新记录
            license_record = AppSumoLicense(
                id=id_generator.get_id(),
                license_key=license_key,
                tier=tier,
                status=license_status,
                event=event,
                event_timestamp=event_timestamp,
                created_at=created_at,
            )
            db.session.add(license_record)
        else:
            # 更新现有记录
            license_record.status = license_status
            license_record.event = event
            license_record.event_timestamp = event_timestamp
            license_record.tier = tier

        return {"success": True, "event": event}

    @staticmethod
    @db_transaction()
    def handle_upgrade_event(event_data):
        """处理升级事件"""

        license_key = event_data.get("license_key")
        prev_license_key = event_data.get("prev_license_key")
        event = event_data.get("event")
        license_status = event_data.get("license_status")
        event_timestamp = event_data.get("event_timestamp")
        created_at = event_data.get("created_at")
        tier = event_data.get("tier")

        # 查找之前的 license 记录
        prev_license = AppSumoLicense.get_by_license_key(prev_license_key)
        if not prev_license:
            logger.warning(f"Previous license not found: {prev_license_key}")
            return {"success": True, "event": event}

        # 创建新的 license 记录
        # 如果之前的 license 已被用户激活，则将状态设置为 active
        actual_status = "active" if prev_license.user_id else license_status
        activation_time = prev_license.activation_time if prev_license.user_id else None
        logger.info(
            "Creating new license record: key=%s, prev_key=%s, status=%s -> %s, activation_time=%s ",
            license_key,
            prev_license_key,
            license_status,
            actual_status,
            activation_time,
        )

        license_record = AppSumoLicense(
            id=id_generator.get_id(),
            user_id=prev_license.user_id,  # 继承用户 ID
            license_key=license_key,
            prev_license_key=prev_license_key,
            tier=tier,
            status=actual_status,  # 使用确定的状态
            event=event,
            event_timestamp=event_timestamp,
            activation_time=activation_time,
            created_at=created_at,
        )
        db.session.add(license_record)

        # 如果用户已激活，更新权益
        if prev_license.user_id:
            logger.info(
                "User already activated previous license, updating entitlements: user_id=%s, license_key=%s, tier=%s",
                prev_license.user_id,
                license_key,
                tier,
            )
            AppSumoService.update_user_entitlements(
                prev_license.user_id, license_key, tier
            )

        return {"success": True, "event": event}

    @staticmethod
    @db_transaction()
    def handle_downgrade_event(event_data):
        """处理降级事件"""
        # 与升级事件处理逻辑相同
        return AppSumoService.handle_upgrade_event(event_data)

    @staticmethod
    @db_transaction()
    def handle_deactivate_event(event_data):
        """处理停用事件"""

        license_key = event_data.get("license_key")
        event = event_data.get("event")
        license_status = event_data.get("license_status")
        event_timestamp = event_data.get("event_timestamp")
        created_at = event_data.get("created_at")
        tier = event_data.get("tier")

        # 查找 license 记录
        license_record = AppSumoLicense.get_by_license_key(license_key)
        if not license_record:
            logger.warning(f"License not found: {license_key}")
            return {"success": True, "event": event}

        # 更新 license 状态
        license_record.status = "deactivated"
        license_record.event = event
        license_record.event_timestamp = event_timestamp

        # 如果用户已激活，处理权益
        if license_record.user_id:
            user_id = license_record.user_id

            # 查找相关的 purchase
            purchase = Purchase.query.filter_by(
                stripe_payment_id=f"appsumo_{license_key}"
            ).first()

            if purchase:
                # 终止 LTD 权益
                EntitlementService.terminate_ltd_entitlements(purchase.id)

                # 一次性查询所有有效的付费权益
                current_time = datetime.now()

                # 查询除了当前被终止的 AppSumo 权益外的所有有效付费权益
                other_paid_entitlements = Entitlement.query.filter(
                    Entitlement.user_id == user_id,
                    Entitlement.valid_until > current_time,
                    Entitlement.source_type.in_(
                        [
                            EntitlementSource.SUBSCRIPTION,
                            EntitlementSource.ONE_TIME,
                            EntitlementSource.LTD,
                        ]
                    ),
                    # 排除当前被终止的 AppSumo 权益
                    ~(
                        (Entitlement.source_type == EntitlementSource.LTD)
                        & (Entitlement.source_id == purchase.id)
                    ),
                ).first()

                has_other_paid_entitlements = other_paid_entitlements is not None

                if has_other_paid_entitlements:
                    logger.info(
                        "User %s has other active paid entitlements (type=%s), not creating free plan entitlement",
                        user_id,
                        other_paid_entitlements.source_type,
                    )

                # 只有在没有其他付费权益的情况下才创建免费计划权益
                if not has_other_paid_entitlements:
                    logger.info(
                        "User %s has no other active paid entitlements, creating free plan entitlement",
                        user_id,
                    )
                    free_entitlement = EntitlementService.create_free_plan_entitlement(
                        user_id
                    )
                    if free_entitlement:
                        db.session.add(free_entitlement)

        return {"success": True, "event": event}

    @staticmethod
    @db_transaction()
    def process_oauth_code(code):
        """处理 OAuth 授权码"""
        client = AppSumoClient()

        try:
            # 获取访问令牌
            logger.info("Getting access token from AppSumo")
            token_data = client.get_access_token(code)
            access_token = token_data.get("access_token")
            refresh_token = token_data.get("refresh_token")
            expires_in = token_data.get("expires_in", 3600)

            logger.info(
                "Access token obtained successfully, expires_in: %s", expires_in
            )

            # 获取许可证信息
            logger.info("Getting license key information using access token")
            license_data = client.get_license_key(access_token)
            license_key = license_data.get("license_key")
            license_status = license_data.get("status")

            logger.info(
                "License key information obtained: key=%s, status=%s",
                license_key,
                license_status,
            )

            # 查找 license 记录
            logger.info("Looking up license record for key: %s", license_key)
            license_record = AppSumoLicense.get_by_license_key(license_key)
            if not license_record:
                logger.warning("License not found during OAuth: %s", license_key)
                raise InvalidAppSumoLicenseError(message="Invalid license key")

            logger.info(
                "License record found: tier=%s, status=%s",
                license_record.tier,
                license_record.status,
            )

            # 返回 license 信息，用于后续处理
            result = {
                "license_key": license_key,
                "license_status": license_status,
                "tier": license_record.tier,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_in": expires_in,
            }
            logger.info("OAuth code processing completed successfully")
            return result
        except Exception as e:
            logger.exception("Error processing OAuth code: %s", str(e))
            raise

    @staticmethod
    @db_transaction()
    def activate_user_license(user_id, license_data):
        """激活用户许可证"""
        logger.info("Starting license activation for user_id=%s", user_id)

        license_key = license_data.get("license_key")
        access_token = license_data.get("access_token")
        refresh_token = license_data.get("refresh_token")
        expires_in = license_data.get("expires_in", 3600)
        tier = license_data.get("tier")

        logger.info(
            "License data: key=%s, tier=%s, expires_in=%s",
            license_key,
            tier,
            expires_in,
        )

        # 查找 license 记录
        logger.info("Looking up license record for key: %s", license_key)
        license_record = AppSumoLicense.get_by_license_key(license_key)
        if not license_record:
            logger.warning("License not found during activation: %s", license_key)
            raise InvalidAppSumoLicenseError(message="Invalid license key")

        logger.info(
            "License record found: id=%s, user_id=%s, status=%s, tier=%s",
            license_record.id,
            license_record.user_id,
            license_record.status,
            license_record.tier,
        )

        # 检查 license 是否已被激活
        if license_record.user_id:
            if license_record.user_id != user_id:
                # 被其他用户激活
                logger.warning(
                    "License already activated by another user: %s (current=%s, requested=%s)",
                    license_key,
                    license_record.user_id,
                    user_id,
                )
                raise AppSumoLicenseAlreadyActivatedError(
                    message="License already activated by another user"
                )

            # 被当前用户激活，只更新 OAuth 令牌信息，不重新激活 license 和创建权益
            logger.info(
                "License already activated by current user: %s, only updating OAuth tokens",
                license_key,
            )

            # 只更新 OAuth 令牌
            expires_at = datetime.now() + timedelta(seconds=expires_in)
            oauth_token = AppSumoOAuthToken.get_by_user_id(user_id)
            if oauth_token:
                logger.info("Updating existing OAuth token for user_id=%s", user_id)
                oauth_token.license_key = license_key
                oauth_token.access_token = access_token
                oauth_token.refresh_token = refresh_token
                oauth_token.expires_at = expires_at
            else:
                logger.info("Creating new OAuth token for user_id=%s", user_id)
                oauth_token = AppSumoOAuthToken(
                    id=id_generator.get_id(),
                    user_id=user_id,
                    license_key=license_key,
                    access_token=access_token,
                    refresh_token=refresh_token,
                    expires_at=expires_at,
                )
                db.session.add(oauth_token)

            logger.info("OAuth token updated successfully for user_id=%s", user_id)
            return True

        # 检查用户是否已经激活了其他 AppSumo 许可证
        logger.info("Checking if user has already activated other AppSumo licenses")
        existing_licenses = AppSumoLicense.get_by_user_id(user_id)
        if existing_licenses:
            # 过滤出状态为 active 的许可证
            active_licenses = [
                lic for lic in existing_licenses if lic.status == "active"
            ]
            if active_licenses:
                logger.warning(
                    "User %s has already activated other AppSumo licenses: %s",
                    user_id,
                    [lic.license_key for lic in active_licenses],
                )
                raise AppSumoMultipleLicensesError()

        # 首次激活 license
        logger.info(
            "First-time activation for license: %s, user_id=%s", license_key, user_id
        )

        # 更新 license 记录
        logger.info(
            "Updating license record: setting user_id=%s, status=active", user_id
        )
        license_record.user_id = user_id
        license_record.status = "active"
        license_record.activation_time = datetime.now()

        # 保存 OAuth 令牌
        logger.info("Saving OAuth token for user_id=%s", user_id)
        expires_at = datetime.now() + timedelta(seconds=expires_in)
        oauth_token = AppSumoOAuthToken.get_by_user_id(user_id)
        if oauth_token:
            logger.info("Updating existing OAuth token for user_id=%s", user_id)
            oauth_token.license_key = license_key
            oauth_token.access_token = access_token
            oauth_token.refresh_token = refresh_token
            oauth_token.expires_at = expires_at
        else:
            logger.info("Creating new OAuth token for user_id=%s", user_id)
            oauth_token = AppSumoOAuthToken(
                id=id_generator.get_id(),
                user_id=user_id,
                license_key=license_key,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_at=expires_at,
            )
            db.session.add(oauth_token)

        # 创建权益
        logger.info(
            "Creating entitlements for user_id=%s, license_key=%s, tier=%s",
            user_id,
            license_key,
            tier,
        )
        AppSumoService.update_user_entitlements(user_id, license_key, tier)

        # 发送 AppSumo onboarding 邮件
        AppSumoService.send_onboarding_email(user_id)

        logger.info(
            "License activation completed successfully for user_id=%s, license_key=%s",
            user_id,
            license_key,
        )

        return True

    @staticmethod
    def send_onboarding_email(user_id):
        """
        发送 AppSumo onboarding 邮件，并使用 Redis 缓存记录已发送状态以避免重复发送

        Args:
            user_id: 用户ID
        """
        # 使用 Redis 缓存检查是否已发送
        cache = RedisCache(prefix="appsumo_email")
        cache_key = f"onboarding:{user_id}"

        # 检查是否已发送
        if cache.exists(cache_key):
            logger.info("AppSumo onboarding email already sent to user_id=%s", user_id)
            return

        # 获取用户信息
        user = User.get_by_id(user_id)
        if not user:
            logger.error(
                "User not found for sending AppSumo onboarding email: user_id=%s",
                user_id,
            )
            return

        # 发送邮件
        try:
            logger.info(
                "Sending AppSumo onboarding email to user_id=%s, email=%s",
                user_id,
                user.email,
            )
            # 如果用户没有设置 first_name，使用 "Sumo-ling" 作为 fallback
            first_name = user.first_name.strip() if user.first_name else "Sumo-ling"
            send_appsumo_onboarding_email(user.email, first_name)

            # 设置缓存，永久记录（使用较长的过期时间，如1年）
            cache.set(
                cache_key,
                {"sent": True, "sent_time": datetime.now().isoformat()},
                ttl=365 * 24 * 3600,
            )
            logger.info(
                "AppSumo onboarding email sent successfully to user_id=%s", user_id
            )
        except Exception as e:
            logger.error(
                "Failed to send AppSumo onboarding email to user_id=%s: %s",
                user_id,
                str(e),
            )

    @staticmethod
    def update_user_entitlements(user_id, license_key, tier):
        """更新用户权益"""
        logger.info(
            "Updating entitlements for user_id=%s, license_key=%s, tier=%s",
            user_id,
            license_key,
            tier,
        )

        # 获取对应的计划 ID
        plan_id = CONFIG.APPSUMO_PLAN_MAPPING.get(f"tier{tier}")
        if not plan_id:
            logger.warning("Unknown AppSumo tier: %s", tier)
            raise AppSumoLicenseActivationError(message=f"Unknown AppSumo tier: {tier}")

        logger.info("Mapped tier %s to plan_id=%s", tier, plan_id)

        plan = Plan.get_by_id(plan_id)

        if not plan:
            logger.warning("Plan not found for ID: %s", plan_id)
            raise AppSumoLicenseActivationError(
                message=f"Plan not found for ID: {plan_id}"
            )

        logger.info("Found plan: id=%s, name=%s", plan.id, plan.name)

        # 查找现有的 purchase
        # 使用 stripe_payment_id 字段来存储 AppSumo license key
        stripe_payment_id = f"appsumo_{license_key}"
        logger.info(
            "Looking up existing purchase with stripe_payment_id=%s", stripe_payment_id
        )
        purchase = Purchase.query.filter_by(
            user_id=user_id, stripe_payment_id=stripe_payment_id
        ).first()

        if purchase:
            # 更新现有 purchase
            logger.info(
                "Updating existing purchase: id=%s, plan_id=%s -> %s",
                purchase.id,
                purchase.plan_id,
                plan.id,
            )
            purchase.plan_id = plan.id
        else:
            # 创建新的 purchase
            logger.info(
                "Creating new purchase for user_id=%s, plan_id=%s", user_id, plan.id
            )
            purchase = Purchase(
                id=id_generator.get_id(),
                user_id=user_id,
                plan_id=plan.id,
                quantity=1,
                stripe_payment_id=stripe_payment_id,  # 使用前缀区分 AppSumo 购买
                created_time=datetime.now(),
            )
            db.session.add(purchase)
            logger.info("New purchase created with id=%s", purchase.id)

        # 1. 终止现有的 AppSumo LTD 权益（处理升级/降级情况）
        logger.info(
            "Looking up existing AppSumo LTD entitlements for user_id=%s", user_id
        )
        current_time = datetime.now()
        existing_ltd_entitlements = Entitlement.query.filter(
            Entitlement.user_id == user_id,
            Entitlement.source_type == EntitlementSource.LTD,
            Entitlement.valid_until > current_time,  # 只考虑有效的权益
        ).all()

        logger.info(
            "Found %s existing LTD entitlements", len(existing_ltd_entitlements)
        )

        terminated_ltd_count = 0
        for entitlement in existing_ltd_entitlements:
            # 检查是否是 AppSumo 权益
            related_purchase = Purchase.get_by_id(entitlement.source_id)
            if (
                related_purchase
                and related_purchase.stripe_payment_id
                and related_purchase.stripe_payment_id.startswith("appsumo_")
            ):
                # 终止权益
                logger.info(
                    "Terminating LTD entitlement: id=%s, source_id=%s",
                    entitlement.id,
                    entitlement.source_id,
                )
                entitlement.valid_until = datetime.now()
                entitlement.is_recurring = False
                entitlement.updated_time = datetime.now()
                terminated_ltd_count += 1

        logger.info("Terminated %s AppSumo LTD entitlements", terminated_ltd_count)

        # 2. 终止现有的免费计划权益（处理首次激活情况）
        logger.info(
            "Looking up existing FREE_PLAN entitlements for user_id=%s", user_id
        )
        existing_free_entitlements = Entitlement.query.filter(
            Entitlement.user_id == user_id,
            Entitlement.source_type == EntitlementSource.FREE_PLAN,
            Entitlement.valid_until > current_time,
            Entitlement.is_recurring.is_(True),
        ).all()

        logger.info(
            "Found %s existing FREE_PLAN entitlements", len(existing_free_entitlements)
        )

        terminated_free_count = 0
        for free_entitlement in existing_free_entitlements:
            logger.info(
                "Terminating FREE_PLAN entitlement: id=%s, source_id=%s",
                free_entitlement.id,
                free_entitlement.source_id,
            )
            EntitlementService.terminate_entitlement(
                free_entitlement, termination_time=current_time
            )
            terminated_free_count += 1

        logger.info("Terminated %s FREE_PLAN entitlements", terminated_free_count)

        # 创建新的权益
        logger.info("Creating new entitlement from purchase_id=%s", purchase.id)
        entitlement = EntitlementService.create_from_ltd_purchase(purchase)
        db.session.add(entitlement)

        logger.info(
            "New entitlement created: id=%s, total_credits=%s, valid_until=%s",
            entitlement.id,
            entitlement.total_credits,
            entitlement.valid_until,
        )

        return entitlement
