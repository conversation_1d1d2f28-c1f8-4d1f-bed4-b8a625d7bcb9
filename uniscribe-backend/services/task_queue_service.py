"""
任务队列服务 - 基于 Redis Streams 实现
"""
import json
import logging
import redis
from redis.exceptions import ResponseError
from typing import Dict, Any, Optional
from config import CONFIG

logger = logging.getLogger(__name__)


class TaskQueueService:
    """基于 Redis Streams 的任务队列服务"""
    
    def __init__(self):
        # Redis 配置，使用默认值以防环境变量未设置
        redis_config = CONFIG.REDIS
        host = redis_config.get("host") or "localhost"
        port = redis_config.get("port") or 6379
        password = redis_config.get("password")
        ssl = redis_config.get("ssl", False)

        # 确保端口是整数
        if isinstance(port, str):
            port = int(port)

        self.redis_client = redis.Redis(
            host=host,
            port=port,
            password=password,
            ssl=ssl,
            decode_responses=True
        )
        
        # 队列名称映射（支持优先级）
        self.streams = {
            'media_preprocessing': 'tasks:media_preprocessing',
            'transcription:high': 'tasks:transcription:high',
            'transcription:low': 'tasks:transcription:low',
            'text:high': 'tasks:text:high',
            'text:low': 'tasks:text:low',
            'speaker_diarization:high': 'tasks:speaker_diarization:high',
            'speaker_diarization:low': 'tasks:speaker_diarization:low',
            'retry': 'tasks:retry'
        }
        
        # 初始化消费者组
        self._init_consumer_groups()

    def _get_stream_key(self, task_type: str, priority: int) -> str:
        """根据任务类型和优先级获取队列键名"""
        # 不需要优先级的任务类型
        if task_type in ['media_preprocessing', 'retry']:
            return task_type

        # 需要优先级的任务类型
        if task_type in ['transcription', 'text', 'speaker_diarization']:
            priority_suffix = 'high' if priority == 1 else 'low'
            return f"{task_type}:{priority_suffix}"

        # 兜底，返回原始类型
        return task_type

    def _init_consumer_groups(self):
        """初始化 Redis Streams 消费者组"""
        for stream_name in self.streams.values():
            try:
                # 创建消费者组，从最新消息开始消费
                self.redis_client.xgroup_create(
                    stream_name, 
                    'workers', 
                    id='$', 
                    mkstream=True
                )
            except ResponseError as e:
                # 如果组已存在，忽略错误
                if "BUSYGROUP" not in str(e):
                    raise
    
    def enqueue_task(self, task_type: str, task_data: Dict[str, Any]) -> str:
        """
        将任务加入队列

        Args:
            task_type: 任务类型 ('media_preprocessing', 'transcription', 'text', 'speaker_diarization', 'retry')
            task_data: 任务数据

        Returns:
            消息ID
        """
        # 根据优先级选择队列
        stream_key = self._get_stream_key(task_type, task_data.get('priority', 0))

        if stream_key not in self.streams:
            raise ValueError(f"不支持的任务类型: {stream_key}")

        stream_name = self.streams[stream_key]
        
        # 队列消息标准格式 - 单层结构设计
        # 设计原则：
        # 1. 所有字段都放在外层，简单直接
        # 2. 不能删除现有字段，保证 Go 消费者兼容
        # 3. 可以添加新字段，Go 消费者忽略即可
        # 4. 所有值转为字符串（Redis Streams 要求）
        message_data = {
            # === 核心字段（必需，不可删减） ===
            'task_id': str(task_data.get('id', '')),
            'task_type': str(task_data.get('task_type', '')),
            'file_id': str(task_data.get('file_id', '')),
            'priority': str(task_data.get('priority', 1)),
            'created_time': str(task_data.get('created_time', '')),

            # === 现有字段（兼容 Go 消费者） ===
            'file_url': str(task_data.get('file_url', '')),
            'transcription_file_id': str(task_data.get('file_id', '')),  # 别名，保持兼容
            'transcription_text': str(task_data.get('transcription_text', '')),
            'file_duration': str(task_data.get('file_duration', 0)),
            'language_code': str(task_data.get('language_code', '')),
            'language': str(task_data.get('language', '')),
            'transcription_type': str(task_data.get('transcription_type', '')),
            'requested_service_provider': str(task_data.get('requested_service_provider', '')),

            # === 扩展字段（新增，Go 消费者可忽略） ===
            'user_id': str(task_data.get('user_id', '')),
            'youtube_url': str(task_data.get('youtube_url', '')),
            'title': str(task_data.get('title', '')),
            'preprocessing_type': str(task_data.get('preprocessing_type', '')),
        }
        
        # 移除空值
        message_data = {k: v for k, v in message_data.items() if v is not None and v != ''}
        
        # 添加到 Redis Stream
        message_id = self.redis_client.xadd(stream_name, message_data)

        logger.info(f"任务已加入队列: {stream_name}, 消息ID: {message_id}, 任务ID: {task_data.get('id')}")

        return message_id
    
    def get_queue_length(self, task_type: str) -> int:
        """获取队列长度"""
        if task_type not in self.streams:
            return 0
        
        stream_name = self.streams[task_type]
        try:
            return self.redis_client.xlen(stream_name)
        except ResponseError:
            return 0
    
    def get_all_queue_lengths(self) -> Dict[str, int]:
        """获取所有队列的长度"""
        lengths = {}
        for task_type, stream_name in self.streams.items():
            lengths[task_type] = self.get_queue_length(task_type)
        return lengths

    def get_all_queue_stats(self, consumer_group: str = 'workers') -> Dict[str, Dict[str, int]]:
        """获取所有队列的详细统计信息"""
        stats = {}
        for task_type in self.streams.keys():
            stats[task_type] = {
                'total': self.get_queue_length(task_type),
                'pending': self.get_pending_messages(task_type, consumer_group),
                'unread': self.get_unread_messages(task_type, consumer_group)  # 使用正确的计算方法
            }
        return stats
    
    def get_pending_messages(self, task_type: str, consumer_group: str = 'workers') -> int:
        """获取待处理消息数量（已读取但未确认）"""
        if task_type not in self.streams:
            return 0

        stream_name = self.streams[task_type]
        try:
            # 获取消费者组信息
            groups = self.redis_client.xinfo_groups(stream_name)
            if groups:
                try:
                    for group in groups:
                        if isinstance(group, dict) and group.get('name') == consumer_group:
                            pending = group.get('pending', 0)
                            return pending if isinstance(pending, int) else 0
                except (TypeError, AttributeError):
                    pass
            return 0
        except ResponseError:
            return 0

    def get_unread_messages(self, task_type: str, consumer_group: str = 'workers') -> int:
        """获取未读取消息数量（已入队但未被消费者读取）

        使用 GPT 提供的正确方法：基于 last-delivered-id 计算
        """
        if task_type not in self.streams:
            return 0

        stream_name = self.streams[task_type]
        try:
            # 获取消费者组信息
            try:
                groups = self.redis_client.xinfo_groups(stream_name)
                if not groups:
                    # 如果没有消费者组，返回 stream 总长度
                    length = self.redis_client.xlen(stream_name)
                    return length if isinstance(length, int) else 0
            except ResponseError:
                # 如果消费者组不存在，返回 stream 总长度
                try:
                    length = self.redis_client.xlen(stream_name)
                    return length if isinstance(length, int) else 0
                except ResponseError:
                    return 0

            # 查找目标消费者组（简化处理，避免类型检查问题）
            group_info = None
            try:
                # 直接使用 Redis 命令，避免复杂的类型检查
                for group in groups:
                    if hasattr(group, 'get') and group.get('name') == consumer_group:
                        group_info = group
                        break
                    elif isinstance(group, dict) and group.get('name') == consumer_group:
                        group_info = group
                        break
            except:
                # 如果遍历失败，返回 stream 总长度
                try:
                    length = self.redis_client.xlen(stream_name)
                    return length if isinstance(length, int) else 0
                except ResponseError:
                    return 0

            if not group_info:
                # 如果消费者组不存在，返回 stream 总长度
                try:
                    length = self.redis_client.xlen(stream_name)
                    return length if isinstance(length, int) else 0
                except ResponseError:
                    return 0

            # 获取 last-delivered-id
            try:
                last_delivered_id = group_info.get('last-delivered-id', '0-0')
            except:
                last_delivered_id = '0-0'

            # 如果还没有读取过任何消息，所有消息都是未读的
            if last_delivered_id == '0-0':
                try:
                    length = self.redis_client.xlen(stream_name)
                    return length if isinstance(length, int) else 0
                except ResponseError:
                    return 0

            # 使用 XRANGE 计算从 last-delivered-id 之后的消息数量（GPT 的方法）
            try:
                messages = self.redis_client.xrange(stream_name, min=f"({last_delivered_id}", max='+')
                return len(messages) if isinstance(messages, list) else 0
            except ResponseError:
                # 如果 XRANGE 失败，返回 0（表示没有未读消息）
                return 0

        except ResponseError:
            return 0
    
    def acknowledge_and_delete_message(self, task_type: str, message_id: str, consumer_group: str = 'workers') -> bool:
        """确认并删除消息（节省内存）"""
        logger.info(f"🔍 开始确认并删除消息: task_type={task_type}, message_id={message_id}, consumer_group={consumer_group}")

        if task_type not in self.streams:
            logger.error(f"❌ 任务类型不存在: task_type={task_type}, available_types={list(self.streams.keys())}")
            return False

        stream_name = self.streams[task_type]
        logger.info(f"🔍 使用流: stream_name={stream_name}")

        try:
            # 1. 先确认消息
            logger.info(f"🔍 正在确认消息: stream={stream_name}, group={consumer_group}, message_id={message_id}")
            ack_result = self.redis_client.xack(stream_name, consumer_group, message_id)
            logger.info(f"🔍 确认结果: ack_result={ack_result}")

            if not ack_result:
                logger.warning(f"⚠️ 消息确认失败: stream={stream_name}, message_id={message_id}, ack_result={ack_result}")
                return False

            # 2. 确认成功后再删除消息
            logger.info(f"🔍 正在删除消息: stream={stream_name}, message_id={message_id}")
            del_result = self.redis_client.xdel(stream_name, message_id)
            logger.info(f"🔍 删除结果: del_result={del_result}")

            success = bool(del_result)
            logger.info(f"✅ 消息确认并删除完成: stream={stream_name}, message_id={message_id}, ack={ack_result}, del={del_result}, success={success}")
            return success
        except ResponseError as e:
            logger.error(f"❌ 确认并删除消息异常: stream={stream_name}, message_id={message_id}, error={e}")
            return False
        except Exception as e:
            logger.error(f"❌ 确认并删除消息未知异常: stream={stream_name}, message_id={message_id}, error={e}, type={type(e)}")
            return False

    def cleanup_old_messages(self, task_type: str, keep_count: int = 1000) -> int:
        """
        清理旧消息，严格按照 keep_count 保留最新消息

        策略：
        1. 检查是否有未处理的消息（pending 或 unread）
        2. 如果没有未处理消息，直接按 keep_count 清理
        3. 如果有未处理消息，使用安全清理策略
        """
        if task_type not in self.streams:
            return 0

        stream_name = self.streams[task_type]

        try:
            # 获取当前状态
            current_length = self.redis_client.xlen(stream_name)
            if not isinstance(current_length, int) or current_length <= keep_count:
                return 0

            # 检查是否有未处理的消息
            pending_count = self.get_pending_messages(task_type)
            unread_count = self.get_unread_messages(task_type)

            logger.info(f"清理前状态: stream={stream_name}, total={current_length}, pending={pending_count}, unread={unread_count}")

            # 如果没有未处理的消息，可以安全地按 keep_count 清理
            if pending_count == 0 and unread_count == 0:
                logger.info(f"没有未处理消息，执行直接清理: stream={stream_name}")
                return self._direct_cleanup(stream_name, current_length, keep_count)
            else:
                logger.info(f"存在未处理消息，执行安全清理: stream={stream_name}")
                return self._safe_cleanup(stream_name, task_type)

        except ResponseError as e:
            logger.error(f"清理操作失败: stream={stream_name}, error={e}")
            return 0

    def _direct_cleanup(self, stream_name: str, current_length: int, keep_count: int) -> int:
        """直接清理：删除最旧的消息，保留最新的 keep_count 条"""
        try:
            to_delete = current_length - keep_count

            # 获取最旧的消息
            old_messages = self.redis_client.xrange(stream_name, count=to_delete)
            if not old_messages:
                return 0

            # 提取消息 ID
            message_ids = []
            try:
                for msg in old_messages:
                    if isinstance(msg, (list, tuple)) and len(msg) > 0:
                        message_ids.append(msg[0])
            except:
                logger.error(f"解析消息 ID 失败: stream={stream_name}")
                return 0

            if not message_ids:
                return 0

            # 批量删除
            deleted_count = self.redis_client.xdel(stream_name, *message_ids)

            logger.info(f"直接清理完成: stream={stream_name}, 删除={deleted_count}条, 保留={keep_count}条")
            return deleted_count if isinstance(deleted_count, int) else 0

        except ResponseError as e:
            logger.error(f"直接清理失败: stream={stream_name}, error={e}")
            return 0

    def _safe_cleanup(self, stream_name: str, task_type: str) -> int:
        """安全清理：只删除已确认的消息，保留未处理的消息"""
        consumer_group = 'workers'

        try:
            # 获取消费者组信息
            try:
                groups = self.redis_client.xinfo_groups(stream_name)
            except ResponseError:
                logger.info(f"消费者组不存在，跳过安全清理: stream={stream_name}")
                return 0

            if not groups:
                logger.info(f"没有消费者组，跳过安全清理: stream={stream_name}")
                return 0

            # 查找目标消费者组
            group_info = None
            try:
                for group in groups:
                    if hasattr(group, 'get') and group.get('name') == consumer_group:
                        group_info = group
                        break
                    elif isinstance(group, dict) and group.get('name') == consumer_group:
                        group_info = group
                        break
            except:
                logger.info(f"无法遍历消费者组，跳过安全清理: stream={stream_name}")
                return 0

            if not group_info:
                logger.info(f"目标消费者组不存在，跳过安全清理: stream={stream_name}")
                return 0

            # 获取 last-delivered-id
            try:
                last_delivered_id = group_info.get('last-delivered-id', '0-0')
            except:
                last_delivered_id = '0-0'

            if last_delivered_id == '0-0':
                logger.info(f"还没有读取过消息，跳过安全清理: stream={stream_name}")
                return 0

            # 获取 pending 消息 ID 集合
            try:
                pending_messages = self.redis_client.xpending_range(
                    stream_name, consumer_group, '-', '+', count=10000
                )
                pending_ids = set()
                if pending_messages:
                    try:
                        for pending in pending_messages:
                            if hasattr(pending, 'get'):
                                pending_ids.add(pending.get('message_id', ''))
                            elif isinstance(pending, dict):
                                pending_ids.add(pending.get('message_id', ''))
                            elif isinstance(pending, (list, tuple)) and len(pending) > 0:
                                pending_ids.add(pending[0])
                    except:
                        pass
            except ResponseError:
                logger.warning(f"无法获取 pending 消息，跳过安全清理: stream={stream_name}")
                return 0

            # 获取可以安全删除的消息（已读取且已确认的）
            try:
                old_messages = self.redis_client.xrange(stream_name, min='-', max=last_delivered_id)
                if not old_messages:
                    return 0

                # 过滤出已确认的消息
                safe_to_delete = []
                try:
                    for msg in old_messages:
                        if isinstance(msg, (list, tuple)) and len(msg) > 0:
                            msg_id = msg[0]
                            if msg_id not in pending_ids:
                                safe_to_delete.append(msg_id)
                except:
                    logger.warning(f"解析消息列表失败: stream={stream_name}")
                    return 0

                if not safe_to_delete:
                    return 0

                # 批量删除已确认的消息
                deleted_count = self.redis_client.xdel(stream_name, *safe_to_delete)

                logger.info(f"安全清理完成: stream={stream_name}, 删除={deleted_count}条已确认消息")
                return deleted_count if isinstance(deleted_count, int) else 0

            except ResponseError as e:
                logger.error(f"安全清理失败: stream={stream_name}, error={e}")
                return 0

        except ResponseError as e:
            logger.error(f"安全清理操作失败: stream={stream_name}, error={e}")
            return 0

    def cleanup_all_old_messages(self, keep_count: int = 1000) -> Dict[str, int]:
        """清理所有队列的旧消息"""
        results = {}
        for task_type in self.streams.keys():
            deleted = self.cleanup_old_messages(task_type, keep_count)
            if deleted > 0:
                results[task_type] = deleted
        return results

    def clear_queue(self, task_type: str):
        """清空指定队列（仅用于测试）"""
        if task_type not in self.streams:
            return

        stream_name = self.streams[task_type]
        try:
            # 删除整个 stream
            self.redis_client.delete(stream_name)
            # 重新初始化消费者组
            self._init_consumer_groups()
        except ResponseError:
            pass
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """获取队列统计信息（兼容旧接口格式）"""
        # 获取详细统计
        detailed_stats = self.get_all_queue_stats()

        # 转换为旧格式以保持兼容性
        stats = {
            'queue_lengths': {},
            'pending_messages': {},
            'unread_messages': {},  # 新增：未读取消息统计
            'total_queued': 0,
            'total_pending': 0,
            'total_unread': 0,  # 新增：总未读取消息数
        }

        for task_type, stat in detailed_stats.items():
            stats['queue_lengths'][task_type] = stat['total']
            stats['pending_messages'][task_type] = stat['pending']
            stats['unread_messages'][task_type] = stat['unread']

            stats['total_pending'] += stat['pending']
            stats['total_unread'] += stat['unread']

        stats['total_queued'] = sum(stats['queue_lengths'].values())

        return stats


# 全局队列服务实例
task_queue_service = TaskQueueService()


def enqueue_task(task_type: str, task_data: Dict[str, Any]) -> str:
    """便捷函数：将任务加入队列"""
    return task_queue_service.enqueue_task(task_type, task_data)


def get_queue_stats() -> Dict[str, Any]:
    """便捷函数：获取队列统计信息"""
    return task_queue_service.get_queue_stats()
