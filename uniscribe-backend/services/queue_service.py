import json
import redis
from config import CONFIG


class QueueService:
    _redis_client = None

    @classmethod
    def get_client(cls):
        if cls._redis_client is None:
            cls._redis_client = redis.Redis(
                host=CONFIG.REDIS["host"],
                port=CONFIG.REDIS["port"],
                password=CONFIG.REDIS["password"],
                ssl=CONFIG.REDIS["ssl"],
            )
            cls._redis_client.ping()
        return cls._redis_client

    @classmethod
    def enqueue(cls, queue_name, data):
        """Add an item to the queue"""
        client = cls.get_client()
        return client.lpush(queue_name, json.dumps(data))

    @classmethod
    def dequeue(cls, queue_name):
        """Remove and return an item from the queue"""
        client = cls.get_client()
        data = client.rpop(queue_name)
        return json.loads(data) if data else None
