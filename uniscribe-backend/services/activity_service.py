import logging
from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy.exc import IntegrityError
from models import db
from models.user_activity import UserActivity
from models.user import User
from models.entitlement import Entitlement, EntitlementSource

logger = logging.getLogger(__name__)


class ActivityService:
    """用户活跃记录服务"""

    # 活跃行为记录的最小间隔（5分钟）
    MIN_ACTIVITY_INTERVAL = timedelta(minutes=5)

    @classmethod
    def record_user_activity(cls, user_id: int, activity_type: str = "general") -> bool:
        """记录用户活跃行为

        Args:
            user_id: 用户ID
            activity_type: 活跃行为类型（用于日志记录）

        Returns:
            bool: 是否成功记录活跃行为
        """
        start_time = datetime.utcnow()
        try:
            # 验证 user_id 有效性
            if not user_id or not isinstance(user_id, int) or user_id <= 0:
                logger.warning(f"Invalid user_id for activity recording: {user_id}")
                return False

            current_time = datetime.utcnow()

            # 检查是否需要记录活跃行为（5分钟频率控制）
            if not cls._should_record_activity(user_id, current_time):
                logger.debug(
                    f"Skipping activity record for user {user_id} due to frequency limit"
                )
                return False

            # 创建或更新活跃记录
            UserActivity.create_or_update_activity(user_id, current_time)

            # 提交数据库事务
            db.session.commit()

            # 计算耗时并记录
            end_time = datetime.utcnow()
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.info(
                f"Recorded activity for user {user_id}, type: {activity_type}, time: {current_time}, duration: {duration_ms:.2f}ms"
            )
            return True

        except ValueError as e:
            logger.warning(
                f"Invalid user_id for activity recording: {user_id}, error: {str(e)}"
            )
            return False
        except IntegrityError:
            # 并发冲突是预期内的，静默忽略
            db.session.rollback()
            return False
        except Exception as e:
            end_time = datetime.utcnow()
            duration_ms = (end_time - start_time).total_seconds() * 1000
            logger.error(
                f"Failed to record activity for user {user_id}: {str(e)}, duration: {duration_ms:.2f}ms"
            )
            db.session.rollback()
            return False

    @classmethod
    def _should_record_activity(cls, user_id: int, current_time: datetime) -> bool:
        """检查是否应该记录活跃行为（基于5分钟频率控制）

        Args:
            user_id: 用户ID
            current_time: 当前时间

        Returns:
            bool: 是否应该记录
        """
        # 验证 user_id 有效性
        if not user_id or not isinstance(user_id, int) or user_id <= 0:
            return False

        current_date = current_time.date()

        # 获取用户当天的活跃记录
        existing_record = UserActivity.get_by_user_and_date(user_id, current_date)

        if not existing_record:
            # 如果当天没有记录，直接记录
            return True

        # 检查距离上次活跃时间是否超过5分钟
        time_diff = current_time - existing_record.last_activity_time
        return time_diff >= cls.MIN_ACTIVITY_INTERVAL
