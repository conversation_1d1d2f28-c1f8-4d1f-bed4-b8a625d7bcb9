import re
import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class AlignmentService:
    """说话人识别对齐服务"""

    @staticmethod
    def parse_time_string(time_str: str) -> float:
        """将时间字符串转换为秒数

        Args:
            time_str: 格式如 "0:00:09.779063" 的时间字符串

        Returns:
            秒数（浮点数）
        """
        try:
            # 分割时:分:秒.微秒
            parts = time_str.split(":")
            if len(parts) != 3:
                raise ValueError(f"Invalid time format: {time_str}")

            hours = float(parts[0])
            minutes = float(parts[1])
            seconds = float(parts[2])

            return hours * 3600 + minutes * 60 + seconds
        except Exception as e:
            logger.error(f"Failed to parse time string '{time_str}': {e}")
            return 0.0

    @staticmethod
    def find_best_speaker(
        segment_start: float,
        segment_end: float,
        diarization_segments: List[Dict],
        fill_nearest: bool = False,
    ) -> Optional[str]:
        """使用 WhisperX 算法为转录段找到最佳匹配的说话人

        Args:
            segment_start: 转录段开始时间
            segment_end: 转录段结束时间
            diarization_segments: 说话人识别段列表
            fill_nearest: 是否在没有重叠时选择最近的说话人

        Returns:
            说话人标签，如果没有匹配则返回 None
        """
        if not diarization_segments:
            return None

        try:
            # 转换为 DataFrame
            df_data = []
            for dia_seg in diarization_segments:
                dia_start = AlignmentService.parse_time_string(
                    dia_seg.get("start", "0:00:00")
                )
                dia_end = AlignmentService.parse_time_string(
                    dia_seg.get("stop", "0:00:00")
                )
                df_data.append(
                    {
                        "start": dia_start,
                        "end": dia_end,
                        "speaker": dia_seg.get("speaker", "Unknown"),
                    }
                )

            diarization_df = pd.DataFrame(df_data)

            if diarization_df.empty:
                return None

            # WhisperX 算法：计算交集
            diarization_df = diarization_df.copy()
            diarization_df["intersection"] = np.minimum(
                diarization_df["end"], segment_end
            ) - np.maximum(diarization_df["start"], segment_start)

            # 过滤有效的重叠
            if not fill_nearest:
                dia_tmp = diarization_df[diarization_df["intersection"] > 0]
            else:
                dia_tmp = diarization_df

            if len(dia_tmp) > 0:
                # 如果有重叠，按说话人分组，计算总交集时间，选择交集最大的说话人
                overlapping_segments = dia_tmp[dia_tmp["intersection"] > 0]
                if len(overlapping_segments) > 0:
                    speaker_intersections = (
                        overlapping_segments.groupby("speaker")["intersection"]
                        .sum()
                        .sort_values(ascending=False)
                    )
                    if len(speaker_intersections) > 0:
                        return str(speaker_intersections.index[0])

                # 如果没有重叠但 fill_nearest=True，找最近的说话人
                if fill_nearest:
                    segment_center = (segment_start + segment_end) / 2
                    dia_tmp_copy = dia_tmp.copy()
                    dia_tmp_copy["center"] = (
                        dia_tmp_copy["start"] + dia_tmp_copy["end"]
                    ) / 2
                    dia_tmp_copy["distance"] = abs(
                        dia_tmp_copy["center"] - segment_center
                    )

                    # 选择距离最近的说话人
                    closest_idx = dia_tmp_copy["distance"].idxmin()
                    return str(dia_tmp_copy.loc[closest_idx, "speaker"])

            return None

        except Exception as e:
            logger.warning(f"Error in find_best_speaker: {e}")
            return None

    @staticmethod
    def _smart_join_words(text_parts: List[str]) -> str:
        """智能拼接词语，根据语言特征决定是否添加空格

        Args:
            text_parts: 词语列表

        Returns:
            拼接后的文本
        """
        if not text_parts:
            return ""

        result = []
        for i, word in enumerate(text_parts):
            if i == 0:
                result.append(word)
            else:
                prev_word = text_parts[i - 1]

                # 检查是否需要添加空格
                need_space = AlignmentService._need_space_between_words(prev_word, word)

                if need_space:
                    result.append(" " + word)
                else:
                    result.append(word)

        return "".join(result)

    @staticmethod
    def _has_chinese(text: str) -> bool:
        """检查文本是否包含中文字符"""
        return any("\u4e00" <= char <= "\u9fff" for char in text)

    @staticmethod
    def _has_latin_or_digit(text: str) -> bool:
        """检查文本是否包含英文字母或数字"""
        return any(char.isalnum() and ord(char) < 256 for char in text)

    @staticmethod
    def _need_space_between_words(prev_word: str, current_word: str) -> bool:
        """判断两个词之间是否需要空格

        Args:
            prev_word: 前一个词
            current_word: 当前词

        Returns:
            是否需要空格
        """
        # 如果当前词是标点符号，不需要空格
        if current_word and current_word in ".,!?;:，。！？；：":
            return False

        # 如果前一个词是标点符号，需要空格（除了中文标点）
        if prev_word and prev_word in ".,!?;:":
            return True
        if prev_word and prev_word in "，。！？；：":
            # 中文标点后面，如果跟中文字符则不需要空格
            return not AlignmentService._has_chinese(current_word)

        # 如果两个词都是中文，不需要空格
        if AlignmentService._has_chinese(prev_word) and AlignmentService._has_chinese(
            current_word
        ):
            return False

        # 如果包含英文字母或数字，需要空格
        if AlignmentService._has_latin_or_digit(
            prev_word
        ) or AlignmentService._has_latin_or_digit(current_word):
            return True

        # 默认情况下需要空格
        return True

    @staticmethod
    def assign_word_speakers(
        diarize_df: pd.DataFrame,
        transcript_result: Dict,
        fill_nearest: bool = False,
        boundary_threshold: float = 0.5,
    ) -> Dict:
        """使用 WhisperX 算法为转录结果分配说话人 (改进版，处理边界重叠)

        Args:
            diarize_df: 说话人识别段的 DataFrame，包含 start, end, speaker 列
            transcript_result: 转录结果，包含 segments 列表
            fill_nearest: 是否在没有重叠时选择最近的说话人
            boundary_threshold: 边界区域阈值（秒），在此范围内使用更精细的判断

        Returns:
            更新后的转录结果，包含说话人信息
        """
        transcript_segments = transcript_result.get("segments", [])

        for seg in transcript_segments:
            # assign speaker to segment (if any)
            seg_start = seg.get("start_time", 0.0)
            seg_end = seg.get("end_time", 0.0)
            seg_center = (seg_start + seg_end) / 2

            speaker = AlignmentService._find_best_speaker_for_segment(
                diarize_df,
                seg_start,
                seg_end,
                seg_center,
                fill_nearest,
                boundary_threshold,
            )

            if speaker:
                seg["speaker"] = speaker

            # assign speaker to words
            if "words" in seg:
                for word in seg["words"]:
                    if "start_time" in word:
                        word_start = word["start_time"]
                        word_end = word["end_time"]
                        word_center = (word_start + word_end) / 2

                        word_speaker = AlignmentService._find_best_speaker_for_segment(
                            diarize_df,
                            word_start,
                            word_end,
                            word_center,
                            fill_nearest,
                            boundary_threshold,
                        )

                        if word_speaker:
                            word["speaker"] = word_speaker

        return transcript_result

    @staticmethod
    def _find_best_speaker_for_segment(
        diarize_df: pd.DataFrame,
        start_time: float,
        end_time: float,
        center_time: float,
        fill_nearest: bool = False,
        boundary_threshold: float = 0.5,
    ) -> Optional[str]:
        """为单个段/词找到最佳说话人，处理边界重叠问题

        Args:
            diarize_df: 说话人识别段的 DataFrame
            start_time: 段开始时间
            end_time: 段结束时间
            center_time: 段中心时间
            fill_nearest: 是否在没有重叠时选择最近的说话人
            boundary_threshold: 边界区域阈值

        Returns:
            最佳说话人标签
        """
        diarize_df_copy = diarize_df.copy()
        diarize_df_copy["intersection"] = np.minimum(
            diarize_df_copy["end"], end_time
        ) - np.maximum(diarize_df_copy["start"], start_time)

        # 计算中心点是否在说话人段内
        diarize_df_copy["contains_center"] = (
            diarize_df_copy["start"] <= center_time
        ) & (diarize_df_copy["end"] >= center_time)

        # 计算到边界的距离
        diarize_df_copy["distance_to_boundary"] = np.minimum(
            np.abs(diarize_df_copy["start"] - center_time),
            np.abs(diarize_df_copy["end"] - center_time),
        )

        # remove no hit, otherwise we look for closest
        if not fill_nearest:
            dia_tmp = diarize_df_copy[diarize_df_copy["intersection"] > 0]

            # 如果没有重叠，检查是否在边界附近
            if len(dia_tmp) == 0:
                # 检查边界溢出情况
                boundary_candidates = []
                for i in range(len(diarize_df_copy)):
                    dia_start = diarize_df_copy.iloc[i]["start"]
                    dia_end = diarize_df_copy.iloc[i]["end"]

                    # 检查是否在说话人段开始前的边界内（溢出到前面）
                    if (
                        start_time < dia_start
                        and end_time <= dia_start + boundary_threshold
                        and dia_start - start_time <= boundary_threshold
                    ):
                        boundary_candidates.append(i)

                    # 检查是否在说话人段结束后的边界内（溢出到后面）
                    elif (
                        start_time >= dia_end - boundary_threshold
                        and start_time <= dia_end + boundary_threshold
                        and end_time - dia_end <= boundary_threshold
                    ):
                        boundary_candidates.append(i)

                if boundary_candidates:
                    # 选择距离最近的边界候选
                    min_distance = float("inf")
                    best_candidate = None
                    for idx in boundary_candidates:
                        row = diarize_df_copy.iloc[idx]
                        dia_center = (row["start"] + row["end"]) / 2
                        distance = abs(dia_center - center_time)
                        if distance < min_distance:
                            min_distance = distance
                            best_candidate = idx

                    if best_candidate is not None:
                        dia_tmp = diarize_df_copy.iloc[[best_candidate]]
                    else:
                        dia_tmp = diarize_df_copy.iloc[0:0]  # 空 DataFrame
                else:
                    dia_tmp = diarize_df_copy.iloc[0:0]  # 空 DataFrame
        else:
            dia_tmp = diarize_df_copy

        if len(dia_tmp) == 0:
            return None

        # 检查是否有多个候选说话人（重叠情况）
        valid_speakers = dia_tmp[dia_tmp["intersection"] > 0]

        if len(valid_speakers) <= 1:
            # 只有一个或没有候选，使用标准 WhisperX 算法
            speaker_intersections = (
                dia_tmp.groupby("speaker")["intersection"]
                .sum()
                .sort_values(ascending=False)
            )
            return (
                str(speaker_intersections.index[0])
                if len(speaker_intersections) > 0
                else None
            )

        # 多个候选说话人，使用改进的判断逻辑（考虑段长度权重）
        # 计算每个说话人段的中心点
        valid_speakers_copy = valid_speakers.copy()
        valid_speakers_copy["speaker_center"] = (
            valid_speakers_copy["start"] + valid_speakers_copy["end"]
        ) / 2

        # 计算词中心点到各说话人中心点的距离
        valid_speakers_copy["center_to_center_distance"] = np.abs(
            valid_speakers_copy["speaker_center"] - center_time
        )

        # 计算段长度和长度权重
        valid_speakers_copy["duration"] = (
            valid_speakers_copy["end"] - valid_speakers_copy["start"]
        )
        # 使用对数权重，避免极长段落过度影响
        valid_speakers_copy["duration_weight"] = np.log(
            valid_speakers_copy["duration"] + 1
        )

        # 1. 优先选择包含中心点的说话人
        center_candidates = valid_speakers_copy[valid_speakers_copy["contains_center"]]
        if len(center_candidates) == 1:
            return str(center_candidates.iloc[0]["speaker"])
        if len(center_candidates) > 1:
            # 多个都包含中心点，使用综合评分（距离 + 长度权重）
            center_candidates = center_candidates.copy()

            # 计算综合评分：距离越近越好，长度越长越好
            # 距离分数：1 / (距离 + 0.1) 避免除零
            center_candidates["distance_score"] = 1 / (
                center_candidates["center_to_center_distance"] + 0.1
            )

            # 综合评分：距离分数 * 长度权重
            center_candidates["combined_score"] = (
                center_candidates["distance_score"]
                * center_candidates["duration_weight"]
            )

            # 选择综合评分最高的
            best_candidate_idx = center_candidates["combined_score"].idxmax()
            best_speaker = str(center_candidates.loc[best_candidate_idx, "speaker"])

            # 调试日志：显示评分详情
            logger.debug(
                f"Multiple center candidates for time {center_time:.2f}s. "
                f"Selected speaker {best_speaker} with combined_score="
                f"{center_candidates.loc[best_candidate_idx, 'combined_score']:.3f} "
                f"(distance={center_candidates.loc[best_candidate_idx, 'center_to_center_distance']:.2f}s, "
                f"duration={center_candidates.loc[best_candidate_idx, 'duration']:.2f}s)"
            )

            return best_speaker

        # 2. 没有说话人包含中心点，使用综合评分选择最佳候选
        valid_speakers_copy["distance_score"] = 1 / (
            valid_speakers_copy["center_to_center_distance"] + 0.1
        )
        valid_speakers_copy["combined_score"] = (
            valid_speakers_copy["distance_score"]
            * valid_speakers_copy["duration_weight"]
        )

        best_speaker_idx = valid_speakers_copy["combined_score"].idxmax()
        best_speaker = str(valid_speakers_copy.loc[best_speaker_idx, "speaker"])

        # 调试日志：显示评分详情
        logger.debug(
            f"No center candidates for time {center_time:.2f}s. "
            f"Selected speaker {best_speaker} with combined_score="
            f"{valid_speakers_copy.loc[best_speaker_idx, 'combined_score']:.3f} "
            f"(distance={valid_speakers_copy.loc[best_speaker_idx, 'center_to_center_distance']:.2f}s, "
            f"duration={valid_speakers_copy.loc[best_speaker_idx, 'duration']:.2f}s)"
        )

        return best_speaker

    @staticmethod
    def group_words_by_speaker(
        word_segments: List[Dict],
        max_gap_seconds: float = 1.0,
        min_segment_duration: float = 0.1,
    ) -> List[Dict]:
        """将词级别的段按说话人分组，合并成句子级别的段

        Args:
            word_segments: 已分配说话人的词级别段列表
            max_gap_seconds: 同一说话人词之间的最大时间间隔
            min_segment_duration: 最小段持续时间

        Returns:
            合并后的句子级别段列表
        """
        if not word_segments:
            return []

        # 按时间排序
        sorted_words = sorted(word_segments, key=lambda x: x.get("start_time", 0.0))

        grouped_segments = []
        current_group = []
        current_speaker = None
        segment_id = 0

        for word in sorted_words:
            word_speaker = word.get("speaker", "Unknown")
            word_start = word.get("start_time", 0.0)
            word_text = word.get("text", "").strip()

            # 跳过空文本
            if not word_text:
                continue

            # 检查是否需要开始新的分组
            should_start_new_group = False

            if current_speaker != word_speaker:
                # 说话人变化
                should_start_new_group = True
            elif current_group:
                # 同一说话人，检查时间间隔
                last_word = current_group[-1]
                last_end = last_word.get("end_time", 0.0)
                time_gap = word_start - last_end

                if time_gap > max_gap_seconds:
                    should_start_new_group = True
            else:
                # 第一个词
                should_start_new_group = True

            if should_start_new_group:
                # 保存当前分组
                if current_group and current_speaker:
                    segment = AlignmentService._create_segment_from_word_group(
                        current_group, current_speaker, segment_id, min_segment_duration
                    )
                    if segment:
                        grouped_segments.append(segment)
                        segment_id += 1

                # 开始新分组
                current_group = [word]
                current_speaker = word_speaker
            else:
                # 添加到当前分组
                current_group.append(word)

        # 处理最后一个分组
        if current_group and current_speaker:
            segment = AlignmentService._create_segment_from_word_group(
                current_group, current_speaker, segment_id, min_segment_duration
            )
            if segment:
                grouped_segments.append(segment)

        logger.info(
            f"Grouped {len(word_segments)} words into {len(grouped_segments)} segments"
        )
        return grouped_segments

    @staticmethod
    def _create_segment_from_word_group(
        words: List[Dict], speaker: str, segment_id: int, min_duration: float = 0.1
    ) -> Optional[Dict]:
        """从词组创建段

        Args:
            words: 词列表
            speaker: 说话人
            segment_id: 段ID
            min_duration: 最小持续时间

        Returns:
            段字典或None
        """
        if not words:
            return None

        # 按时间排序
        sorted_words = sorted(words, key=lambda x: x.get("start_time", 0.0))

        start_time = sorted_words[0].get("start_time", 0.0)
        end_time = sorted_words[-1].get("end_time", 0.0)

        # 检查最小持续时间
        if end_time - start_time < min_duration:
            return None

        # 拼接文本
        text_parts = [word.get("text", "").strip() for word in sorted_words]
        text_parts = [part for part in text_parts if part]  # 过滤空文本

        if not text_parts:
            return None

        # 智能拼接文本
        combined_text = AlignmentService._smart_join_words(text_parts)

        return {
            "id": segment_id,
            "start_time": start_time,
            "end_time": end_time,
            "text": combined_text,
            "speaker": speaker,
            "words": [
                {
                    "start_time": word.get("start_time", 0.0),
                    "end_time": word.get("end_time", 0.0),
                    "text": word.get("text", ""),
                    "speaker": speaker,
                }
                for word in sorted_words
            ],
        }

    @staticmethod
    def align_segments(
        transcription_segments: List[Dict], diarization_data: Dict
    ) -> List[Dict]:
        """对齐转录段和说话人识别结果

        Args:
            transcription_segments: 转录段列表
            diarization_data: 说话人识别数据，包含 segments 和 speakers

        Returns:
            对齐后的段列表，每个段包含说话人信息
        """
        if not transcription_segments or not diarization_data:
            logger.warning("Empty transcription segments or diarization data")
            return transcription_segments or []

        diarization_segments = diarization_data.get("segments", [])
        if not diarization_segments:
            logger.warning("No diarization segments found")
            return transcription_segments

        aligned_segments = []

        for segment in transcription_segments:
            try:
                # 复制原始段数据
                aligned_segment = segment.copy()

                # 获取时间信息
                start_time = segment.get("start_time", 0.0)
                end_time = segment.get("end_time", 0.0)

                # 找到最佳匹配的说话人
                speaker = AlignmentService.find_best_speaker(
                    start_time, end_time, diarization_segments
                )

                # 如果没有找到重叠的说话人，尝试使用最近的说话人
                if not speaker:
                    speaker = AlignmentService.find_best_speaker(
                        start_time, end_time, diarization_segments, fill_nearest=True
                    )

                # 添加说话人信息
                if speaker:
                    aligned_segment["speaker"] = speaker
                else:
                    # 如果仍然没有找到匹配的说话人，使用默认值
                    aligned_segment["speaker"] = "Unknown"
                    logger.warning(
                        f"No speaker found for segment {segment.get('id', 'unknown')} "
                        f"({start_time:.2f}-{end_time:.2f}s), using Unknown"
                    )

                # 处理单词级别的说话人分配（如果存在）
                if "words" in segment and segment["words"]:
                    aligned_words = []
                    for word in segment["words"]:
                        aligned_word = word.copy()
                        word_start = word.get("start_time", start_time)
                        word_end = word.get("end_time", end_time)

                        word_speaker = AlignmentService.find_best_speaker(
                            word_start, word_end, diarization_segments
                        )

                        if word_speaker:
                            aligned_word["speaker"] = word_speaker
                        else:
                            aligned_word["speaker"] = aligned_segment["speaker"]

                        aligned_words.append(aligned_word)

                    aligned_segment["words"] = aligned_words

                aligned_segments.append(aligned_segment)

            except Exception as e:
                logger.error(f"Error aligning segment {segment}: {e}")
                # 如果对齐失败，保留原始段但添加未知说话人
                fallback_segment = segment.copy()
                fallback_segment["speaker"] = "Unknown"
                aligned_segments.append(fallback_segment)

        logger.info(f"Successfully aligned {len(aligned_segments)} segments")
        return aligned_segments

    @staticmethod
    def align_segments_whisperx(
        transcription_segments: List[Dict],
        diarization_data: Dict,
        group_by_speaker: bool = True,
        max_gap_seconds: float = 1.0,
    ) -> List[Dict]:
        """使用 WhisperX 算法对齐转录段和说话人识别结果

        Args:
            transcription_segments: 转录段列表
            diarization_data: 说话人识别数据，包含 segments 和 speakers
            group_by_speaker: 是否按说话人分组合并词级别的段
            max_gap_seconds: 同一说话人词之间的最大时间间隔

        Returns:
            对齐后的段列表，每个段包含说话人信息
        """
        if not transcription_segments or not diarization_data:
            logger.warning("Empty transcription segments or diarization data")
            return transcription_segments or []

        diarization_segments = diarization_data.get("segments", [])
        if not diarization_segments:
            logger.warning("No diarization segments found")
            return transcription_segments

        try:
            # 转换说话人识别数据为 DataFrame
            df_data = []
            for dia_seg in diarization_segments:
                dia_start = AlignmentService.parse_time_string(
                    dia_seg.get("start", "0:00:00")
                )
                dia_end = AlignmentService.parse_time_string(
                    dia_seg.get("stop", "0:00:00")
                )
                df_data.append(
                    {
                        "start": dia_start,
                        "end": dia_end,
                        "speaker": dia_seg.get("speaker", "Unknown"),
                    }
                )

            diarization_df = pd.DataFrame(df_data)

            # 构造转录结果格式
            transcript_result = {"segments": transcription_segments}

            # 使用 WhisperX 算法分配说话人
            # 先不使用 fill_nearest，保持原有的精确匹配
            aligned_result = AlignmentService.assign_word_speakers(
                diarization_df, transcript_result, fill_nearest=False
            )

            aligned_segments = aligned_result["segments"]

            # 后处理：只对真正的 Unknown 使用最近说话人策略
            aligned_segments = AlignmentService._post_process_unknown_speakers(
                aligned_segments, diarization_df
            )

            # 检查是否需要按说话人分组
            if group_by_speaker:
                # 检查是否有 words 数据，如果有则提取 words 并重新分组
                has_words = any(
                    "words" in seg and seg["words"] for seg in aligned_segments
                )

                if has_words:
                    logger.info(
                        "Detected word-level data, extracting words and regrouping by speaker"
                    )
                    # 提取所有已分配说话人的 words
                    all_words = []
                    for seg in aligned_segments:
                        for word in seg.get("words", []):
                            if word.get("text", "").strip():  # 跳过空文本
                                all_words.append(word)

                    logger.info(
                        f"Extracted {len(all_words)} words, regrouping by speaker"
                    )
                    # 基于说话人重新分组
                    aligned_segments = AlignmentService.group_words_by_speaker(
                        all_words, max_gap_seconds=max_gap_seconds
                    )
                else:
                    logger.info(
                        "No word-level data, keeping original segment structure"
                    )

            logger.info(
                f"Successfully aligned {len(aligned_segments)} segments using WhisperX algorithm"
            )

            # 应用说话人分配优化
            optimized_segments = AlignmentService._optimize_speaker_assignments(
                aligned_segments
            )
            logger.info(
                f"Applied speaker assignment optimization to {len(optimized_segments)} segments"
            )

            return optimized_segments

        except Exception as e:
            logger.error(f"Error in WhisperX alignment: {e}")
            # 降级到原有算法
            return AlignmentService.align_segments(
                transcription_segments, diarization_data
            )

    @staticmethod
    def _post_process_unknown_speakers(
        segments: List[Dict], diarization_df: pd.DataFrame
    ) -> List[Dict]:
        """后处理：智能处理 Unknown 说话人，考虑上下文和边界溢出

        Args:
            segments: 对齐后的段落列表
            diarization_df: 说话人识别数据 DataFrame

        Returns:
            处理后的段落列表
        """
        if diarization_df.empty:
            return segments

        processed_segments = []

        for segment in segments:
            if not isinstance(segment, dict):
                processed_segments.append(segment)
                continue

            # 检查是否有 words 数据
            if "words" in segment and segment["words"]:
                # 处理词级别的 Unknown，使用上下文感知策略
                processed_words = AlignmentService._process_words_with_context(
                    segment["words"], diarization_df
                )

                segment_copy = segment.copy()
                segment_copy["words"] = processed_words
                processed_segments.append(segment_copy)
            else:
                # 处理段级别的 Unknown
                if segment.get("speaker") == "Unknown":
                    segment_start = segment.get("start_time", 0)
                    segment_end = segment.get("end_time", 0)

                    # 使用智能策略选择说话人
                    best_speaker = AlignmentService._find_best_speaker_for_unknown(
                        segment_start, segment_end, diarization_df
                    )

                    segment_copy = segment.copy()
                    segment_copy["speaker"] = best_speaker
                    processed_segments.append(segment_copy)

                    logger.debug(
                        f"Assigned Unknown segment {segment_start:.2f}-{segment_end:.2f}s to speaker {best_speaker}"
                    )
                else:
                    processed_segments.append(segment)

        return processed_segments

    @staticmethod
    def _process_words_with_context(
        words: List[Dict], diarization_df: pd.DataFrame
    ) -> List[Dict]:
        """使用上下文感知策略处理词级别的 Unknown 说话人

        Args:
            words: 词列表
            diarization_df: 说话人识别数据 DataFrame

        Returns:
            处理后的词列表
        """
        processed_words = []

        for i, word in enumerate(words):
            if word.get("speaker") == "Unknown":
                word_start = word.get("start_time", 0)
                word_end = word.get("end_time", 0)

                # 策略1: 检查前后词的说话人（上下文感知）
                context_speaker = AlignmentService._get_context_speaker(words, i)

                if context_speaker:
                    # 如果上下文有明确的说话人，使用上下文说话人
                    word_copy = word.copy()
                    word_copy["speaker"] = context_speaker
                    processed_words.append(word_copy)

                    logger.debug(
                        f"Assigned Unknown word '{word.get('text', '')}' at {word_start:.2f}s to context speaker {context_speaker}"
                    )
                else:
                    # 策略2: 使用智能距离策略
                    best_speaker = AlignmentService._find_best_speaker_for_unknown(
                        word_start, word_end, diarization_df
                    )

                    word_copy = word.copy()
                    word_copy["speaker"] = best_speaker
                    processed_words.append(word_copy)

                    logger.debug(
                        f"Assigned Unknown word '{word.get('text', '')}' at {word_start:.2f}s to nearest speaker {best_speaker}"
                    )
            else:
                processed_words.append(word)

        return processed_words

    @staticmethod
    def _get_context_speaker(words: List[Dict], current_index: int) -> Optional[str]:
        """获取当前词的上下文说话人

        Args:
            words: 词列表
            current_index: 当前词的索引

        Returns:
            上下文说话人，如果没有则返回 None
        """
        # 检查前后各3个词的说话人
        context_range = 3
        speakers = []

        # 检查前面的词
        for i in range(max(0, current_index - context_range), current_index):
            speaker = words[i].get("speaker")
            if speaker and speaker != "Unknown":
                speakers.append(speaker)

        # 检查后面的词
        for i in range(
            current_index + 1, min(len(words), current_index + context_range + 1)
        ):
            speaker = words[i].get("speaker")
            if speaker and speaker != "Unknown":
                speakers.append(speaker)

        if speakers:
            # 返回最常见的说话人
            from collections import Counter

            most_common = Counter(speakers).most_common(1)
            return most_common[0][0]

        return None

    @staticmethod
    def _find_best_speaker_for_unknown(
        start_time: float, end_time: float, diarization_df: pd.DataFrame
    ) -> str:
        """为 Unknown 时间段找到最佳说话人，考虑边界溢出情况

        Args:
            start_time: 开始时间
            end_time: 结束时间
            diarization_df: 说话人识别数据 DataFrame

        Returns:
            最佳说话人
        """
        if diarization_df.empty:
            return "Unknown"

        segment_center = (start_time + end_time) / 2

        # 策略1: 检查是否在某个说话人段的边界附近（0.5秒内）
        boundary_threshold = 0.5

        # 转换为列表进行处理，避免 pandas 比较问题
        segments = []
        for i in range(len(diarization_df)):
            row = diarization_df.iloc[i]
            segments.append(
                {
                    "start": row["start"],
                    "end": row["end"],
                    "speaker": row["speaker"],
                }
            )

        for seg in segments:
            dia_start = seg["start"]
            dia_end = seg["end"]
            dia_speaker = seg["speaker"]

            # 检查是否在说话人段开始前的边界内（溢出到前面）
            # 这种情况通常是句子开头的词汇溢出到说话人识别范围之前
            if (
                start_time < dia_start
                and end_time <= dia_start + boundary_threshold
                and dia_start - start_time <= boundary_threshold
            ):
                logger.debug(
                    f"Found boundary overflow (before): {start_time:.2f}-{end_time:.2f}s near speaker {dia_speaker} segment {dia_start:.2f}-{dia_end:.2f}s"
                )
                return dia_speaker

            # 检查是否在说话人段结束后的边界内（溢出到后面）
            if (
                start_time >= dia_end - boundary_threshold
                and start_time <= dia_end + boundary_threshold
                and end_time - dia_end <= boundary_threshold
            ):
                logger.debug(
                    f"Found boundary overflow (after): {start_time:.2f}-{end_time:.2f}s near speaker {dia_speaker} segment {dia_start:.2f}-{dia_end:.2f}s"
                )
                return dia_speaker

        # 策略2: 如果不在边界内，使用最近距离 + 段长度权重
        best_score = float("-inf")  # 使用最高分数而不是最小距离
        closest_speaker = "Unknown"

        for seg in segments:
            seg_center = (seg["start"] + seg["end"]) / 2
            distance = abs(seg_center - segment_center)

            # 计算距离得分 (距离越近得分越高)
            distance_score = max(0, 10 - distance)  # 10秒内的距离转换为正分数

            # 计算段长度权重 (段越长权重越高)
            segment_length = seg["end"] - seg["start"]
            length_weight = min(
                segment_length / 10.0, 2.0
            )  # 最大权重为2.0，10秒以上的段获得最大权重

            # 计算最终得分：距离得分 + 段长度权重
            final_score = distance_score + length_weight

            logger.debug(
                f"Speaker {seg['speaker']}: distance={distance:.3f}, distance_score={distance_score:.3f}, "
                f"segment_length={segment_length:.3f}s, length_weight={length_weight:.3f}, "
                f"final_score={final_score:.3f}"
            )

            if final_score > best_score:
                best_score = final_score
                closest_speaker = seg["speaker"]

        logger.debug(
            f"Selected speaker {closest_speaker} with best score {best_score:.3f}"
        )
        return closest_speaker

    @staticmethod
    def validate_diarization_data(diarization_data: Dict) -> bool:
        """验证说话人识别数据格式

        Args:
            diarization_data: 说话人识别数据

        Returns:
            是否有效
        """
        if not isinstance(diarization_data, dict):
            return False

        segments = diarization_data.get("segments", [])
        if not isinstance(segments, list):
            return False

        # 检查每个段是否有必要的字段
        for segment in segments:
            if not isinstance(segment, dict):
                return False
            if (
                "start" not in segment
                or "stop" not in segment
                or "speaker" not in segment
            ):
                return False

        return True

    @staticmethod
    def _optimize_speaker_assignments(segments: List[Dict]) -> List[Dict]:
        """优化说话人分配，修复可能的错误分配

        Args:
            segments: 对齐后的段落列表

        Returns:
            优化后的段落列表
        """
        # 配置开关
        ENABLE_SPEAKER_OPTIMIZATION = True
        if not ENABLE_SPEAKER_OPTIMIZATION:
            return segments

        if len(segments) < 3:  # 至少需要3个段落才能进行优化
            return segments

        optimized_segments = []
        optimization_count = 0

        for i, segment in enumerate(segments):
            current_segment = segment.copy()

            # 检查是否需要优化当前段落
            if AlignmentService._should_optimize_segment(segments, i):
                prev_segment = segments[i - 1] if i > 0 else None
                next_segment = segments[i + 1] if i < len(segments) - 1 else None

                # 获取前后段落的说话人
                if prev_segment and next_segment:
                    prev_speaker = prev_segment.get("speaker")
                    next_speaker = next_segment.get("speaker")
                    current_speaker = current_segment.get("speaker")

                    # 如果前后说话人相同，且与当前不同，考虑优化
                    if (
                        prev_speaker == next_speaker
                        and current_speaker != prev_speaker
                        and prev_speaker
                        and next_speaker
                    ):

                        # 应用段长度权重决策
                        should_reassign = AlignmentService._should_reassign_speaker(
                            current_segment, prev_segment, next_segment
                        )

                        if should_reassign:
                            old_speaker = current_speaker
                            current_segment["speaker"] = prev_speaker
                            optimization_count += 1

                            start_time = current_segment.get(
                                "start_time", current_segment.get("start", 0)
                            )
                            end_time = current_segment.get(
                                "end_time", current_segment.get("end", 0)
                            )
                            duration = end_time - start_time

                            logger.info(
                                f"Optimized speaker assignment: {old_speaker} -> {prev_speaker} "
                                f"for segment {start_time:.2f}-{end_time:.2f}s (duration: {duration:.2f}s)"
                            )

            optimized_segments.append(current_segment)

        if optimization_count > 0:
            logger.info(
                f"Speaker assignment optimization completed: {optimization_count} segments optimized"
            )

        return optimized_segments

    @staticmethod
    def _should_optimize_segment(segments: List[Dict], index: int) -> bool:
        """判断是否应该优化指定段落的说话人分配

        Args:
            segments: 段落列表
            index: 当前段落索引

        Returns:
            是否应该优化
        """
        if index <= 0 or index >= len(segments) - 1:
            return False  # 第一个或最后一个段落不优化

        current_segment = segments[index]

        # 只优化较短的段落（< 3秒）
        start_time = current_segment.get("start_time", current_segment.get("start", 0))
        end_time = current_segment.get("end_time", current_segment.get("end", 0))
        duration = end_time - start_time

        if duration >= 3.0:
            return False

        # 确保有说话人信息
        current_speaker = current_segment.get("speaker")
        if not current_speaker:
            return False

        return True

    @staticmethod
    def _should_reassign_speaker(
        current_segment: Dict, prev_segment: Dict, next_segment: Dict
    ) -> bool:
        """基于段长度权重决定是否重新分配说话人

        Args:
            current_segment: 当前段落
            prev_segment: 前一个段落
            next_segment: 后一个段落

        Returns:
            是否应该重新分配
        """
        # 计算段落长度
        current_duration = current_segment.get(
            "end_time", current_segment.get("end", 0)
        ) - current_segment.get("start_time", current_segment.get("start", 0))
        prev_duration = prev_segment.get(
            "end_time", prev_segment.get("end", 0)
        ) - prev_segment.get("start_time", prev_segment.get("start", 0))
        next_duration = next_segment.get(
            "end_time", next_segment.get("end", 0)
        ) - next_segment.get("start_time", next_segment.get("start", 0))

        # 计算相邻段落的平均长度
        adjacent_avg_duration = (prev_duration + next_duration) / 2

        # 如果当前段落明显比相邻段落短，更可能是错误分配
        duration_ratio = (
            current_duration / adjacent_avg_duration if adjacent_avg_duration > 0 else 1
        )

        # 保守策略：只有当前段落明显较短时才重新分配
        # 比如当前段落长度小于相邻段落平均长度的30%
        if duration_ratio < 0.3:
            logger.debug(
                f"Segment duration ratio {duration_ratio:.3f} suggests reassignment "
                f"(current: {current_duration:.2f}s, adjacent avg: {adjacent_avg_duration:.2f}s)"
            )
            return True

        return False
