import requests
from config import CONFIG
from typing import Dict
import logging
from libs.rate_limit import rate_limit, MemoryRateLimitBackend

logger = logging.getLogger(__name__)


class UsercheckClient:
    def __init__(self):
        self.api_key = CONFIG.USERCHECK.get("api_key")
        self.base_url = "https://api.usercheck.com"

    @rate_limit(
        max_requests=1,
        window_size=1.0,
        strategy="fixed_window",
        key_prefix="usercheck_api",
        backend=MemoryRateLimitBackend(),
        wait=False,
    )
    def check_domain(self, domain: str) -> Dict:
        """
        检查域名是否为一次性邮箱

        注意：此方法有频率限制，每秒最多1次请求，符合 Usercheck API 要求

        Args:
            domain: 要检查的域名

        Returns:
            Dict: {
                "domain": str,
                "is_disposable": bool,
                "has_mx": bool,
                "is_public_domain": bool,
                "relay_domain": bool,
                "spam": bool
            }

        Raises:
            requests.exceptions.RequestException: API 调用失败
        """
        try:
            response = requests.get(
                f"{self.base_url}/domain/{domain}",
                headers={"Authorization": f"Bearer {self.api_key}"},
                timeout=5,  # 设置超时时间
            )

            # 处理 HTTP 状态码
            if response.status_code == 429:
                logger.warning(f"Rate limit exceeded for domain check: {domain}")
                raise Exception("API rate limit exceeded")

            if response.status_code == 400:
                logger.error(f"Invalid domain format: {domain}")
                raise ValueError("Invalid domain format")

            response.raise_for_status()
            data = response.json()

            # 转换为统一的返回格式
            return {
                "domain": data["domain"],
                "is_disposable": data["disposable"],
                "has_mx": data["mx"],
                "is_public_domain": data["public_domain"],
                "relay_domain": data.get("relay_domain", False),
                "spam": data.get("spam", False),
            }

        except requests.exceptions.Timeout:
            logger.error(f"Timeout while checking domain: {domain}")
            raise
        except requests.exceptions.RequestException as e:
            logger.error(f"Error checking domain {domain}: {str(e)}")
            raise
