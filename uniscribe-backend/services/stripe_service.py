import logging

import stripe

from config import CONFIG

stripe.api_key = CONFIG.STRIPE["secret_key"]

logger = logging.getLogger(__name__)


class StripeService:
    @staticmethod
    def create_checkout_session(price_id, quantity, mode, customer_id, discounts=None, metadata=None):
        checkout_session_params = {
            "line_items": [
                {
                    "price": price_id,
                    "quantity": quantity,
                }
            ],
            "mode": mode,
            "customer": customer_id,
            "success_url": f"{CONFIG.HOST}/dashboard",
            "cancel_url": f"{CONFIG.HOST}/?utm_source=navbar#subscription-price",
        }

        if discounts:
            checkout_session_params["discounts"] = discounts
        else:
            checkout_session_params["allow_promotion_codes"] = True

        if metadata:
            checkout_session_params["metadata"] = metadata
            # For subscription mode, also pass metadata to the subscription
            if mode == "subscription":
                checkout_session_params["subscription_data"] = {"metadata": metadata}
            # For payment mode, pass metadata to payment intent
            elif mode == "payment":
                checkout_session_params["payment_intent_data"] = {"metadata": metadata}

        return stripe.checkout.Session.create(**checkout_session_params)

    @staticmethod
    def get_price_id_by_lookup_key(lookup_key):
        prices = stripe.Price.list(lookup_keys=[lookup_key])
        return prices.data[0].id

    @staticmethod
    def create_customer(email, metadata=None):
        customer_params = {"email": email}
        if metadata is not None:
            customer_params["metadata"] = metadata
        customer = stripe.Customer.create(**customer_params)
        return customer.id

    @staticmethod
    def parse_event(payload, sig_header):
        endpoint_secret = CONFIG.STRIPE["webhook_secret"]
        try:
            event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
        except ValueError as e:
            # Invalid payload
            raise ValueError("Invalid payload")
        except stripe.error.SignatureVerificationError as e:
            # Invalid signature
            raise ValueError("Invalid signature")

        return event

    @staticmethod
    def create_portal_session(customer_id):
        return stripe.billing_portal.Session.create(
            customer=customer_id, return_url=f"{CONFIG.HOST}/dashboard"
        )

    @staticmethod
    def retrieve_subscription(subscription_id):
        return stripe.Subscription.retrieve(subscription_id)

    @staticmethod
    def retrieve_customer(customer_id):
        return stripe.Customer.retrieve(customer_id)

    @staticmethod
    def get_promotion_code_id(promotion_code):
        promotion_codes = stripe.PromotionCode.list(code=promotion_code, active=True)
        if not promotion_codes.data:
            raise ValueError("Invalid or inactive promotion code")
        return promotion_codes.data[0].id
