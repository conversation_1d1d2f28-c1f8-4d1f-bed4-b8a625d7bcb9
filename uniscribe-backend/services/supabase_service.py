import time
import logging

from supabase import create_client
from gotrue.errors import AuthRetryableError

from config import CONFIG

supabase = create_client(CONFIG.SUPABASE["url"], CONFIG.SUPABASE["anon_key"])

logger = logging.getLogger(__name__)


class SupabaseService:

    @staticmethod
    def get_user_by_token(token, max_retries=3, delay=0.5):
        for attempt in range(max_retries):
            try:
                user_response = supabase.auth.get_user(token)
                if user_response.user:
                    return user_response.user.model_dump()
                return None
            except AuthRetryableError as e:
                logger.exception("supabase AuthRetryableError")
                if attempt == max_retries - 1:
                    raise
                time.sleep(delay)
        return None

    @staticmethod
    def sign_out():
        return supabase.auth.sign_out()

    @staticmethod
    def refresh_session(refresh_token):
        return supabase.auth.refresh_session(refresh_token)
