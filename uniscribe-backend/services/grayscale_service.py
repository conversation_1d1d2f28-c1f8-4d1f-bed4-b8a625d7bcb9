"""
灰度发布服务 - 控制任务消费方式的灰度切换
"""
import os
from typing import Dict, Any


class GrayscaleService:
    """灰度发布服务"""

    def __init__(self):
        # 从环境变量读取灰度配置
        self.queue_mode_enabled = os.getenv('ENABLE_QUEUE_MODE', 'false').lower() == 'true'
        self.queue_mode_percentage = int(os.getenv('QUEUE_MODE_PERCENTAGE', '0'))
        self.queue_mode_user_whitelist = self._parse_user_whitelist()

    def _parse_user_whitelist(self):
        """解析用户白名单"""
        whitelist_str = os.getenv('QUEUE_MODE_USER_WHITELIST', '')
        if not whitelist_str:
            return set()
        return set(int(uid.strip()) for uid in whitelist_str.split(',') if uid.strip().isdigit())
    
    def should_use_queue_mode(self, user_id: int, file_id: int, task_type: int) -> bool:
        """
        灰度规则：决定是否使用队列模式

        Args:
            user_id: 用户ID
            file_id: 文件ID
            task_type: 任务类型（暂未使用，保留接口兼容性）

        Returns:
            bool: True 使用队列模式，False 使用 HTTP 轮询模式
        """

        # 如果队列模式未启用，直接返回 False
        if not self.queue_mode_enabled:
            return False

        # 规则1：用户白名单（优先级最高）
        if user_id in self.queue_mode_user_whitelist:
            return True

        # 规则2：按百分比灰度（基于用户ID和文件ID的组合）
        if self.queue_mode_percentage > 0:
            # 使用用户ID和文件ID的组合来确保同一用户的相同文件始终走相同路径
            hash_value = hash(f"{user_id}_{file_id}") % 100
            if hash_value < self.queue_mode_percentage:
                return True

        return False
    
    def get_consumption_mode(self, user_id: int, file_id: int, task_type: int) -> str:
        """
        获取消费模式
        
        Returns:
            str: 'redis_queue' 或 'http_polling'
        """
        if self.should_use_queue_mode(user_id, file_id, task_type):
            return 'redis_queue'
        return 'http_polling'
    
    def get_grayscale_stats(self) -> Dict[str, Any]:
        """获取灰度配置统计"""
        return {
            'queue_mode_enabled': self.queue_mode_enabled,
            'queue_mode_percentage': self.queue_mode_percentage,
            'user_whitelist_count': len(self.queue_mode_user_whitelist),
            'user_whitelist': list(self.queue_mode_user_whitelist),
            'current_config': {
                'ENABLE_QUEUE_MODE': os.getenv('ENABLE_QUEUE_MODE', 'false'),
                'QUEUE_MODE_PERCENTAGE': os.getenv('QUEUE_MODE_PERCENTAGE', '0'),
                'QUEUE_MODE_USER_WHITELIST': os.getenv('QUEUE_MODE_USER_WHITELIST', ''),
            }
        }


# 全局灰度服务实例
grayscale_service = GrayscaleService()


def should_use_queue_mode(user_id: int, file_id: int, task_type: int) -> bool:
    """便捷函数：判断是否使用队列模式"""
    return grayscale_service.should_use_queue_mode(user_id, file_id, task_type)


def get_consumption_mode(user_id: int, file_id: int, task_type: int) -> str:
    """便捷函数：获取消费模式"""
    return grayscale_service.get_consumption_mode(user_id, file_id, task_type)


def get_grayscale_stats() -> Dict[str, Any]:
    """便捷函数：获取灰度统计"""
    return grayscale_service.get_grayscale_stats()


# 示例配置说明
"""
环境变量配置示例：

# 基础开关
ENABLE_QUEUE_MODE=true                    # 启用队列模式

# 百分比灰度（0-100）
QUEUE_MODE_PERCENTAGE=30                  # 30% 的任务使用队列模式

# 用户白名单（逗号分隔的用户ID）
QUEUE_MODE_USER_WHITELIST=1001,1002,1003 # 指定用户始终使用队列模式

灰度策略示例：

1. 保守灰度：
   ENABLE_QUEUE_MODE=true
   QUEUE_MODE_PERCENTAGE=5

2. 用户白名单：
   ENABLE_QUEUE_MODE=true
   QUEUE_MODE_USER_WHITELIST=1001,1002

3. 全量切换：
   ENABLE_QUEUE_MODE=true
   QUEUE_MODE_PERCENTAGE=100
"""
