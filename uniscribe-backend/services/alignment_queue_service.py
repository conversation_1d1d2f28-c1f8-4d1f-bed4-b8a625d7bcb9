import json
import time
import redis
from typing import Dict, Any, Optional
from config import CONFIG


class AlignmentQueueService:
    """使用 Redis Streams 管理说话人对齐任务队列"""

    STREAM_NAME = "alignment_tasks"
    CONSUMER_GROUP = "alignment_workers"
    CONSUMER_NAME = "worker_1"

    _redis_client = None

    @classmethod
    def get_client(cls):
        """获取 Redis 客户端"""
        if cls._redis_client is None:
            cls._redis_client = redis.Redis(
                host=CONFIG.REDIS["host"],
                port=CONFIG.REDIS["port"],
                password=CONFIG.REDIS["password"],
                ssl=CONFIG.REDIS["ssl"],
                decode_responses=True,  # 自动解码响应为字符串
            )
            cls._redis_client.ping()
        return cls._redis_client

    @classmethod
    def _ensure_consumer_group(cls):
        """确保消费者组存在"""
        client = cls.get_client()
        try:
            client.xgroup_create(
                cls.STREAM_NAME, cls.CONSUMER_GROUP, id="0", mkstream=True
            )
        except Exception as e:
            # 消费者组已存在，忽略错误
            if "BUSYGROUP" not in str(e):
                raise

    @classmethod
    def enqueue_alignment_task(cls, task_data: Dict[str, Any]) -> str:
        """将对齐任务加入队列

        Args:
            task_data: 包含 file_id, transcription_task_id, diarization_task_id 的字典

        Returns:
            消息ID
        """
        client = cls.get_client()

        # 添加时间戳
        task_data_copy = task_data.copy()
        task_data_copy["timestamp"] = str(int(time.time()))

        # 确保消费者组存在
        cls._ensure_consumer_group()

        # 使用 XADD 添加到 Stream，确保所有值都是字符串
        fields = {str(k): str(v) for k, v in task_data_copy.items()}
        message_id = client.xadd(cls.STREAM_NAME, fields)

        return str(message_id)

    @classmethod
    def dequeue_alignment_task(cls, timeout: int = 5000) -> Optional[Dict[str, Any]]:
        """从队列中获取对齐任务

        Args:
            timeout: 超时时间（毫秒）

        Returns:
            任务数据字典，如果没有任务则返回 None
        """
        client = cls.get_client()

        # 确保消费者组存在
        cls._ensure_consumer_group()

        try:
            # 使用 XREADGROUP 读取消息
            messages = client.xreadgroup(
                cls.CONSUMER_GROUP,
                cls.CONSUMER_NAME,
                {cls.STREAM_NAME: ">"},
                count=1,
                block=timeout,
            )

            if messages:
                _, stream_messages = messages[0]
                if stream_messages:
                    message_id, fields = stream_messages[0]

                    # 由于设置了 decode_responses=True，数据已经是字符串
                    task_data = dict(fields)
                    task_data["message_id"] = message_id

                    return task_data

        except Exception as e:
            print(f"Error reading from alignment queue: {e}")

        return None

    @classmethod
    def acknowledge_task(cls, message_id: str) -> bool:
        """确认任务已处理完成

        Args:
            message_id: 消息ID

        Returns:
            是否成功确认
        """
        client = cls.get_client()

        try:
            # 使用 XACK 确认消息
            result = client.xack(cls.STREAM_NAME, cls.CONSUMER_GROUP, message_id)
            return bool(result)
        except Exception as e:
            print(f"Error acknowledging message {message_id}: {e}")
            return False

    @classmethod
    def get_pending_count(cls) -> int:
        """获取待处理任务数量"""
        client = cls.get_client()

        try:
            # 获取 Stream 长度
            length = client.xlen(cls.STREAM_NAME)
            return length if isinstance(length, int) else 0
        except Exception:
            return 0

    @classmethod
    def clear_stream(cls) -> bool:
        """清空队列（仅用于测试）"""
        client = cls.get_client()

        try:
            client.delete(cls.STREAM_NAME)
            return True
        except Exception:
            return False
