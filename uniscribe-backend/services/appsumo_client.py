import requests
import logging
import json
from config import CONFIG
from typing import Dict, Optional, Tuple, Any, Literal
from exceptions.appsumo import AppSumoOAuthError

logger = logging.getLogger(__name__)


class AppSumoClient:
    def __init__(self):
        self.client_id = CONFIG.APPSUMO.get("client_id")
        self.client_secret = CONFIG.APPSUMO.get("client_secret")
        self.redirect_uri = CONFIG.APPSUMO.get("redirect_uri")
        self.base_url = "https://appsumo.com/openid"

    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        success_log_message: str = "Request successful",
        success_log_params: Optional[list] = None,
    ) -> Dict:
        """
        发送请求并处理通用的错误情况

        Args:
            method: 请求方法，如 'get' 或 'post'
            endpoint: API 端点，如 '/token/'
            data: POST 请求的 JSON 数据
            params: GET 请求的查询参数
            success_log_message: 成功时的日志消息，可以包含 %s 占位符
            success_log_params: 成功时要记录的参数列表（从响应中提取对应的值）

        Returns:
            Dict: API 响应的 JSON 数据

        Raises:
            AppSumoOAuthError: 当 OAuth 请求失败时
        """
        url = f"{self.base_url}{endpoint}"
        headers = {"Content-type": "application/json"}

        logger.info("Sending %s request to %s", method.upper(), url)
        if data:
            logger.info(
                "Request data: %s",
                {
                    k: "***" if k in ["client_secret", "code", "refresh_token"] else v
                    for k, v in data.items()
                },
            )
        if params:
            logger.info(
                "Request params: %s",
                {k: "***" if k in ["access_token"] else v for k, v in params.items()},
            )

        try:
            if method.lower() == "get":
                response = requests.get(url, params=params, headers=headers, timeout=10)
            elif method.lower() == "post":
                response = requests.post(url, json=data, headers=headers, timeout=10)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            logger.info("Response status code: %s", response.status_code)
            logger.info("Response headers: %s", dict(response.headers))

            # 处理错误响应
            if response.status_code != 200:
                logger.warning("Error response body: %s", response.text)
                # 尝试解析错误响应
                try:
                    error_data = response.json()
                    error_type = error_data.get("error", "unknown_error")
                    error_description = error_data.get(
                        "error_description", "Unknown error"
                    )
                    error_message = f"{error_type}: {error_description}"
                    raise AppSumoOAuthError(message=error_message) from None
                except json.JSONDecodeError as exc:
                    # 如果响应不是有效的 JSON，使用原始文本
                    raise AppSumoOAuthError(
                        message=f"OAuth error: {response.text}"
                    ) from exc

            # 解析 JSON 响应
            try:
                response_data = response.json()

                # 记录成功日志（但不记录敏感信息）
                if success_log_params:
                    log_params = [
                        response_data.get(k, "N/A") for k in success_log_params
                    ]
                    logger.info(success_log_message, *log_params)
                else:
                    logger.info(success_log_message)

                return response_data
            except json.JSONDecodeError as exc:
                logger.error("Failed to parse JSON response: %s", response.text)
                raise AppSumoOAuthError(
                    message="Invalid JSON response from OAuth server"
                ) from exc

        except requests.exceptions.HTTPError as e:
            logger.error(
                "HTTP error during %s request: %s, status: %s, body: %s",
                method.upper(),
                str(e),
                e.response.status_code if hasattr(e, "response") else "N/A",
                e.response.text if hasattr(e, "response") else "N/A",
            )
            raise
        except requests.exceptions.RequestException as e:
            logger.error("Request error during %s request: %s", method.upper(), str(e))
            raise
        except Exception as e:
            logger.error(
                "Unexpected error during %s request: %s", method.upper(), str(e)
            )
            raise

    def get_access_token(self, code: str) -> Dict:
        """
        使用授权码获取访问令牌

        Args:
            code: OAuth 授权码

        Returns:
            Dict: 包含 access_token, refresh_token 等信息
        """
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": code,
            "redirect_uri": self.redirect_uri,
            "grant_type": "authorization_code",
        }

        return self._make_request(
            method="post",
            endpoint="/token/",
            data=data,
            success_log_message="Successfully obtained access token, expires_in: %s",
            success_log_params=["expires_in"],
        )

    def refresh_access_token(self, refresh_token: str) -> Dict:
        """
        刷新访问令牌

        Args:
            refresh_token: 刷新令牌

        Returns:
            Dict: 包含新的 access_token, refresh_token 等信息
        """
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token,
            "grant_type": "refresh_token",
        }

        return self._make_request(
            method="post",
            endpoint="/token/",
            data=data,
            success_log_message="Successfully refreshed access token, expires_in: %s",
            success_log_params=["expires_in"],
        )

    def get_license_key(self, access_token: str) -> Dict:
        """
        获取用户的许可证信息

        Args:
            access_token: OAuth 访问令牌

        Returns:
            Dict: 包含 license_key, status 等信息
        """
        params = {"access_token": access_token}

        response_data = self._make_request(
            method="get",
            endpoint="/license_key/",
            params=params,
            success_log_message="Successfully obtained license key",
        )

        # 额外的日志记录（特定于此方法）
        if "license_key" in response_data:
            logger.info(
                "License key details: key=%s, status=%s",
                response_data.get("license_key"),
                response_data.get("status", "N/A"),
            )
        else:
            logger.warning("License key not found in response: %s", response_data)

        return response_data
