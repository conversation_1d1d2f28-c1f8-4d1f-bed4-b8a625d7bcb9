#!/usr/bin/env python3
"""
WeasyPrint 多语言 PDF 测试脚本
"""

import sys
import os

# 添加项目路径到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_weasyprint_pdf():
    """测试 WeasyPrint PDF 生成功能"""

    print("🧪 Starting WeasyPrint multilingual PDF generation test...")

    try:
        from controllers.export import PDFExport

        # 创建测试数据
        test_data = [
            {
                "speaker": "Speaker",
                "start_time": 0,
                "text": "Test Content:\nEnglish: Hello World\nKorean: 안녕하세요 세계\nArabic: مرحبا بالعالم\nChinese: 你好世界\nHungarian: Helló világ áéíóöőúüű\nRussian: Привет мир",
            },
            {
                "speaker": "Speaker",
                "start_time": 23,
                "text": "そこでは生と死の境界線がかつてないほど薄くなっていますまず最初にその名前がすべてを物語る場所を訪れましょうデスバレー",
            },
            {
                "speaker": "Speaker",
                "start_time": 35,
                "text": "アメリカ合衆国カリフォルニア州にあるデスバレー国立公園に位置しこれは世界でこの恐ろしい名前を持つ2つの場所の1つです",
            },
        ]

        # 创建 PDF 导出器
        pdf_exporter = PDFExport()

        print("📄 Generating PDF...")
        pdf_content = pdf_exporter.export(
            test_data, show_speaker_name=True, show_timestamps=True
        )

        # Save test PDF
        test_filename = "test_multilingual.pdf"
        with open(test_filename, "wb") as f:
            f.write(pdf_content)

        print("✅ PDF generation successful!")
        print(f"📁 File saved as: {test_filename}")
        print(f"📊 File size: {len(pdf_content)} bytes")

        # Verify file
        if os.path.exists(test_filename) and os.path.getsize(test_filename) > 1000:
            print("✅ PDF file verification passed!")
            return True
        else:
            print("❌ PDF file verification failed!")
            return False

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure WeasyPrint is installed: pip install weasyprint")
        return False

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_weasyprint_basic():
    """Test WeasyPrint basic functionality"""

    print("🔧 Testing WeasyPrint basic functionality...")

    try:
        from weasyprint import HTML, CSS
        from weasyprint.text.fonts import FontConfiguration

        # Create simple multilingual HTML
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Multilingual Test</title>
        </head>
        <body>
            <h1>Multilingual PDF Test</h1>
            <p>English: Hello World</p>
            <p>Korean: 안녕하세요 세계</p>
            <p>Arabic: مرحبا بالعالم</p>
            <p>Chinese: 你好世界</p>
            <p>Japanese: こんにちは世界</p>
            <p>Hungarian: Helló világ áéíóöőúüű</p>
            <p>Russian: Привет мир</p>
        </body>
        </html>
        """

        css_content = """
        body {
            font-family: 'Noto Sans', 'DejaVu Sans', Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #333;
        }
        p {
            margin: 10px 0;
        }
        """

        # Generate PDF
        font_config = FontConfiguration()
        html_doc = HTML(string=html_content)
        css_doc = CSS(string=css_content, font_config=font_config)

        pdf_content = html_doc.write_pdf(stylesheets=[css_doc], font_config=font_config)

        # Save test file
        basic_filename = "test_basic_multilingual.pdf"
        with open(basic_filename, "wb") as f:
            f.write(pdf_content)

        print("✅ Basic test successful!")
        print(f"📁 File saved as: {basic_filename}")
        print(f"📊 File size: {len(pdf_content)} bytes")

        return True

    except Exception as e:
        print(f"❌ Basic test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 WeasyPrint Multilingual PDF Test Started")
    print("=" * 50)

    # Test basic functionality
    basic_success = test_weasyprint_basic()
    print()

    # Test full functionality
    full_success = test_weasyprint_pdf()
    print()

    # Summary
    print("=" * 50)
    if basic_success and full_success:
        print(
            "🎉 All tests passed! WeasyPrint multilingual PDF functionality is working!"
        )
        sys.exit(0)
    else:
        print(
            "💥 Tests failed! Please check WeasyPrint installation and configuration!"
        )
        sys.exit(1)
