#!/usr/bin/env python3
"""
Minimal WeasyPrint test to check basic functionality
"""

def test_minimal_weasyprint():
    """Test minimal WeasyPrint functionality"""
    
    print("🧪 Testing minimal WeasyPrint...")
    
    try:
        from weasyprint import HTML
        print("✅ WeasyPrint import successful")
        
        # Create very simple HTML
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Minimal Test</title>
        </head>
        <body>
            <h1>Minimal WeasyPrint Test</h1>
            <p>English: Hello World</p>
            <p>Korean: 안녕하세요</p>
            <p>Chinese: 你好</p>
            <p>Arabic: مرحبا</p>
        </body>
        </html>
        """
        
        print("📄 Generating minimal PDF...")
        
        # Generate PDF without CSS
        html_doc = HTML(string=html_content)
        pdf_content = html_doc.write_pdf()
        
        # Save test file
        test_filename = "minimal_test.pdf"
        with open(test_filename, "wb") as f:
            f.write(pdf_content)
        
        print("✅ Minimal WeasyPrint test successful!")
        print(f"📁 File saved as: {test_filename}")
        print(f"📊 File size: {len(pdf_content)} bytes")
        
        return True
        
    except ImportError as e:
        print(f"❌ WeasyPrint import failed: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Minimal WeasyPrint Test")
    print("=" * 40)
    
    success = test_minimal_weasyprint()
    
    if success:
        print("🎉 Minimal test passed!")
    else:
        print("💥 Minimal test failed!")
    
    exit(0 if success else 1)
