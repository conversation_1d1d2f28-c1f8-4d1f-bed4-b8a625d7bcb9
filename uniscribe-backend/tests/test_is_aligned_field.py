#!/usr/bin/env python3
"""
测试 is_aligned 字段功能

验证新的对齐状态判断逻辑正确性
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text
import socket

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # 如果没有 python-dotenv，尝试手动加载 .env 文件
    env_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))), ".env"
    )
    if os.path.exists(env_path):
        with open(env_path, "r") as f:
            for line in f:
                if "=" in line and not line.strip().startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value


def create_minimal_app():
    """创建最小化的 Flask 应用"""
    app = Flask(__name__)
    
    # 从环境变量加载数据库配置
    database_url = os.getenv("SQLALCHEMY_DATABASE_URI") or os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError(
            "SQLALCHEMY_DATABASE_URI or DATABASE_URL environment variable is required"
        )
    
    # 如果在本地运行且是 Docker 环境的配置，替换为本地连接
    # 如果在 Docker 容器内运行，保持原有配置
    try:
        # 尝试解析 mysql_db 主机名，如果失败说明在本地环境
        socket.gethostbyname('mysql_db')
        # 在 Docker 环境中，保持原有配置
        pass
    except socket.gaierror:
        # 在本地环境中，替换为本地连接
        if "mysql_db:3306" in database_url:
            database_url = database_url.replace("mysql_db:3306", "localhost:3307")
    
    app.config["SQLALCHEMY_DATABASE_URI"] = database_url
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
    
    db = SQLAlchemy(app)
    return app, db


def test_field_exists(db):
    """测试 is_aligned 字段是否存在"""
    print("1. 测试 is_aligned 字段是否存在...")
    
    try:
        # 检查字段是否存在
        result = db.session.execute(
            text("""
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'task_result' 
                AND COLUMN_NAME = 'is_aligned'
            """)
        )
        
        field_info = result.fetchone()
        if field_info:
            print(f"  ✅ is_aligned 字段存在")
            print(f"    - 类型: {field_info[1]}")
            print(f"    - 可空: {field_info[2]}")
            print(f"    - 默认值: {field_info[3]}")
            print(f"    - 注释: {field_info[4]}")
            return True
        else:
            print("  ❌ is_aligned 字段不存在")
            return False
        
    except Exception as e:
        print(f"  ❌ 检查字段失败: {e}")
        return False


def test_data_consistency(db):
    """测试数据一致性"""
    print("2. 测试数据一致性...")
    
    try:
        # 检查总记录数
        total_result = db.session.execute(text("SELECT COUNT(*) FROM task_result"))
        total_count = total_result.fetchone()[0]
        
        # 检查已对齐的记录数
        aligned_result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE is_aligned = TRUE")
        )
        aligned_count = aligned_result.fetchone()[0]
        
        # 检查未对齐的记录数
        not_aligned_result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE is_aligned = FALSE")
        )
        not_aligned_count = not_aligned_result.fetchone()[0]
        
        print(f"  - 总记录数: {total_count}")
        print(f"  - 已对齐: {aligned_count}")
        print(f"  - 未对齐: {not_aligned_count}")
        
        # 验证数据一致性
        if aligned_count + not_aligned_count == total_count:
            print("  ✅ 数据一致性检查通过")
            return True
        else:
            print("  ❌ 数据一致性检查失败")
            return False
        
    except Exception as e:
        print(f"  ❌ 数据一致性检查失败: {e}")
        return False


def test_alignment_logic(db):
    """测试对齐逻辑"""
    print("3. 测试对齐逻辑...")
    
    try:
        # 检查有 original_segments 的记录是否都标记为已对齐
        result = db.session.execute(
            text("""
                SELECT COUNT(*) FROM task_result 
                WHERE original_segments IS NOT NULL AND is_aligned = FALSE
            """)
        )
        inconsistent_count = result.fetchone()[0]
        
        if inconsistent_count > 0:
            print(f"  ❌ 发现 {inconsistent_count} 条不一致的记录（有 original_segments 但未标记为已对齐）")
            return False
        
        # 检查没有 original_segments 的记录是否都标记为未对齐
        result = db.session.execute(
            text("""
                SELECT COUNT(*) FROM task_result 
                WHERE original_segments IS NULL AND is_aligned = TRUE
            """)
        )
        inconsistent_count = result.fetchone()[0]
        
        if inconsistent_count > 0:
            print(f"  ❌ 发现 {inconsistent_count} 条不一致的记录（没有 original_segments 但标记为已对齐）")
            return False
        
        print("  ✅ 对齐逻辑一致性检查通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 对齐逻辑检查失败: {e}")
        return False


def test_query_performance(db):
    """测试查询性能"""
    print("4. 测试查询性能...")
    
    try:
        # 测试新的查询逻辑
        result = db.session.execute(
            text("""
                SELECT COUNT(*) FROM task_result 
                WHERE segments IS NOT NULL 
                AND diarization_segments IS NOT NULL 
                AND is_aligned = FALSE
            """)
        )
        need_alignment_count = result.fetchone()[0]
        
        print(f"  - 需要对齐的文件数: {need_alignment_count}")
        
        # 测试已对齐的查询
        result = db.session.execute(
            text("""
                SELECT COUNT(*) FROM task_result 
                WHERE segments IS NOT NULL 
                AND diarization_segments IS NOT NULL 
                AND is_aligned = TRUE
            """)
        )
        already_aligned_count = result.fetchone()[0]
        
        print(f"  - 已对齐的文件数: {already_aligned_count}")
        print("  ✅ 查询性能测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 查询性能测试失败: {e}")
        return False


def test_semantic_clarity(db):
    """测试语义清晰度"""
    print("5. 测试语义清晰度...")
    
    try:
        # 展示新旧查询方式的对比
        print("  - 新查询方式（语义清晰）:")
        print("    WHERE is_aligned = FALSE")
        
        print("  - 旧查询方式（语义模糊）:")
        print("    WHERE original_segments IS NULL")
        
        # 验证两种查询结果是否一致
        new_query_result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE is_aligned = FALSE")
        )
        new_count = new_query_result.fetchone()[0]
        
        old_query_result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE original_segments IS NULL")
        )
        old_count = old_query_result.fetchone()[0]
        
        print(f"  - 新查询结果: {new_count}")
        print(f"  - 旧查询结果: {old_count}")
        
        if new_count == old_count:
            print("  ✅ 语义清晰度测试通过（查询结果一致）")
            return True
        else:
            print("  ❌ 语义清晰度测试失败（查询结果不一致）")
            return False
        
    except Exception as e:
        print(f"  ❌ 语义清晰度测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🧪 开始测试 is_aligned 字段功能...")
    print("=" * 60)
    
    app, db = create_minimal_app()
    
    with app.app_context():
        # 执行测试
        tests = [
            test_field_exists,
            test_data_consistency,
            test_alignment_logic,
            test_query_performance,
            test_semantic_clarity,
        ]
        
        passed = 0
        failed = 0
        
        for test in tests:
            if test(db):
                passed += 1
            else:
                failed += 1
    
    print("=" * 60)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("✅ 所有测试通过！is_aligned 字段功能正常！")
        print("\n🎉 优势总结:")
        print("  - 语义清晰：is_aligned = FALSE 比 original_segments IS NULL 更直观")
        print("  - 逻辑分离：对齐状态和数据存储解耦")
        print("  - 易于维护：状态判断逻辑简单明了")
        print("  - 扩展性好：可以轻松添加更多状态字段")
        return 0
    else:
        print("❌ 部分测试失败，请检查问题")
        return 1


if __name__ == "__main__":
    sys.exit(main())
