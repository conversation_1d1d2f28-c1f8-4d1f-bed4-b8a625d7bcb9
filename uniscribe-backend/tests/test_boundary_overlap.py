#!/usr/bin/env python3
"""
测试边界重叠问题的修复
"""

import sys
import os
import time
import json
from typing import List, Dict

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.alignment_service import AlignmentService


def create_boundary_overlap_test_data():
    """创建边界重叠的测试数据，模拟你遇到的问题"""
    
    # 模拟你的实际数据：转录词在说话人边界附近
    word_segments = [
        # Speaker A 的最后几个词
        {"id": 2514, "text": " It", "end_time": 889.4, "start_time": 889.3},
        {"id": 2515, "text": " was", "end_time": 889.56, "start_time": 889.4},
        {"id": 2516, "text": " a", "end_time": 889.7, "start_time": 889.56},
        {"id": 2517, "text": " miracle.", "end_time": 890.0, "start_time": 889.7},
        
        # 边界区域的词 - 这些应该属于 Speaker B
        {"id": 2518, "text": " If", "end_time": 890.26, "start_time": 890.18},
        {"id": 2519, "text": " I", "end_time": 890.36, "start_time": 890.26},
        {"id": 2520, "text": " hadn't", "end_time": 890.68, "start_time": 890.36},
        {"id": 2521, "text": " turned", "end_time": 890.86, "start_time": 890.68},
        {"id": 2522, "text": " my", "end_time": 891.1, "start_time": 890.86},
        {"id": 2523, "text": " head,", "end_time": 891.6, "start_time": 891.1},
        
        # Speaker B 继续
        {"id": 2524, "text": " I", "end_time": 891.94, "start_time": 891.6},
        {"id": 2525, "text": " would", "end_time": 892.36, "start_time": 891.94},
        {"id": 2526, "text": " not", "end_time": 892.5, "start_time": 892.36},
        {"id": 2527, "text": " be", "end_time": 892.66, "start_time": 892.5},
        {"id": 2528, "text": " talking", "end_time": 893.0, "start_time": 892.66},
        {"id": 2529, "text": " to", "end_time": 893.2, "start_time": 893.0},
        {"id": 2530, "text": " you", "end_time": 893.4, "start_time": 893.2},
        {"id": 2531, "text": " right", "end_time": 893.7, "start_time": 893.4},
        {"id": 2532, "text": " now.", "end_time": 894.0, "start_time": 893.7},
    ]
    
    # 模拟你的实际 diarization 数据（转换为秒）
    # 14:42.165938 = 882.165938, 14:50.772188 = 890.772188
    # 14:49.202813 = 889.202813, 14:54.737813 = 894.737813
    diarization_data = {
        "segments": [
            {
                "speaker": "A",
                "start": "0:14:42.165938",  # 882.165938 秒
                "stop": "0:14:50.772188"    # 890.772188 秒
            },
            {
                "speaker": "B", 
                "start": "0:14:49.202813",  # 889.202813 秒
                "stop": "0:14:54.737813"    # 894.737813 秒
            }
        ],
        "speakers": {
            "count": 2,
            "labels": ["A", "B"]
        }
    }
    
    return word_segments, diarization_data


def test_boundary_overlap_original():
    """测试原始算法在边界重叠时的表现"""
    print("Testing Boundary Overlap - Original Algorithm")
    print("=" * 60)
    
    word_segments, diarization_data = create_boundary_overlap_test_data()
    
    print("Word segments around boundary:")
    for seg in word_segments:
        # 转换为分:秒格式显示
        start_min = int(seg['start_time'] // 60)
        start_sec = seg['start_time'] % 60
        end_min = int(seg['end_time'] // 60)
        end_sec = seg['end_time'] % 60
        print(f"  {seg['id']}: {start_min}:{start_sec:06.3f}-{end_min}:{end_sec:06.3f} | {seg['text']}")
    
    print("\nDiarization segments:")
    for seg in diarization_data["segments"]:
        print(f"  {seg['speaker']}: {seg['start']}-{seg['stop']}")
    
    print("\n" + "=" * 60)
    
    # 使用原始 WhisperX 算法（不分组）
    aligned_segments = AlignmentService.align_segments_whisperx(
        word_segments, diarization_data, group_by_speaker=False
    )
    
    print("\nOriginal Algorithm Results:")
    print("Word-level speaker assignments:")
    
    problem_words = []
    for seg in aligned_segments:
        speaker = seg.get("speaker", "Unknown")
        start_min = int(seg['start_time'] // 60)
        start_sec = seg['start_time'] % 60
        end_min = int(seg['end_time'] // 60)
        end_sec = seg['end_time'] % 60
        
        # 标记可能有问题的词（应该是 B 但被分配为 A）
        is_problem = (seg['text'].strip() in ["If", "I", "hadn't", "turned", "my", "head,"] and speaker == "A")
        marker = " ⚠️ PROBLEM" if is_problem else ""
        
        print(f"  {seg['id']}: {start_min}:{start_sec:06.3f}-{end_min}:{end_sec:06.3f} | Speaker: {speaker} | {seg['text']}{marker}")
        
        if is_problem:
            problem_words.append(seg['text'].strip())
    
    if problem_words:
        print(f"\n❌ Found {len(problem_words)} problematic word assignments: {problem_words}")
        print("These words should belong to Speaker B but were assigned to Speaker A")
    else:
        print("\n✅ No obvious problems detected")


def test_boundary_overlap_improved():
    """测试改进算法在边界重叠时的表现"""
    print("\n" + "=" * 60)
    print("Testing Boundary Overlap - Improved Algorithm")
    print("=" * 60)
    
    word_segments, diarization_data = create_boundary_overlap_test_data()
    
    # 使用改进的 WhisperX 算法（分组）
    aligned_segments = AlignmentService.align_segments_whisperx(
        word_segments, diarization_data, group_by_speaker=True
    )
    
    print("Improved Algorithm Results:")
    print("Grouped segments with speaker assignments:")
    
    for seg in aligned_segments:
        speaker = seg.get("speaker", "Unknown")
        start_min = int(seg['start_time'] // 60)
        start_sec = seg['start_time'] % 60
        end_min = int(seg['end_time'] // 60)
        end_sec = seg['end_time'] % 60
        word_count = len(seg.get("words", []))
        
        print(f"  {seg['id']}: {start_min}:{start_sec:06.3f}-{end_min}:{end_sec:06.3f} | Speaker: {speaker} | Words: {word_count}")
        print(f"    Text: {seg['text']}")
        
        # 检查词级别的分配
        words = seg.get("words", [])
        if words:
            print("    Word details:")
            for word in words:
                word_speaker = word.get("speaker", "Unknown")
                word_start_min = int(word['start_time'] // 60)
                word_start_sec = word['start_time'] % 60
                print(f"      {word_start_min}:{word_start_sec:06.3f} | {word_speaker} | {word['text']}")
        print()


def analyze_boundary_behavior():
    """分析边界行为的详细信息"""
    print("=" * 60)
    print("Boundary Behavior Analysis")
    print("=" * 60)
    
    # 重点分析边界附近的几个关键词
    critical_words = [
        {"text": " miracle.", "start_time": 889.7, "end_time": 890.0},  # A的最后一个词
        {"text": " If", "start_time": 890.18, "end_time": 890.26},        # 边界词1
        {"text": " I", "start_time": 890.26, "end_time": 890.36},         # 边界词2
        {"text": " hadn't", "start_time": 890.36, "end_time": 890.68},   # 边界词3
    ]
    
    # Diarization 时间点（转换为秒）
    # A: 882.165938 - 890.772188
    # B: 889.202813 - 894.737813
    # 重叠区域: 889.202813 - 890.772188
    
    print("Critical words analysis:")
    print("Speaker A range: 882.17 - 890.77 seconds")
    print("Speaker B range: 889.20 - 894.74 seconds") 
    print("Overlap region: 889.20 - 890.77 seconds")
    print()
    
    for word in critical_words:
        start = word['start_time']
        end = word['end_time']
        center = (start + end) / 2
        
        # 检查与各个说话人的重叠
        a_overlap = max(0, min(890.772188, end) - max(882.165938, start))
        b_overlap = max(0, min(894.737813, end) - max(889.202813, start))
        
        in_overlap_region = start >= 889.202813 and end <= 890.772188
        
        print(f"Word: '{word['text']}' ({start:.3f}-{end:.3f}, center: {center:.3f})")
        print(f"  A overlap: {a_overlap:.3f}s, B overlap: {b_overlap:.3f}s")
        print(f"  In overlap region: {in_overlap_region}")
        print(f"  Expected speaker: {'B (center in B range)' if center > 889.9 else 'A or B (ambiguous)'}")
        print()


def main():
    """主函数"""
    test_boundary_overlap_original()
    test_boundary_overlap_improved()
    analyze_boundary_behavior()


if __name__ == "__main__":
    main()
