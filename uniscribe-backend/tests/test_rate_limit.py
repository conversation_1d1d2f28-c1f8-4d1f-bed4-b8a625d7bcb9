"""
Rate Limit 功能测试

测试通用频率限制功能的各种场景。
"""

import time
import pytest
from unittest.mock import patch, MagicMock
from libs.rate_limit import (
    RateLimiter,
    SlidingWindowStrategy,
    FixedWindowStrategy,
    MemoryRateLimitBackend,
    RedisRateLimitBackend,
    rate_limit,
    RateLimitExceeded,
)


class TestMemoryRateLimitBackend:
    """测试内存后端"""

    def test_get_set_requests(self):
        backend = MemoryRateLimitBackend()

        # 初始状态应该返回空列表
        requests = backend.get_requests("test_key")
        assert requests == []

        # 设置请求列表
        test_requests = [1.0, 2.0, 3.0]
        result = backend.set_requests("test_key", test_requests, 10)
        assert result is True

        # 获取请求列表
        requests = backend.get_requests("test_key")
        assert requests == test_requests

    def test_increment_counter(self):
        backend = MemoryRateLimitBackend()

        # 第一次增加
        count = backend.increment_counter("counter_key", 10)
        assert count == 1

        # 第二次增加
        count = backend.increment_counter("counter_key", 10)
        assert count == 2

    def test_expiration(self):
        backend = MemoryRateLimitBackend()

        # 设置很短的TTL
        backend.set_requests("expire_key", [1.0, 2.0], 0.1)

        # 立即获取应该有数据
        requests = backend.get_requests("expire_key")
        assert requests == [1.0, 2.0]

        # 等待过期
        time.sleep(0.2)

        # 过期后应该返回空列表
        requests = backend.get_requests("expire_key")
        assert requests == []


class TestSlidingWindowStrategy:
    """测试滑动窗口策略"""

    def test_basic_sliding_window(self):
        backend = MemoryRateLimitBackend()
        strategy = SlidingWindowStrategy(max_requests=2, window_size=1.0)

        # 第一次请求应该被允许
        allowed, wait_time = strategy.is_allowed(backend, "test_key")
        assert allowed is True
        assert wait_time is None

        # 第二次请求应该被允许
        allowed, wait_time = strategy.is_allowed(backend, "test_key")
        assert allowed is True
        assert wait_time is None

        # 第三次请求应该被拒绝
        allowed, wait_time = strategy.is_allowed(backend, "test_key")
        assert allowed is False
        assert wait_time is not None
        assert wait_time > 0

    def test_window_expiration(self):
        backend = MemoryRateLimitBackend()
        strategy = SlidingWindowStrategy(max_requests=1, window_size=0.1)

        # 第一次请求
        allowed, _ = strategy.is_allowed(backend, "test_key")
        assert allowed is True

        # 检查后端是否正确记录了请求
        requests = backend.get_requests("test_key")
        print(f"After first request: {requests}")
        assert len(requests) == 1

        # 立即第二次请求应该被拒绝
        allowed, wait_time = strategy.is_allowed(backend, "test_key")
        print(f"Second request: allowed={allowed}, wait_time={wait_time}")
        assert allowed is False

        # 等待窗口过期
        time.sleep(0.2)

        # 窗口过期后应该被允许
        allowed, _ = strategy.is_allowed(backend, "test_key")
        assert allowed is True


class TestFixedWindowStrategy:
    """测试固定窗口策略"""

    def test_basic_fixed_window(self):
        backend = MemoryRateLimitBackend()
        strategy = FixedWindowStrategy(max_requests=2, window_size=1.0)

        # 前两次请求应该被允许
        for i in range(2):
            allowed, wait_time = strategy.is_allowed(backend, "test_key")
            assert allowed is True
            assert wait_time is None

        # 第三次请求应该被拒绝
        allowed, wait_time = strategy.is_allowed(backend, "test_key")
        assert allowed is False
        assert wait_time is not None
        assert wait_time > 0


class TestRateLimiter:
    """测试 RateLimiter 类"""

    def test_rate_limiter_with_sliding_window(self):
        backend = MemoryRateLimitBackend()
        strategy = SlidingWindowStrategy(max_requests=2, window_size=1.0)
        limiter = RateLimiter(strategy, backend, "test_prefix")

        # 测试允许的请求
        allowed, _ = limiter.is_allowed("user1")
        assert allowed is True

        allowed, _ = limiter.is_allowed("user1")
        assert allowed is True

        # 测试被拒绝的请求
        allowed, wait_time = limiter.is_allowed("user1")
        assert allowed is False
        assert wait_time > 0

    def test_different_identifiers(self):
        backend = MemoryRateLimitBackend()
        strategy = SlidingWindowStrategy(max_requests=1, window_size=1.0)
        limiter = RateLimiter(strategy, backend, "test_prefix")

        # 不同标识符应该有独立的限制
        allowed, _ = limiter.is_allowed("user1")
        assert allowed is True

        allowed, _ = limiter.is_allowed("user2")
        assert allowed is True

        # 同一标识符的第二次请求应该被拒绝
        allowed, _ = limiter.is_allowed("user1")
        assert allowed is False

    def test_wait_if_needed(self):
        backend = MemoryRateLimitBackend()
        strategy = SlidingWindowStrategy(max_requests=1, window_size=0.1)
        limiter = RateLimiter(strategy, backend, "test_prefix")

        # 第一次请求
        waited = limiter.wait_if_needed("user1")
        assert waited is False

        # 第二次请求应该等待
        start_time = time.time()
        waited = limiter.wait_if_needed("user1")
        end_time = time.time()

        assert waited is True
        assert (end_time - start_time) >= 0.05  # 至少等待了一些时间


class TestRateLimitDecorator:
    """测试 rate_limit 装饰器"""

    def test_basic_decorator(self):
        @rate_limit(
            max_requests=2,
            window_size=1.0,
            backend=MemoryRateLimitBackend(),
            key_prefix="test_decorator",
        )
        def test_function(data):
            return f"processed: {data}"

        # 前两次调用应该成功
        result1 = test_function("data1")
        assert result1 == "processed: data1"

        result2 = test_function("data2")
        assert result2 == "processed: data2"

        # 第三次调用应该等待（在测试中我们不等待）
        # 这里只是验证装饰器能正常工作

    def test_decorator_with_exception(self):
        @rate_limit(
            max_requests=1,
            window_size=1.0,
            backend=MemoryRateLimitBackend(),
            key_prefix="test_exception",
            wait=False,
        )
        def test_function(data):
            return f"processed: {data}"

        # 第一次调用应该成功
        result = test_function("data1")
        assert result == "processed: data1"

        # 第二次调用应该抛出异常
        with pytest.raises(RateLimitExceeded):
            test_function("data2")

    def test_decorator_with_identifier_func(self):
        def get_user_id(*args, **kwargs):
            return kwargs.get("user_id", "default")

        @rate_limit(
            max_requests=1,
            window_size=1.0,
            backend=MemoryRateLimitBackend(),
            key_prefix="test_user_id",
            identifier_func=get_user_id,
        )
        def test_function(data, user_id=None):
            return f"processed: {data} for {user_id}"

        # 不同用户应该有独立的限制
        result1 = test_function("data1", user_id="user1")
        assert "user1" in result1

        result2 = test_function("data2", user_id="user2")
        assert "user2" in result2


class TestRedisRateLimitBackend:
    """测试 Redis 后端（需要模拟）"""

    @patch("libs.rate_limit.RedisCache")
    def test_redis_backend_get_requests(self, mock_redis_cache):
        # 模拟 Redis 缓存
        mock_cache_instance = MagicMock()
        mock_redis_cache.return_value = mock_cache_instance
        mock_cache_instance.get.return_value = [1.0, 2.0, 3.0]

        backend = RedisRateLimitBackend("test_prefix")
        requests = backend.get_requests("test_key")

        assert requests == [1.0, 2.0, 3.0]
        mock_cache_instance.get.assert_called_once_with("test_key")

    @patch("libs.rate_limit.RedisCache")
    def test_redis_backend_set_requests(self, mock_redis_cache):
        # 模拟 Redis 缓存
        mock_cache_instance = MagicMock()
        mock_redis_cache.return_value = mock_cache_instance
        mock_cache_instance.set.return_value = True

        backend = RedisRateLimitBackend("test_prefix")
        result = backend.set_requests("test_key", [1.0, 2.0], 10)

        assert result is True
        mock_cache_instance.set.assert_called_once_with("test_key", [1.0, 2.0], 10)


def test_integration():
    """集成测试"""
    # 使用内存后端进行完整的集成测试
    backend = MemoryRateLimitBackend()

    @rate_limit(
        max_requests=3, window_size=0.5, backend=backend, key_prefix="integration_test"
    )
    def api_call(data):
        return f"API response: {data}"

    # 前3次调用应该成功
    for i in range(3):
        result = api_call(f"request_{i}")
        assert f"request_{i}" in result

    # 第4次调用应该等待（在实际测试中我们验证行为）
    start_time = time.time()
    result = api_call("request_3")  # 这会触发等待
    end_time = time.time()

    # 验证确实等待了一些时间
    assert (end_time - start_time) > 0.1
    assert "request_3" in result


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
