#!/usr/bin/env python3

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_factory import create_app
from models.task_result import TaskResult
import json


def analyze_speaker_optimization(file_id):
    """分析说话人优化的详细情况"""
    app = create_app()

    with app.app_context():
        # 获取任务结果
        task_result = TaskResult.query.filter_by(file_id=file_id).first()
        if not task_result:
            print(f"❌ 未找到文件 {file_id} 的任务结果")
            return

        segments = task_result.segments
        if not segments:
            print(f"❌ 文件 {file_id} 没有段落数据")
            return

        print(f"=== 分析文件 {file_id} 的说话人分配 ===")
        print(f"总段落数: {len(segments)}")

        # 先检查段落的字段结构
        if segments:
            print(f"\n=== 段落字段结构示例 ===")
            sample_segment = segments[0]
            print(f"字段: {list(sample_segment.keys())}")
            for key, value in sample_segment.items():
                if isinstance(value, (int, float, str)) and len(str(value)) < 100:
                    print(f"  {key}: {value}")

        # 分析 06:10-06:20 时间段的段落
        target_segments = []
        for i, segment in enumerate(segments):
            # 尝试不同的时间字段名
            start_time = segment.get("start", segment.get("start_time", 0))
            end_time = segment.get("end", segment.get("end_time", 0))

            # 找到 06:10-06:20 时间段的段落
            if 370 <= start_time <= 380:  # 6:10 = 370秒, 6:20 = 380秒
                target_segments.append((i, segment))

        print(f"\n=== 06:10-06:20 时间段的段落 ===")
        for i, (idx, segment) in enumerate(target_segments):
            start_time = segment.get("start", 0)
            end_time = segment.get("end", 0)
            duration = end_time - start_time
            speaker = segment.get("speaker", "Unknown")
            text = (
                segment.get("text", "")[:50] + "..."
                if len(segment.get("text", "")) > 50
                else segment.get("text", "")
            )

            print(
                f"  [{idx}] {start_time:.2f}-{end_time:.2f}s ({duration:.2f}s) | Speaker: {speaker} | Text: {text}"
            )

            # 分析是否符合优化条件
            if i > 0 and i < len(target_segments) - 1:
                prev_segment = target_segments[i - 1][1]
                next_segment = target_segments[i + 1][1]

                prev_speaker = prev_segment.get("speaker")
                next_speaker = next_segment.get("speaker")
                current_speaker = segment.get("speaker")

                print(f"    前一段说话人: {prev_speaker}, 后一段说话人: {next_speaker}")

                # 检查优化条件
                if duration < 3.0:
                    print(f"    ✅ 段落较短 ({duration:.2f}s < 3.0s)")
                else:
                    print(f"    ❌ 段落不够短 ({duration:.2f}s >= 3.0s)")

                if prev_speaker == next_speaker and current_speaker != prev_speaker:
                    print(f"    ✅ 前后说话人相同且与当前不同")

                    # 计算段长度比例
                    prev_duration = prev_segment.get("end", 0) - prev_segment.get(
                        "start", 0
                    )
                    next_duration = next_segment.get("end", 0) - next_segment.get(
                        "start", 0
                    )
                    adjacent_avg_duration = (prev_duration + next_duration) / 2
                    duration_ratio = (
                        duration / adjacent_avg_duration
                        if adjacent_avg_duration > 0
                        else 1
                    )

                    print(
                        f"    段长度比例: {duration_ratio:.3f} (当前: {duration:.2f}s, 相邻平均: {adjacent_avg_duration:.2f}s)"
                    )

                    if duration_ratio < 0.3:
                        print(f"    ✅ 应该重新分配 (比例 < 0.3)")
                    else:
                        print(f"    ❌ 不需要重新分配 (比例 >= 0.3)")
                else:
                    print(f"    ❌ 前后说话人不相同或与当前相同")

        # 查找所有可能的优化候选
        print(f"\n=== 全部段落的优化分析 ===")
        optimization_candidates = []

        for i in range(1, len(segments) - 1):
            current_segment = segments[i]
            prev_segment = segments[i - 1]
            next_segment = segments[i + 1]

            start_time = current_segment.get("start", 0)
            end_time = current_segment.get("end", 0)
            duration = end_time - start_time

            # 检查优化条件
            if duration < 3.0:
                prev_speaker = prev_segment.get("speaker")
                next_speaker = next_segment.get("speaker")
                current_speaker = current_segment.get("speaker")

                if (
                    prev_speaker == next_speaker
                    and current_speaker != prev_speaker
                    and prev_speaker
                    and next_speaker
                ):

                    # 计算段长度比例
                    prev_duration = prev_segment.get("end", 0) - prev_segment.get(
                        "start", 0
                    )
                    next_duration = next_segment.get("end", 0) - next_segment.get(
                        "start", 0
                    )
                    adjacent_avg_duration = (prev_duration + next_duration) / 2
                    duration_ratio = (
                        duration / adjacent_avg_duration
                        if adjacent_avg_duration > 0
                        else 1
                    )

                    optimization_candidates.append(
                        {
                            "index": i,
                            "start_time": start_time,
                            "end_time": end_time,
                            "duration": duration,
                            "current_speaker": current_speaker,
                            "target_speaker": prev_speaker,
                            "duration_ratio": duration_ratio,
                            "should_optimize": duration_ratio < 0.3,
                        }
                    )

        if optimization_candidates:
            print(f"找到 {len(optimization_candidates)} 个优化候选:")
            for candidate in optimization_candidates:
                status = "✅ 应优化" if candidate["should_optimize"] else "❌ 不优化"
                print(
                    f"  [{candidate['index']}] {candidate['start_time']:.2f}-{candidate['end_time']:.2f}s "
                    f"({candidate['duration']:.2f}s) | {candidate['current_speaker']} -> {candidate['target_speaker']} "
                    f"| 比例: {candidate['duration_ratio']:.3f} | {status}"
                )
        else:
            print("没有找到优化候选")


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python test_speaker_optimization.py <file_id>")
        sys.exit(1)

    file_id = sys.argv[1]
    analyze_speaker_optimization(file_id)
