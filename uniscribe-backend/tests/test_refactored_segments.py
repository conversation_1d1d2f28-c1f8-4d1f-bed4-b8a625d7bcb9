#!/usr/bin/env python3
"""
测试重构后的 segments 数据结构功能

验证以下功能：
1. 数据模型字段正确性
2. 读取接口逻辑
3. 编辑接口逻辑
4. 对齐服务逻辑
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models import db
from models.task_result import TaskResult
from models.transcription_file import TranscriptionFile
from controllers.transcription import (
    get_transcription_file_with_result,
    update_segment_text,
    update_speakers_batch,
)
from services.alignment_service import AlignmentService
import json


def test_data_model():
    """测试数据模型字段"""
    print("1. 测试数据模型字段...")

    try:
        # 检查 TaskResult 模型是否有正确的字段
        task_result = TaskResult()

        # 检查必要字段是否存在
        assert hasattr(task_result, "segments"), "segments 字段不存在"
        assert hasattr(task_result, "original_segments"), "original_segments 字段不存在"
        assert hasattr(
            task_result, "diarization_segments"
        ), "diarization_segments 字段不存在"

        # 检查 aligned_segments 字段是否已删除
        assert not hasattr(
            task_result, "aligned_segments"
        ), "aligned_segments 字段仍然存在"

        print("  ✅ 数据模型字段检查通过")
        return True

    except Exception as e:
        print(f"  ❌ 数据模型字段检查失败: {e}")
        return False


def test_read_interface():
    """测试读取接口逻辑"""
    print("2. 测试读取接口逻辑...")

    try:
        # 创建测试数据
        test_segments = [
            {
                "id": 1,
                "start_time": 0.0,
                "end_time": 5.0,
                "text": "Hello",
                "speaker": "A",
            },
            {
                "id": 2,
                "start_time": 5.0,
                "end_time": 10.0,
                "text": "World",
                "speaker": "B",
            },
        ]

        # 测试有说话人信息的情况
        task_result = TaskResult(
            id=999999,
            file_id=999999,
            duration=10.0,
            language="en",
            original_text="Hello World",
            segments=test_segments,
            original_segments=[
                {"id": 1, "start_time": 0.0, "end_time": 5.0, "text": "Hello"},
                {"id": 2, "start_time": 5.0, "end_time": 10.0, "text": "World"},
            ],
        )

        # 检查说话人信息检测逻辑
        contains_speaker_info = task_result.segments and any(
            "speaker" in seg for seg in task_result.segments if isinstance(seg, dict)
        )

        assert contains_speaker_info == True, "应该检测到说话人信息"

        # 测试没有说话人信息的情况
        test_segments_no_speaker = [
            {"id": 1, "start_time": 0.0, "end_time": 5.0, "text": "Hello"},
            {"id": 2, "start_time": 5.0, "end_time": 10.0, "text": "World"},
        ]

        task_result.segments = test_segments_no_speaker
        contains_speaker_info = task_result.segments and any(
            "speaker" in seg for seg in task_result.segments if isinstance(seg, dict)
        )

        assert contains_speaker_info == False, "不应该检测到说话人信息"

        print("  ✅ 读取接口逻辑检查通过")
        return True

    except Exception as e:
        print(f"  ❌ 读取接口逻辑检查失败: {e}")
        return False


def test_alignment_service():
    """测试对齐服务逻辑"""
    print("3. 测试对齐服务逻辑...")

    try:
        # 创建测试数据
        transcription_segments = [
            {"id": 1, "start_time": 0.0, "end_time": 5.0, "text": "Hello"},
            {"id": 2, "start_time": 5.0, "end_time": 10.0, "text": "World"},
        ]

        diarization_data = {
            "segments": [
                {"speaker": "A", "start": "0:00:00.000000", "stop": "0:00:05.000000"},
                {"speaker": "B", "start": "0:00:05.000000", "stop": "0:00:10.000000"},
            ],
            "speakers": {"count": 2, "labels": ["A", "B"]},
        }

        # 执行对齐
        aligned_segments = AlignmentService.align_segments(
            transcription_segments, diarization_data
        )

        # 验证结果
        assert len(aligned_segments) == 2, "对齐后的段数不正确"
        assert all(
            "speaker" in seg for seg in aligned_segments
        ), "所有段都应该有说话人信息"

        print("  ✅ 对齐服务逻辑检查通过")
        return True

    except Exception as e:
        print(f"  ❌ 对齐服务逻辑检查失败: {e}")
        return False


def test_database_schema():
    """测试数据库表结构"""
    print("4. 测试数据库表结构...")

    try:
        from sqlalchemy import text

        # 检查字段是否存在
        result = db.session.execute(
            text(
                """
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'task_result' 
                AND COLUMN_NAME IN ('segments', 'original_segments', 'diarization_segments', 'aligned_segments')
                ORDER BY COLUMN_NAME
            """
            )
        )

        fields = {row[0]: row for row in result.fetchall()}

        # 检查必要字段
        assert "segments" in fields, "segments 字段不存在"
        assert "original_segments" in fields, "original_segments 字段不存在"
        assert "diarization_segments" in fields, "diarization_segments 字段不存在"

        # 检查 aligned_segments 字段是否已删除
        assert "aligned_segments" not in fields, "aligned_segments 字段仍然存在"

        print("  ✅ 数据库表结构检查通过")
        print("  - segments: 存在")
        print("  - original_segments: 存在")
        print("  - diarization_segments: 存在")
        print("  - aligned_segments: 已删除")
        return True

    except Exception as e:
        print(f"  ❌ 数据库表结构检查失败: {e}")
        return False


def test_data_integrity():
    """测试数据完整性"""
    print("5. 测试数据完整性...")

    try:
        # 检查现有数据的完整性
        total_count = TaskResult.query.count()
        segments_count = TaskResult.query.filter(
            TaskResult.segments.isnot(None)
        ).count()
        original_segments_count = TaskResult.query.filter(
            TaskResult.original_segments.isnot(None)
        ).count()

        print(f"  - 总记录数: {total_count}")
        print(f"  - 有 segments 的记录: {segments_count}")
        print(f"  - 有 original_segments 的记录: {original_segments_count}")

        # 检查是否有记录的 segments 为空但 original_segments 不为空（这种情况不应该存在）
        invalid_records = TaskResult.query.filter(
            TaskResult.segments.is_(None), TaskResult.original_segments.isnot(None)
        ).count()

        assert (
            invalid_records == 0
        ), f"发现 {invalid_records} 条无效记录（segments 为空但 original_segments 不为空）"

        print("  ✅ 数据完整性检查通过")
        return True

    except Exception as e:
        print(f"  ❌ 数据完整性检查失败: {e}")
        return False


def main():
    """主函数"""
    app = create_app()

    print("🧪 开始测试重构后的 segments 数据结构...")
    print("=" * 60)

    with app.app_context():
        # 执行测试
        tests = [
            test_data_model,
            test_read_interface,
            test_alignment_service,
            test_database_schema,
            test_data_integrity,
        ]

        passed = 0
        failed = 0

        for test in tests:
            if test():
                passed += 1
            else:
                failed += 1

    print("=" * 60)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")

    if failed == 0:
        print("✅ 所有测试通过！重构成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查问题")
        return 1


if __name__ == "__main__":
    sys.exit(main())
