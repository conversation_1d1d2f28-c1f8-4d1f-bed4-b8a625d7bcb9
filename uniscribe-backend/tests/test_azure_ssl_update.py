#!/usr/bin/env python3
"""
测试 Azure MySQL SSL 证书更新

这个脚本用于验证新的合并证书文件是否能正常工作
"""

import os
import sys
from sqlalchemy import create_engine, text
from config import ProductionConfig

def test_ssl_connection_with_new_cert():
    """测试使用新合并证书的 SSL 连接"""
    
    print("🔒 测试 Azure MySQL SSL 证书更新...")
    print("=" * 60)
    
    # 检查证书文件是否存在
    config = ProductionConfig()
    cert_path = os.path.join(config.BASE_DIR, "cert", "azure-mysql-combined-ca.pem")
    
    if not os.path.exists(cert_path):
        print(f"❌ 证书文件不存在: {cert_path}")
        return False
    
    print(f"✅ 证书文件存在: {cert_path}")
    
    # 检查证书文件内容
    with open(cert_path, 'r') as f:
        cert_content = f.read()
        cert_count = cert_content.count('-----BEGIN CERTIFICATE-----')
        print(f"✅ 证书文件包含 {cert_count} 个证书")
        
        # 验证包含的证书
        if 'DigiCert Global Root CA' in cert_content:
            print("  - ✅ DigiCert Global Root CA")
        if 'DigiCert Global Root G2' in cert_content:
            print("  - ✅ DigiCert Global Root G2")
        if 'Microsoft RSA Root Certificate Authority 2017' in cert_content:
            print("  - ✅ Microsoft RSA Root Certificate Authority 2017")
    
    # 测试数据库连接
    database_url = os.getenv("SQLALCHEMY_DATABASE_URI")
    if not database_url:
        print("❌ SQLALCHEMY_DATABASE_URI 环境变量未设置")
        return False
    
    print(f"🔗 测试数据库连接...")
    
    try:
        # 创建引擎
        engine = create_engine(
            database_url,
            **config.SQLALCHEMY_ENGINE_OPTIONS
        )
        
        with engine.connect() as conn:
            # 检查 SSL 状态
            result = conn.execute(text("SHOW STATUS LIKE 'Ssl_cipher'"))
            ssl_info = result.fetchone()
            
            if ssl_info and ssl_info[1]:
                print(f"✅ SSL 连接成功，加密算法: {ssl_info[1]}")
                
                # 获取更多 SSL 信息
                ssl_queries = [
                    ("Ssl_version", "SSL 版本"),
                    ("Ssl_verify_mode", "证书验证模式"),
                    ("Ssl_verify_depth", "证书验证深度")
                ]
                
                for status_name, description in ssl_queries:
                    try:
                        result = conn.execute(text(f"SHOW STATUS LIKE '{status_name}'"))
                        info = result.fetchone()
                        if info and info[1]:
                            print(f"  - {description}: {info[1]}")
                    except Exception as e:
                        print(f"  - {description}: 无法获取 ({e})")
                
                # 测试简单查询
                result = conn.execute(text("SELECT 1 as test"))
                test_result = result.fetchone()
                if test_result and test_result[0] == 1:
                    print("✅ 数据库查询测试通过")
                else:
                    print("❌ 数据库查询测试失败")
                    return False
                    
            else:
                print("❌ SSL 连接未启用")
                return False
                
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False
    
    print("=" * 60)
    print("🎉 Azure MySQL SSL 证书更新测试通过！")
    print("📝 证书更新完成，可以安全使用到 2042 年")
    return True

def show_certificate_info():
    """显示证书信息"""
    print("\n📋 证书信息:")
    print("-" * 40)
    
    config = ProductionConfig()
    cert_path = os.path.join(config.BASE_DIR, "cert", "azure-mysql-combined-ca.pem")
    
    if os.path.exists(cert_path):
        # 使用 openssl 显示证书信息
        import subprocess
        try:
            result = subprocess.run([
                'openssl', 'x509', '-in', cert_path, '-text', '-noout'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # 提取关键信息
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Subject:' in line or 'Issuer:' in line or 'Not After:' in line:
                        print(f"  {line.strip()}")
            else:
                print("  无法解析证书信息")
        except Exception as e:
            print(f"  无法获取证书详细信息: {e}")

if __name__ == "__main__":
    success = test_ssl_connection_with_new_cert()
    
    if success:
        show_certificate_info()
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查配置")
        sys.exit(1)
