#!/usr/bin/env python3
"""
Test system-installed WeasyPrint functionality
"""

import sys
import os

def test_system_weasyprint():
    """Test system-installed WeasyPrint"""
    
    print("🧪 Testing system-installed WeasyPrint...")
    
    try:
        # Test WeasyPrint import
        from weasyprint import HTML, CSS
        from weasyprint.text.fonts import FontConfiguration
        print("✅ WeasyPrint import successful")
        
        # Create multilingual test HTML
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>System WeasyPrint Test</title>
        </head>
        <body>
            <h1>System WeasyPrint Multilingual Test</h1>
            <p>English: Hello World</p>
            <p>Korean: 안녕하세요 세계</p>
            <p>Arabic: مرحبا بالعالم</p>
            <p>Chinese: 你好世界</p>
            <p>Japanese: こんにちは世界</p>
            <p>Hungarian: <PERSON><PERSON>úüű</p>
            <p>Russian: Привет мир</p>
        </body>
        </html>
        """
        
        # CSS with font fallbacks
        css_content = """
        body {
            font-family: 'Noto Sans', 'Noto Sans CJK SC', 'DejaVu Sans', Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            margin: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        p {
            margin: 10px 0;
            padding: 5px;
            border-left: 3px solid #007acc;
            padding-left: 10px;
        }
        """
        
        print("📄 Generating PDF with system WeasyPrint...")
        
        # Generate PDF
        font_config = FontConfiguration()
        html_doc = HTML(string=html_content)
        css_doc = CSS(string=css_content, font_config=font_config)
        
        pdf_content = html_doc.write_pdf(stylesheets=[css_doc], font_config=font_config)
        
        # Save test file
        test_filename = "system_weasyprint_test.pdf"
        with open(test_filename, "wb") as f:
            f.write(pdf_content)
        
        print("✅ System WeasyPrint test successful!")
        print(f"📁 File saved as: {test_filename}")
        print(f"📊 File size: {len(pdf_content)} bytes")
        
        # Verify file
        if os.path.exists(test_filename) and os.path.getsize(test_filename) > 1000:
            print("✅ PDF file verification passed!")
            return True
        else:
            print("❌ PDF file verification failed!")
            return False
            
    except ImportError as e:
        print(f"❌ WeasyPrint import failed: {e}")
        print("Please install WeasyPrint: sudo apt install weasyprint")
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_system_fonts():
    """Check available system fonts"""
    
    print("🔍 Checking system fonts...")
    
    font_paths = [
        "/usr/share/fonts/truetype/noto/",
        "/usr/share/fonts/truetype/dejavu/",
        "/usr/share/fonts/truetype/liberation/",
        "/usr/share/fonts/opentype/noto/",
    ]
    
    found_fonts = []
    for font_path in font_paths:
        if os.path.exists(font_path):
            fonts = [f for f in os.listdir(font_path) if f.endswith(('.ttf', '.otf'))]
            if fonts:
                found_fonts.extend([f"{font_path}{font}" for font in fonts[:3]])  # Show first 3
    
    if found_fonts:
        print("✅ Found system fonts:")
        for font in found_fonts:
            print(f"  📝 {font}")
    else:
        print("⚠️  No system fonts found in common locations")
    
    return len(found_fonts) > 0

def check_weasyprint_command():
    """Check if weasyprint command is available"""
    
    print("🔧 Checking weasyprint command...")
    
    try:
        import subprocess
        result = subprocess.run(['weasyprint', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ WeasyPrint command available: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ WeasyPrint command failed: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ WeasyPrint command not found")
        return False
    except Exception as e:
        print(f"❌ Error checking WeasyPrint command: {e}")
        return False

if __name__ == "__main__":
    print("🚀 System WeasyPrint Test Started")
    print("=" * 50)
    
    # Check WeasyPrint command
    cmd_success = check_weasyprint_command()
    print()
    
    # Check system fonts
    fonts_success = check_system_fonts()
    print()
    
    # Test WeasyPrint functionality
    test_success = test_system_weasyprint()
    print()
    
    # Summary
    print("=" * 50)
    if cmd_success and test_success:
        print("🎉 All tests passed! System WeasyPrint is working correctly!")
        if fonts_success:
            print("✅ System fonts are available for multilingual support!")
        else:
            print("⚠️  Consider installing additional fonts: sudo apt install fonts-noto fonts-noto-cjk")
        sys.exit(0)
    else:
        print("💥 Tests failed! Please check WeasyPrint installation!")
        print("Try: sudo apt install weasyprint fonts-noto fonts-noto-cjk")
        sys.exit(1)
