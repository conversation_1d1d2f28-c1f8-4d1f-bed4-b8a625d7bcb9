#!/usr/bin/env python3
"""
测试对齐服务功能（不依赖完整应用环境）
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.alignment_service import AlignmentService


def test_alignment_service():
    """测试对齐服务"""
    print("Testing AlignmentService...")

    # 模拟转录结果
    transcription_segments = [
        {
            "id": 0,
            "start_time": 0.0,
            "end_time": 5.2,
            "text": "Hello, how are you today?",
        },
        {
            "id": 1,
            "start_time": 5.2,
            "end_time": 10.1,
            "text": "I'm doing great, thank you for asking.",
        },
        {
            "id": 2,
            "start_time": 10.1,
            "end_time": 15.5,
            "text": "That's wonderful to hear.",
        },
    ]

    # 模拟说话人识别结果
    diarization_data = {
        "segments": [
            {"speaker": "A", "start": "0:00:00.000000", "stop": "0:00:07.500000"},
            {"speaker": "B", "start": "0:00:07.500000", "stop": "0:00:12.000000"},
            {"speaker": "A", "start": "0:00:12.000000", "stop": "0:00:16.000000"},
        ],
        "speakers": {"count": 2, "labels": ["A", "B"]},
    }

    # 执行对齐
    aligned_segments = AlignmentService.align_segments(
        transcription_segments, diarization_data
    )

    print("Original segments:")
    for seg in transcription_segments:
        print(f"  {seg['id']}: {seg['start_time']}-{seg['end_time']} | {seg['text']}")

    print("\nDiarization segments:")
    for seg in diarization_data["segments"]:
        print(f"  {seg['speaker']}: {seg['start']}-{seg['stop']}")

    print("\nAligned segments:")
    for seg in aligned_segments:
        speaker = seg.get("speaker", "Unknown")
        print(
            f"  {seg['id']}: {seg['start_time']}-{seg['end_time']} | {speaker} | {seg['text']}"
        )

    # 验证结果
    assert len(aligned_segments) == len(transcription_segments)
    assert all("speaker" in seg for seg in aligned_segments)

    print("✓ AlignmentService test passed!")
    return True


def test_time_parsing():
    """测试时间解析"""
    print("\nTesting time parsing...")

    test_cases = [
        ("0:00:00.000000", 0.0),
        ("0:00:05.500000", 5.5),
        ("0:01:30.250000", 90.25),
        ("1:00:00.000000", 3600.0),
    ]

    for time_str, expected in test_cases:
        result = AlignmentService.parse_time_string(time_str)
        print(f"  {time_str} -> {result} (expected: {expected})")
        assert abs(result - expected) < 0.001, f"Expected {expected}, got {result}"

    print("✓ Time parsing test passed!")
    return True


def test_speaker_info_detection():
    """测试说话人信息检测逻辑"""
    print("\nTesting speaker info detection...")

    # 测试有说话人信息的情况
    segments_with_speaker = [
        {"id": 1, "start_time": 0.0, "end_time": 5.0, "text": "Hello", "speaker": "A"},
        {"id": 2, "start_time": 5.0, "end_time": 10.0, "text": "World", "speaker": "B"},
    ]

    contains_speaker_info = any(
        "speaker" in seg for seg in segments_with_speaker if isinstance(seg, dict)
    )
    assert contains_speaker_info == True, "应该检测到说话人信息"
    print("  ✓ 正确检测到说话人信息")

    # 测试没有说话人信息的情况
    segments_without_speaker = [
        {"id": 1, "start_time": 0.0, "end_time": 5.0, "text": "Hello"},
        {"id": 2, "start_time": 5.0, "end_time": 10.0, "text": "World"},
    ]

    contains_speaker_info = any(
        "speaker" in seg for seg in segments_without_speaker if isinstance(seg, dict)
    )
    assert contains_speaker_info == False, "不应该检测到说话人信息"
    print("  ✓ 正确检测到无说话人信息")

    print("✓ Speaker info detection test passed!")
    return True


def test_whisperx_alignment():
    """测试 WhisperX 对齐算法"""
    print("\nTesting WhisperX alignment...")

    # 创建词级别的测试数据
    word_segments = [
        {"id": 0, "start_time": 0.0, "end_time": 1.0, "text": "Hello"},
        {"id": 1, "start_time": 1.0, "end_time": 2.0, "text": "world"},
        {"id": 2, "start_time": 3.0, "end_time": 4.0, "text": "How"},
        {"id": 3, "start_time": 4.0, "end_time": 5.0, "text": "are"},
        {"id": 4, "start_time": 5.0, "end_time": 6.0, "text": "you"},
    ]

    diarization_data = {
        "segments": [
            {"speaker": "A", "start": "0:00:00.000000", "stop": "0:00:02.500000"},
            {"speaker": "B", "start": "0:00:02.500000", "stop": "0:00:06.000000"},
        ],
        "speakers": {"count": 2, "labels": ["A", "B"]},
    }

    # 测试不分组的对齐
    aligned_segments = AlignmentService.align_segments_whisperx(
        word_segments, diarization_data, group_by_speaker=False
    )

    assert len(aligned_segments) == len(word_segments)
    assert all("speaker" in seg for seg in aligned_segments)
    print("  ✓ WhisperX alignment (no grouping) passed")

    # 测试分组的对齐
    grouped_segments = AlignmentService.align_segments_whisperx(
        word_segments, diarization_data, group_by_speaker=True
    )

    assert len(grouped_segments) <= len(word_segments)  # 分组后段数可能减少
    assert all("speaker" in seg for seg in grouped_segments)
    print("  ✓ WhisperX alignment (with grouping) passed")

    print("✓ WhisperX alignment test passed!")
    return True


def main():
    """主函数"""
    print("🧪 测试对齐服务功能...")
    print("=" * 50)

    tests = [
        test_alignment_service,
        test_time_parsing,
        test_speaker_info_detection,
        test_whisperx_alignment,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"  ❌ Test failed: {e}")
            failed += 1

    print("=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")

    if failed == 0:
        print("✅ 所有测试通过！对齐服务功能正常！")
        return 0
    else:
        print("❌ 部分测试失败，请检查问题")
        return 1


if __name__ == "__main__":
    sys.exit(main())
