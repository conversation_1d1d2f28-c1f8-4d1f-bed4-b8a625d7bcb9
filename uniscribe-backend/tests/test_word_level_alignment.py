#!/usr/bin/env python3
"""
测试词级别的对齐算法
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models.task_result import TaskResult
from services.alignment_service import AlignmentService
import json


def test_word_level_alignment(file_id):
    """测试词级别的对齐算法"""
    with app.app_context():
        result = TaskResult.get_by_file_id(file_id)
        if not result:
            print(f"未找到文件 {file_id} 的结果")
            return
        
        print(f"=== 测试文件 {file_id} 的词级别对齐 ===")
        
        # 获取原始数据
        original_segments = result.original_segments
        diarization_data = result.diarization_segments
        
        if not original_segments or not diarization_data:
            print("缺少原始数据，无法测试")
            return
        
        print(f"原始转录段数: {len(original_segments)}")
        print(f"说话人识别段数: {len(diarization_data.get('segments', []))}")
        
        # 显示说话人识别的时间范围
        print("\n=== 说话人识别时间范围 ===")
        for i, seg in enumerate(diarization_data.get('segments', [])[:5]):  # 只显示前5个
            start_str = seg.get('start', '0:00:00')
            stop_str = seg.get('stop', '0:00:00')
            speaker = seg.get('speaker', 'Unknown')
            
            # 转换时间字符串为秒
            start_time = AlignmentService.parse_time_string(start_str)
            end_time = AlignmentService.parse_time_string(stop_str)
            
            print(f"  [{i}] {start_time:.3f}-{end_time:.3f}s | Speaker: {speaker}")
        
        # 使用修复后的对齐算法
        print("\n=== 使用修复后的对齐算法 ===")
        aligned_segments = AlignmentService.align_segments_whisperx(
            original_segments, diarization_data
        )
        
        # 分析第一个段落的词级别分配
        print("\n=== 第一个段落的词级别分配 ===")
        if aligned_segments and len(aligned_segments) > 0:
            first_segment = aligned_segments[0]
            if "words" in first_segment and first_segment["words"]:
                print(f"段落说话人: {first_segment.get('speaker', 'N/A')}")
                print(f"段落时间: {first_segment.get('start_time', 0):.3f}-{first_segment.get('end_time', 0):.3f}s")
                print("词级别分配:")
                
                for i, word in enumerate(first_segment["words"][:10]):  # 只显示前10个词
                    text = word.get('text', '')
                    start_time = word.get('start_time', 0)
                    end_time = word.get('end_time', 0)
                    speaker = word.get('speaker', 'N/A')
                    
                    # 检查是否在说话人A的范围内 (0.497812s - 17.912813s)
                    in_a_range = 0.497812 <= start_time <= 17.912813
                    range_status = "✅ 在A范围内" if in_a_range else "❌ 在A范围外"
                    
                    print(f"    [{i:2d}] '{text:8s}' | {start_time:.3f}-{end_time:.3f}s | Speaker: {speaker} | {range_status}")
                    
                    # 特别关注 "We're", "at", "the" 这几个词
                    if text.strip().lower() in ['we\'re', 'at', 'the']:
                        if not in_a_range and speaker == 'A':
                            print(f"         🎯 成功！'{text}' 在A范围外但被正确分配给A")
                        elif not in_a_range and speaker != 'A':
                            print(f"         ⚠️  问题：'{text}' 在A范围外但被分配给{speaker}")
        
        # 检查是否还有Unknown
        print("\n=== Unknown 检查 ===")
        unknown_words = []
        for seg in aligned_segments:
            if "words" in seg and seg["words"]:
                for word in seg["words"]:
                    if word.get("speaker") == "Unknown":
                        unknown_words.append(word)
        
        if unknown_words:
            print(f"发现 {len(unknown_words)} 个 Unknown 词:")
            for word in unknown_words[:5]:  # 只显示前5个
                print(f"  '{word.get('text', '')}' at {word.get('start_time', 0):.3f}s")
        else:
            print("✅ 没有发现 Unknown 词")


if __name__ == "__main__":
    file_id = int(sys.argv[1]) if len(sys.argv) > 1 else 7342100145535848448
    test_word_level_alignment(file_id)
