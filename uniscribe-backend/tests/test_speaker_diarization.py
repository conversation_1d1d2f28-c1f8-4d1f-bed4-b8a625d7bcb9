#!/usr/bin/env python3
"""
测试说话人识别功能

使用方法：
python cli/test_speaker_diarization.py
"""

import sys
import os
import json

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models.transcription_file import TranscriptionFile
from models.task_result import TaskResult
from models.task import Task
from constants.task import TaskType, TaskStatus
from services.alignment_service import AlignmentService
from services.alignment_queue_service import AlignmentQueueService


def test_alignment_service():
    """测试对齐服务"""
    print("Testing AlignmentService...")

    # 模拟转录结果
    transcription_segments = [
        {
            "id": 0,
            "start_time": 0.0,
            "end_time": 5.2,
            "text": "Hello, how are you today?",
        },
        {
            "id": 1,
            "start_time": 5.2,
            "end_time": 10.1,
            "text": "I'm doing great, thank you for asking.",
        },
        {
            "id": 2,
            "start_time": 10.1,
            "end_time": 15.5,
            "text": "That's wonderful to hear.",
        },
    ]

    # 模拟说话人识别结果
    diarization_data = {
        "segments": [
            {"speaker": "A", "start": "0:00:00.000000", "stop": "0:00:07.500000"},
            {"speaker": "B", "start": "0:00:07.500000", "stop": "0:00:12.000000"},
            {"speaker": "A", "start": "0:00:12.000000", "stop": "0:00:16.000000"},
        ],
        "speakers": {"count": 2, "labels": ["A", "B"]},
    }

    # 执行对齐
    aligned_segments = AlignmentService.align_segments(
        transcription_segments, diarization_data
    )

    print("Original segments:")
    for seg in transcription_segments:
        print(f"  {seg['id']}: {seg['start_time']}-{seg['end_time']} | {seg['text']}")

    print("\nDiarization segments:")
    for seg in diarization_data["segments"]:
        print(f"  {seg['speaker']}: {seg['start']}-{seg['stop']}")

    print("\nAligned segments:")
    for seg in aligned_segments:
        speaker = seg.get("speaker", "Unknown")
        print(
            f"  {seg['id']}: {seg['start_time']}-{seg['end_time']} | {speaker} | {seg['text']}"
        )

    # 验证结果
    assert len(aligned_segments) == len(transcription_segments)
    assert all("speaker" in seg for seg in aligned_segments)

    print("✓ AlignmentService test passed!")


def test_time_parsing():
    """测试时间解析"""
    print("\nTesting time parsing...")

    test_cases = [
        ("0:00:00.000000", 0.0),
        ("0:00:05.500000", 5.5),
        ("0:01:30.250000", 90.25),
        ("1:00:00.000000", 3600.0),
    ]

    for time_str, expected in test_cases:
        result = AlignmentService.parse_time_string(time_str)
        print(f"  {time_str} -> {result} (expected: {expected})")
        assert abs(result - expected) < 0.001, f"Expected {expected}, got {result}"

    print("✓ Time parsing test passed!")


def test_queue_service():
    """测试队列服务"""
    print("\nTesting AlignmentQueueService...")

    # 清空队列
    AlignmentQueueService.clear_stream()

    # 测试入队
    task_data = {
        "file_id": 123,
        "transcription_task_id": 456,
        "diarization_task_id": 789,
    }

    message_id = AlignmentQueueService.enqueue_alignment_task(task_data)
    print(f"  Enqueued task with message ID: {message_id}")

    # 测试队列长度
    count = AlignmentQueueService.get_pending_count()
    print(f"  Pending tasks count: {count}")
    assert count >= 1

    # 测试出队
    dequeued_task = AlignmentQueueService.dequeue_alignment_task(timeout=1000)
    print(f"  Dequeued task: {dequeued_task}")

    if dequeued_task:
        assert dequeued_task["file_id"] == "123"  # Redis 返回字符串

        # 测试确认
        ack_result = AlignmentQueueService.acknowledge_task(dequeued_task["message_id"])
        print(f"  Acknowledged task: {ack_result}")
        assert ack_result

    print("✓ AlignmentQueueService test passed!")


def test_database_fields():
    """测试数据库字段"""
    print("\nTesting database fields...")

    with app.app_context():
        # 测试 TranscriptionFile 的新字段
        from sqlalchemy import text
        from models import db

        try:
            result = db.session.execute(
                text(
                    """
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'transcription_file' 
                AND COLUMN_NAME = 'enable_speaker_diarization'
            """
                )
            )

            if result.fetchone():
                print("  ✓ transcription_file.enable_speaker_diarization field exists")
            else:
                print("  ✗ transcription_file.enable_speaker_diarization field missing")
                print("  Run: python cli/migrate_speaker_diarization.py")
                return False

            # 测试 TaskResult 的字段
            fields_to_check = ["diarization_segments", "original_segments"]

            for field_name in fields_to_check:
                result = db.session.execute(
                    text(
                        f"""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'task_result' 
                    AND COLUMN_NAME = '{field_name}'
                """
                    )
                )

                if result.fetchone():
                    print(f"  ✓ task_result.{field_name} field exists")
                else:
                    print(f"  ✗ task_result.{field_name} field missing")
                    print("  Run: python cli/migrate_speaker_diarization.py")
                    return False

            print("✓ Database fields test passed!")
            return True

        except Exception as e:
            print(f"  ✗ Database test failed: {e}")
            return False


def main():
    """主函数"""
    print("Speaker Diarization Feature Test")
    print("=" * 40)

    try:
        # 测试对齐服务
        test_alignment_service()

        # 测试时间解析
        test_time_parsing()

        # 测试队列服务
        test_queue_service()

        # 测试数据库字段
        test_database_fields()

        print("\n" + "=" * 40)
        print("All tests passed! 🎉")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
