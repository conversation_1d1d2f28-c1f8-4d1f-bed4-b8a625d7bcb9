#!/usr/bin/env python3
"""
Test CJK fonts with WeasyPrint
"""

import sys
import os

def test_cjk_fonts():
    """Test CJK fonts with explicit font specification"""
    
    print("🧪 Testing CJK fonts with WeasyPrint...")
    
    try:
        from weasyprint import HTML, CSS
        from weasyprint.text.fonts import FontConfiguration
        print("✅ WeasyPrint import successful")
        
        # Create HTML with explicit CJK font specification
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>CJK Font Test</title>
        </head>
        <body>
            <h1>CJK Font Test</h1>
            
            <div class="english">
                <h2>English</h2>
                <p>Hello World - This should work with any font</p>
            </div>
            
            <div class="chinese">
                <h2>Chinese (中文)</h2>
                <p>你好世界 - 这是中文测试</p>
                <p>简体中文：北京大学</p>
                <p>繁体中文：臺灣大學</p>
            </div>
            
            <div class="japanese">
                <h2>Japanese (日本語)</h2>
                <p>こんにちは世界 - これは日本語のテストです</p>
                <p>ひらがな：あいうえお</p>
                <p>カタカナ：アイウエオ</p>
                <p>漢字：東京大学</p>
            </div>
            
            <div class="korean">
                <h2>Korean (한국어)</h2>
                <p>안녕하세요 세계 - 이것은 한국어 테스트입니다</p>
                <p>한글：서울대학교</p>
            </div>
            
            <div class="mixed">
                <h2>Mixed CJK</h2>
                <p>中文 + 日本語 + 한국어 = CJK</p>
            </div>
        </body>
        </html>
        """
        
        # CSS with comprehensive CJK font fallbacks
        css_content = """
        body {
            font-family: 
                'Noto Sans CJK SC', 
                'Noto Sans CJK TC', 
                'Noto Sans CJK JP', 
                'Noto Sans CJK KR',
                'WenQuanYi Zen Hei',
                'WenQuanYi Micro Hei',
                'Takao PGothic',
                'Takao Gothic',
                'IPAexfont',
                'IPAfont',
                'Nanum Gothic',
                'Nanum Myeongjo',
                'DejaVu Sans',
                'Arial Unicode MS',
                sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            margin: 20px;
        }
        
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 5px;
        }
        
        .chinese {
            font-family: 
                'Noto Sans CJK SC', 
                'Noto Serif CJK SC',
                'WenQuanYi Zen Hei',
                'WenQuanYi Micro Hei',
                'AR PL UMing CN',
                'AR PL UKai CN',
                sans-serif;
        }
        
        .japanese {
            font-family: 
                'Noto Sans CJK JP', 
                'Noto Serif CJK JP',
                'Takao PGothic',
                'Takao Gothic',
                'IPAexfont',
                'IPAfont',
                sans-serif;
        }
        
        .korean {
            font-family: 
                'Noto Sans CJK KR', 
                'Noto Serif CJK KR',
                'Nanum Gothic',
                'Nanum Myeongjo',
                'UnDotum',
                sans-serif;
        }
        
        .mixed {
            font-family: 
                'Noto Sans CJK SC', 
                'Noto Sans CJK JP', 
                'Noto Sans CJK KR',
                'WenQuanYi Zen Hei',
                'Takao PGothic',
                'Nanum Gothic',
                sans-serif;
        }
        
        p {
            margin: 10px 0;
            padding: 8px;
            background-color: #f9f9f9;
            border-left: 4px solid #007acc;
        }
        """
        
        print("📄 Generating CJK PDF with explicit font specification...")
        
        # Generate PDF with font configuration
        font_config = FontConfiguration()
        html_doc = HTML(string=html_content)
        css_doc = CSS(string=css_content, font_config=font_config)
        
        pdf_content = html_doc.write_pdf(stylesheets=[css_doc], font_config=font_config)
        
        # Save test file
        test_filename = "cjk_font_test.pdf"
        with open(test_filename, "wb") as f:
            f.write(pdf_content)
        
        print("✅ CJK font test successful!")
        print(f"📁 File saved as: {test_filename}")
        print(f"📊 File size: {len(pdf_content)} bytes")
        
        return True
        
    except ImportError as e:
        print(f"❌ WeasyPrint import failed: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_system_fonts():
    """Check available CJK fonts on the system"""
    
    print("🔍 Checking available CJK fonts...")
    
    try:
        import subprocess
        
        # Check for CJK fonts
        result = subprocess.run(['fc-list', ':', 'family'], capture_output=True, text=True)
        
        if result.returncode == 0:
            fonts = result.stdout.lower()
            
            cjk_fonts = [
                ('noto sans cjk', 'Noto Sans CJK'),
                ('noto serif cjk', 'Noto Serif CJK'),
                ('wenquanyi', 'WenQuanYi'),
                ('takao', 'Takao'),
                ('nanum', 'Nanum'),
                ('ipaexfont', 'IPAexfont'),
                ('ipafont', 'IPAfont'),
                ('ar pl', 'AR PL'),
            ]
            
            found_fonts = []
            for font_key, font_name in cjk_fonts:
                if font_key in fonts:
                    found_fonts.append(font_name)
                    print(f"✅ Found: {font_name}")
                else:
                    print(f"❌ Missing: {font_name}")
            
            if found_fonts:
                print(f"\n🎉 Found {len(found_fonts)} CJK font families!")
                return True
            else:
                print("\n💥 No CJK fonts found!")
                print("Please install CJK fonts:")
                print("sudo apt install fonts-noto-cjk fonts-wqy-zenhei fonts-takao fonts-nanum")
                return False
        else:
            print("❌ Failed to check fonts")
            return False
            
    except Exception as e:
        print(f"❌ Error checking fonts: {e}")
        return False

if __name__ == "__main__":
    print("🚀 CJK Font Test for WeasyPrint")
    print("=" * 50)
    
    # Check system fonts first
    fonts_available = check_system_fonts()
    print()
    
    if not fonts_available:
        print("⚠️  CJK fonts not found. Please run:")
        print("chmod +x install_cjk_fonts.sh")
        print("./install_cjk_fonts.sh")
        print()
    
    # Test WeasyPrint with CJK
    test_success = test_cjk_fonts()
    print()
    
    # Summary
    print("=" * 50)
    if test_success:
        if fonts_available:
            print("🎉 Test completed! Check the generated PDF for CJK character display.")
        else:
            print("⚠️  Test completed but CJK fonts may not be available.")
            print("If CJK characters don't display, install fonts and try again.")
    else:
        print("💥 Test failed! Please check WeasyPrint installation.")
    
    sys.exit(0 if test_success else 1)
