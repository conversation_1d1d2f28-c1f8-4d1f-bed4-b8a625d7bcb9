#!/usr/bin/env python3
"""
测试对齐算法修复
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models.task_result import TaskResult
from services.alignment_service import AlignmentService
from sqlalchemy.orm.attributes import flag_modified
from models import db
import json


def test_alignment_fix(file_id):
    """测试对齐算法修复"""
    with app.app_context():
        result = TaskResult.get_by_file_id(file_id)
        if not result:
            print(f"未找到文件 {file_id} 的结果")
            return
        
        print(f"=== 测试文件 {file_id} 的对齐算法修复 ===")
        
        # 获取原始数据
        original_segments = result.original_segments
        diarization_data = result.diarization_segments
        
        if not original_segments or not diarization_data:
            print("缺少原始数据，无法测试")
            return
        
        print(f"原始转录段数: {len(original_segments)}")
        print(f"说话人识别段数: {len(diarization_data.get('segments', []))}")
        
        # 使用修复后的对齐算法
        print("\n=== 使用修复后的对齐算法 ===")
        aligned_segments = AlignmentService.align_segments_whisperx(
            original_segments, diarization_data
        )
        
        print(f"对齐后段数: {len(aligned_segments)}")
        
        # 分析结果
        print("\n=== 前一分钟的对齐结果 ===")
        unknown_count = 0
        for i, seg in enumerate(aligned_segments):
            if isinstance(seg, dict):
                start_time = seg.get('start_time', 0)
                end_time = seg.get('end_time', 0)
                text = seg.get('text', '')
                speaker = seg.get('speaker', 'N/A')
                
                # 只显示前60秒的数据
                if start_time <= 60:
                    status = "❌" if speaker == "Unknown" else "✅"
                    print(f"  {status} [{i}] {start_time:.2f}-{end_time:.2f}s | Speaker: {speaker} | Text: {text[:50]}...")
                    if speaker == "Unknown":
                        unknown_count += 1
                else:
                    break
        
        print(f"\n=== 修复结果统计 ===")
        print(f"Unknown 说话人段落数: {unknown_count}")
        
        if unknown_count == 0:
            print("🎉 修复成功！没有 Unknown 说话人段落")
            
            # 自动保存修复结果
            print("自动保存修复结果到数据库...")
            result.segments = aligned_segments
            result.is_aligned = True
            flag_modified(result, "segments")
            flag_modified(result, "is_aligned")
            db.session.commit()
            print("✅ 修复结果已保存到数据库")
        else:
            print(f"⚠️ 仍有 {unknown_count} 个 Unknown 说话人段落，需要进一步优化算法")


if __name__ == "__main__":
    file_id = int(sys.argv[1]) if len(sys.argv) > 1 else 7342100145535848448
    test_alignment_fix(file_id)
