#!/usr/bin/env python3
"""
测试改进的边界判断算法
"""

import sys
import os
import time
import json
from typing import List, Dict

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.alignment_service import AlignmentService


def create_boundary_test_data():
    """创建边界测试数据"""
    
    # 关键的边界词
    word_segments = [
        # Speaker A 的最后几个词
        {"id": 2514, "text": " It", "end_time": 889.4, "start_time": 889.3},
        {"id": 2515, "text": " was", "end_time": 889.56, "start_time": 889.4},
        {"id": 2516, "text": " a", "end_time": 889.7, "start_time": 889.56},
        {"id": 2517, "text": " miracle.", "end_time": 890.0, "start_time": 889.7},
        
        # 边界区域的词 - 这些应该属于 Speaker B
        {"id": 2518, "text": " If", "end_time": 890.26, "start_time": 890.18},
        {"id": 2519, "text": " I", "end_time": 890.36, "start_time": 890.26},
        {"id": 2520, "text": " hadn't", "end_time": 890.68, "start_time": 890.36},
        {"id": 2521, "text": " turned", "end_time": 890.86, "start_time": 890.68},
    ]
    
    # Diarization 数据
    diarization_data = {
        "segments": [
            {
                "speaker": "A",
                "start": "0:14:42.165938",  # 882.165938 秒
                "stop": "0:14:50.772188"    # 890.772188 秒
            },
            {
                "speaker": "B", 
                "start": "0:14:49.202813",  # 889.202813 秒
                "stop": "0:14:54.737813"    # 894.737813 秒
            }
        ],
        "speakers": {
            "count": 2,
            "labels": ["A", "B"]
        }
    }
    
    return word_segments, diarization_data


def test_word_level_assignments():
    """测试词级别的说话人分配"""
    print("Testing Improved Word-Level Speaker Assignment")
    print("=" * 60)
    
    word_segments, diarization_data = create_boundary_test_data()
    
    # 使用改进算法，但不分组，这样我们可以看到每个词的分配
    aligned_segments = AlignmentService.align_segments_whisperx(
        word_segments, diarization_data, group_by_speaker=False
    )
    
    print("Diarization ranges:")
    print("  Speaker A: 14:42.17 - 14:50.77 (center: 14:46.47)")
    print("  Speaker B: 14:49.20 - 14:54.74 (center: 14:51.97)")
    print("  Overlap:   14:49.20 - 14:50.77")
    print()
    
    print("Word-level assignments with improved algorithm:")
    
    correct_assignments = 0
    total_assignments = 0
    
    for seg in aligned_segments:
        speaker = seg.get("speaker", "Unknown")
        start_min = int(seg['start_time'] // 60)
        start_sec = seg['start_time'] % 60
        end_min = int(seg['end_time'] // 60)
        end_sec = seg['end_time'] % 60
        center = (seg['start_time'] + seg['end_time']) / 2
        center_min = int(center // 60)
        center_sec = center % 60
        
        # 判断期望的说话人
        # 基于中心点位置和说话人段中心的距离
        a_center = (882.165938 + 890.772188) / 2  # 886.469
        b_center = (889.202813 + 894.737813) / 2  # 891.970
        
        distance_to_a = abs(center - a_center)
        distance_to_b = abs(center - b_center)
        
        expected_speaker = "A" if distance_to_a < distance_to_b else "B"
        
        is_correct = speaker == expected_speaker
        status = "✅" if is_correct else "❌"
        
        print(f"  {seg['id']}: {start_min}:{start_sec:06.3f}-{end_min}:{end_sec:06.3f} (center: {center_min}:{center_sec:06.3f})")
        print(f"    Text: '{seg['text']}' | Assigned: {speaker} | Expected: {expected_speaker} {status}")
        print(f"    Distance to A: {distance_to_a:.3f}s, Distance to B: {distance_to_b:.3f}s")
        print()
        
        if is_correct:
            correct_assignments += 1
        total_assignments += 1
    
    accuracy = correct_assignments / total_assignments * 100
    print(f"Assignment Accuracy: {correct_assignments}/{total_assignments} ({accuracy:.1f}%)")
    
    return accuracy


def test_grouped_assignments():
    """测试分组后的说话人分配"""
    print("\n" + "=" * 60)
    print("Testing Grouped Speaker Assignment")
    print("=" * 60)
    
    word_segments, diarization_data = create_boundary_test_data()
    
    # 使用改进算法并分组
    aligned_segments = AlignmentService.align_segments_whisperx(
        word_segments, diarization_data, group_by_speaker=True
    )
    
    print("Grouped segments:")
    
    for seg in aligned_segments:
        speaker = seg.get("speaker", "Unknown")
        start_min = int(seg['start_time'] // 60)
        start_sec = seg['start_time'] % 60
        end_min = int(seg['end_time'] // 60)
        end_sec = seg['end_time'] % 60
        word_count = len(seg.get("words", []))
        
        print(f"  Segment {seg['id']}: {start_min}:{start_sec:06.3f}-{end_min}:{end_sec:06.3f}")
        print(f"    Speaker: {speaker} | Words: {word_count}")
        print(f"    Text: {seg['text']}")
        
        # 显示词级别的详细信息
        words = seg.get("words", [])
        if words:
            print("    Word details:")
            for word in words:
                word_speaker = word.get("speaker", "Unknown")
                word_start_min = int(word['start_time'] // 60)
                word_start_sec = word['start_time'] % 60
                print(f"      {word_start_min}:{word_start_sec:06.3f} | {word_speaker} | '{word['text']}'")
        print()


def analyze_speaker_centers():
    """分析说话人中心点的计算"""
    print("=" * 60)
    print("Speaker Center Analysis")
    print("=" * 60)
    
    # 计算说话人段的中心点
    a_start = 882.165938
    a_end = 890.772188
    a_center = (a_start + a_end) / 2
    a_duration = a_end - a_start
    
    b_start = 889.202813
    b_end = 894.737813
    b_center = (b_start + b_end) / 2
    b_duration = b_end - b_start
    
    overlap_start = max(a_start, b_start)
    overlap_end = min(a_end, b_end)
    overlap_duration = overlap_end - overlap_start
    
    print(f"Speaker A:")
    print(f"  Range: {a_start:.3f} - {a_end:.3f} seconds")
    print(f"  Duration: {a_duration:.3f} seconds")
    print(f"  Center: {a_center:.3f} seconds")
    print()
    
    print(f"Speaker B:")
    print(f"  Range: {b_start:.3f} - {b_end:.3f} seconds")
    print(f"  Duration: {b_duration:.3f} seconds")
    print(f"  Center: {b_center:.3f} seconds")
    print()
    
    print(f"Overlap:")
    print(f"  Range: {overlap_start:.3f} - {overlap_end:.3f} seconds")
    print(f"  Duration: {overlap_duration:.3f} seconds")
    print(f"  Center: {(overlap_start + overlap_end) / 2:.3f} seconds")
    print()
    
    # 分析关键词的位置
    critical_words = [
        {"text": " miracle.", "start": 889.7, "end": 890.0},
        {"text": " If", "start": 890.18, "end": 890.26},
        {"text": " I", "start": 890.26, "end": 890.36},
        {"text": " hadn't", "start": 890.36, "end": 890.68},
    ]
    
    print("Critical words analysis:")
    for word in critical_words:
        center = (word['start'] + word['end']) / 2
        distance_to_a = abs(center - a_center)
        distance_to_b = abs(center - b_center)
        
        closer_to = "A" if distance_to_a < distance_to_b else "B"
        
        print(f"  '{word['text']}' (center: {center:.3f})")
        print(f"    Distance to A center: {distance_to_a:.3f}s")
        print(f"    Distance to B center: {distance_to_b:.3f}s")
        print(f"    Closer to: Speaker {closer_to}")
        print()


def main():
    """主函数"""
    accuracy = test_word_level_assignments()
    test_grouped_assignments()
    analyze_speaker_centers()
    
    print("=" * 60)
    print("Summary")
    print("=" * 60)
    print(f"Word-level assignment accuracy: {accuracy:.1f}%")
    
    if accuracy >= 90:
        print("✅ Excellent accuracy! The improved algorithm is working well.")
    elif accuracy >= 75:
        print("⚠️  Good accuracy, but there's room for improvement.")
    else:
        print("❌ Poor accuracy, the algorithm needs further refinement.")


if __name__ == "__main__":
    main()
