"""
测试异常装饰器
"""

import unittest
from exceptions.base import BaseAPIException
from exceptions.error_codes import ErrorCode
from exceptions.decorators import register_exception
import exceptions


class TestExceptionDecorator(unittest.TestCase):
    """测试异常装饰器"""

    def test_register_exception(self):
        """测试register_exception装饰器是否正确注册异常类"""
        # 定义一个测试异常类
        @register_exception
        class TestException(BaseAPIException):
            code = 400
            error_code = ErrorCode.UNKNOWN

        # 检查异常类是否已注册到exceptions命名空间
        self.assertTrue(
            hasattr(exceptions, "TestException"),
            "TestException should be registered in exceptions namespace",
        )
        self.assertEqual(
            exceptions.TestException, TestException, "TestException should be the same class"
        )


if __name__ == "__main__":
    unittest.main()
