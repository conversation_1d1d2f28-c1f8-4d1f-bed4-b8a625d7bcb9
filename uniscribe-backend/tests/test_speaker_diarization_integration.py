#!/usr/bin/env python3
"""
说话人识别功能集成测试

测试完整的说话人识别流程：
1. 创建转录文件（开启说话人识别）
2. 创建转录任务和说话人识别任务
3. 模拟任务完成
4. 验证对齐结果

使用方法：
python cli/test_speaker_diarization_integration.py
"""

import sys
import os
import json

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models.transcription_file import TranscriptionFile
from models.task import Task
from models.task_result import TaskResult
from models.user import User
from models import db
from constants.task import TaskType, TaskStatus
from constants.transcription_file import TranscriptionFileStatus
from controllers.task import create_transcription_task, save_task_result
from controllers.transcription import create_transcription_file
from services.alignment_queue_service import AlignmentQueueService
from libs.id_generator import id_generator


def create_test_user():
    """创建测试用户"""
    user_id = id_generator.get_id()
    user = User(
        id=user_id,
        email=f"test_{user_id}@example.com",
        name="Test User",
        has_paid_plan=True,  # 付费用户，优先级高
    )
    db.session.add(user)
    db.session.commit()
    return user


def test_speaker_diarization_workflow():
    """测试说话人识别完整工作流程"""
    print("Testing speaker diarization workflow...")

    with app.app_context():
        # 1. 创建测试用户
        user = create_test_user()
        print(f"✓ Created test user: {user.id}")

        # 2. 创建转录文件（开启说话人识别）
        transcription_file = create_transcription_file(
            user_id=user.id,
            filename="test_audio.mp3",
            file_type="mp3",
            file_size=1024000,
            fingerprint="test_fingerprint",
            duration=120.5,
            language_code="en",
            transcription_type="transcript",
            enable_speaker_diarization=True,  # 开启说话人识别
        )
        print(
            f"✓ Created transcription file with speaker diarization: {transcription_file.id}"
        )

        # 3. 创建转录任务（应该自动创建说话人识别任务）
        transcription_task = create_transcription_task(user.id, transcription_file.id)
        print(f"✓ Created transcription task: {transcription_task.id}")

        # 4. 验证说话人识别任务是否被创建
        diarization_task = Task.get_by_file_id_and_type(
            transcription_file.id, TaskType.speaker_diarization.id
        )
        assert (
            diarization_task is not None
        ), "Speaker diarization task should be created"
        print(f"✓ Speaker diarization task created: {diarization_task.id}")

        # 5. 模拟转录任务完成
        mock_transcription_segments = [
            {
                "id": 0,
                "start_time": 0.0,
                "end_time": 5.2,
                "text": "Hello, how are you today?",
            },
            {
                "id": 1,
                "start_time": 5.2,
                "end_time": 10.1,
                "text": "I'm doing great, thank you for asking.",
            },
            {
                "id": 2,
                "start_time": 10.1,
                "end_time": 15.5,
                "text": "That's wonderful to hear.",
            },
        ]

        save_task_result(
            task_id=transcription_task.id,
            task_type=TaskType.transcription.id,
            original_text="Hello, how are you today? I'm doing great, thank you for asking. That's wonderful to hear.",
            segments=mock_transcription_segments,
            detected_language="en",
            service_provider="test_provider",
        )
        print("✓ Transcription task completed")

        # 6. 模拟说话人识别任务完成
        mock_diarization_data = {
            "segments": [
                {"speaker": "A", "start": "0:00:00.000000", "stop": "0:00:07.500000"},
                {"speaker": "B", "start": "0:00:07.500000", "stop": "0:00:12.000000"},
                {"speaker": "A", "start": "0:00:12.000000", "stop": "0:00:16.000000"},
            ],
            "speakers": {
                "count": 2,
                "labels": ["A", "B"],
                "embeddings": {"A": [1.0, 2.0, 3.0], "B": [4.0, 5.0, 6.0]},
            },
        }

        save_task_result(
            task_id=diarization_task.id,
            task_type=TaskType.speaker_diarization.id,
            diarization_segments=mock_diarization_data,
        )
        print("✓ Speaker diarization task completed")

        # 7. 验证对齐任务是否被推送到队列
        alignment_task = AlignmentQueueService.dequeue_alignment_task(timeout=1000)
        assert alignment_task is not None, "Alignment task should be enqueued"
        print(f"✓ Alignment task enqueued: {alignment_task}")

        # 8. 确认对齐任务
        AlignmentQueueService.acknowledge_task(alignment_task["message_id"])
        print("✓ Alignment task acknowledged")

        # 9. 验证任务状态
        transcription_task = Task.get_by_id(transcription_task.id)
        diarization_task = Task.get_by_id(diarization_task.id)

        assert transcription_task.status == TaskStatus.completed.id
        assert diarization_task.status == TaskStatus.completed.id
        print("✓ Both tasks completed successfully")

        # 10. 验证结果存储
        task_result = TaskResult.get_by_file_id(transcription_file.id)
        assert task_result is not None
        assert task_result.segments is not None
        assert task_result.diarization_segments is not None
        print("✓ Results stored correctly")

        # 11. 清理测试数据
        db.session.delete(user)
        db.session.delete(transcription_file)
        db.session.delete(transcription_task)
        db.session.delete(diarization_task)
        db.session.delete(task_result)
        db.session.commit()
        print("✓ Test data cleaned up")

        print(
            "\n🎉 All tests passed! Speaker diarization workflow is working correctly."
        )


def test_api_endpoints():
    """测试 API 端点"""
    print("\nTesting API endpoints...")

    # 测试任务获取端点
    from resources.task import TaskNextResource

    # 这里可以添加更多的 API 测试
    print("✓ API endpoints test completed")


def main():
    """主函数"""
    print("Speaker Diarization Integration Test")
    print("=" * 50)

    try:
        # 测试工作流程
        test_speaker_diarization_workflow()

        # 测试 API 端点
        test_api_endpoints()

        print("\n" + "=" * 50)
        print("🎉 All integration tests passed!")

    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
