from datetime import datetime, date
from sqlalchemy import func, text
from sqlalchemy.dialects.mysql import JSON
from models import db


class UserActivity(db.Model):
    __tablename__ = "user_activity"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("user_id", "activity_date", name="uk_user_activity_date"),
        db.Index("idx_activity_date", "activity_date"),
        db.Index("idx_user_id", "user_id"),
        db.Index("idx_last_activity_time", "last_activity_time"),
        {"comment": "用户活跃记录表，记录用户每日活跃情况"},
    )

    id = db.Column(
        db.BigInteger, primary_key=True, autoincrement=True, comment="主键ID"
    )
    user_id = db.Column(db.BigInteger, nullable=False, comment="用户ID")
    activity_date = db.Column(db.Date, nullable=False, comment="活跃日期（UTC时区）")
    last_activity_time = db.Column(
        db.TIMESTAMP, nullable=False, comment="最后活跃时间（UTC时区）"
    )
    is_anonymous = db.Column(db.Boolean, nullable=False, comment="活跃时是否为匿名用户")
    has_free_plan = db.Column(
        db.Boolean,
        nullable=False,
        server_default=text("false"),
        comment="是否拥有免费计划",
    )
    has_subscription = db.Column(
        db.Boolean,
        nullable=False,
        server_default=text("false"),
        comment="是否拥有订阅权益",
    )
    has_ltd = db.Column(
        db.Boolean,
        nullable=False,
        server_default=text("false"),
        comment="是否拥有LTD权益",
    )
    has_one_time = db.Column(
        db.Boolean,
        nullable=False,
        server_default=text("false"),
        comment="是否拥有一次性付费权益",
    )
    is_merged = db.Column(
        db.Boolean,
        nullable=False,
        server_default=text("false"),
        comment="是否已合并到注册用户",
    )
    merged_to_user_id = db.Column(
        db.BigInteger, nullable=True, comment="合并到的目标用户ID"
    )
    merged_time = db.Column(db.TIMESTAMP, nullable=True, comment="合并时间")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )

    def __repr__(self):
        return f"<UserActivity {self.user_id} on {self.activity_date}>"

    def to_dict(self):
        """将用户活跃记录对象转换为字典"""
        return {
            "id": self.id,
            "userId": self.user_id,
            "activityDate": (
                self.activity_date.isoformat() if self.activity_date else None
            ),
            "lastActivityTime": (
                self.last_activity_time.isoformat() if self.last_activity_time else None
            ),
            "isAnonymous": self.is_anonymous,
            "hasFreePlan": self.has_free_plan,
            "hasSubscription": self.has_subscription,
            "hasLtd": self.has_ltd,
            "hasOneTime": self.has_one_time,
            "isMerged": self.is_merged,
            "mergedToUserId": self.merged_to_user_id,
            "mergedTime": self.merged_time.isoformat() if self.merged_time else None,
            "createdTime": self.created_time.isoformat() if self.created_time else None,
        }

    @classmethod
    def get_by_user_and_date(cls, user_id, activity_date):
        """根据用户ID和活跃日期获取记录"""
        return cls.query.filter_by(user_id=user_id, activity_date=activity_date).first()

    @classmethod
    def create_or_update_activity(cls, user_id, activity_time=None):
        """创建或更新用户活跃记录

        Args:
            user_id: 用户ID
            activity_time: 活跃时间，默认为当前时间（UTC）

        Returns:
            UserActivity: 创建或更新的活跃记录
        """
        # 验证 user_id 有效性
        if not user_id or not isinstance(user_id, int) or user_id <= 0:
            raise ValueError(f"Invalid user_id: {user_id}")

        if activity_time is None:
            activity_time = datetime.utcnow()

        # 获取活跃日期（UTC时区）
        activity_date = activity_time.date()

        # 查找是否已存在当天的记录
        existing_record = cls.get_by_user_and_date(user_id, activity_date)

        if existing_record:
            # 更新现有记录的最后活跃时间（用户状态快照不更新，保持首次记录时的状态）
            existing_record.last_activity_time = activity_time
            return existing_record
        else:
            # 获取用户当前状态快照
            user_status = cls._get_user_status_snapshot(user_id)
            is_anonymous = user_status["is_anonymous"]
            entitlement_flags = user_status["entitlement_flags"]

            # 创建新记录，包含用户状态快照
            new_record = cls(
                user_id=user_id,
                activity_date=activity_date,
                last_activity_time=activity_time,
                is_anonymous=is_anonymous,
                has_free_plan=entitlement_flags.get("has_free_plan", False),
                has_subscription=entitlement_flags.get("has_subscription", False),
                has_ltd=entitlement_flags.get("has_ltd", False),
                has_one_time=entitlement_flags.get("has_one_time", False),
            )
            db.session.add(new_record)
            return new_record

    @classmethod
    def _get_user_status_snapshot(cls, user_id):
        """获取用户当前状态快照

        Args:
            user_id: 用户ID

        Returns:
            dict: 包含用户状态信息的字典
        """
        from models.user import User
        from models.entitlement import Entitlement, EntitlementSource

        # 默认值
        result = {
            "is_anonymous": True,
            "entitlement_flags": {
                "has_free_plan": True,
                "has_subscription": False,
                "has_ltd": False,
                "has_one_time": False,
            },
        }

        user = User.get_by_id(user_id)
        if not user:
            return result

        result["is_anonymous"] = user.is_anonymous

        # 获取用户的所有有效权益
        entitlements = Entitlement.query.filter(
            Entitlement.user_id == user_id, Entitlement.valid_until >= datetime.utcnow()
        ).all()

        if not entitlements:
            return result

        # 重置默认的免费计划标记
        result["entitlement_flags"]["has_free_plan"] = False

        # 根据实际权益设置标记
        for entitlement in entitlements:
            source_type = entitlement.source_type
            if source_type == EntitlementSource.FREE_PLAN.value:
                result["entitlement_flags"]["has_free_plan"] = True
            elif source_type == EntitlementSource.SUBSCRIPTION.value:
                result["entitlement_flags"]["has_subscription"] = True
            elif source_type == EntitlementSource.LTD.value:
                result["entitlement_flags"]["has_ltd"] = True
            elif source_type == EntitlementSource.ONE_TIME.value:
                result["entitlement_flags"]["has_one_time"] = True

        return result

    @classmethod
    def mark_as_merged(cls, anonymous_user_id, target_user_id, merge_time=None):
        """标记匿名用户的活跃记录为已合并

        Args:
            anonymous_user_id: 匿名用户ID
            target_user_id: 目标用户ID
            merge_time: 合并时间，默认为当前时间

        Returns:
            int: 标记的记录数量
        """
        if merge_time is None:
            merge_time = datetime.utcnow()

        updated_count = cls.query.filter_by(
            user_id=anonymous_user_id, is_merged=False
        ).update(
            {
                "is_merged": True,
                "merged_to_user_id": target_user_id,
                "merged_time": merge_time,
            }
        )
        return updated_count
