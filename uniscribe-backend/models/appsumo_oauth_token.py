from models import db
from datetime import datetime
from sqlalchemy import func

class AppSumoOAuthToken(db.Model):
    __tablename__ = "appsumo_oauth_token"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("user_id"),
        db.UniqueConstraint("license_key"),
        {"comment": "AppSumo OAuth 令牌表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=False, comment="用户ID")
    license_key = db.Column(db.String(255), nullable=False, comment="关联的 license key")
    access_token = db.Column(db.String(255), nullable=False, comment="OAuth access token")
    refresh_token = db.Column(db.String(255), nullable=False, comment="OAuth refresh token")
    expires_at = db.Column(db.TIMESTAMP, nullable=False, comment="token 过期时间")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    @classmethod
    def get_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id).first()

    @classmethod
    def get_by_license_key(cls, license_key):
        return cls.query.filter_by(license_key=license_key).first()

    @classmethod
    def get_by_access_token(cls, access_token):
        return cls.query.filter_by(access_token=access_token).first()
