from contextlib import contextmanager
from functools import wraps

from flask_sqlalchemy import SQLAlchemy


db = SQLAlchemy(
    engine_options={"pool_pre_ping": True, "pool_size": 20, "max_overflow": 5},
    session_options={"autoflush": False},
)


@contextmanager
def db_transaction():
    """
    用法： 如果是在函数内部使用（比如 handler 内简单的数据操作），可以用 with 语法
    e.g.
    def some_data_operation():
        with db_transaction() as session:
            do_sth()
    无需 commit 和 rollback，会自动处理
    """
    session = db.session()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        raise e


def db_transaction_decorator(func):
    """
    装饰器版本的数据库事务管理

    用法：
    @db_transaction_decorator
    def some_data_operation():
        do_sth()
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            db.session.commit()
            return result
        except Exception as e:
            db.session.rollback()
            raise e
    return wrapper


class TransactionCallbacks:
    """事务后回调管理器"""

    def __init__(self):
        self._callbacks = []

    def add_callback(self, callback, *args, **kwargs):
        """添加事务提交后的回调"""
        self._callbacks.append((callback, args, kwargs))

    def execute_callbacks(self):
        """执行所有回调"""
        callbacks = self._callbacks.copy()
        self._callbacks.clear()

        for callback, args, kwargs in callbacks:
            try:
                callback(*args, **kwargs)
            except Exception as e:
                # 记录回调执行失败，但不影响其他回调
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Transaction callback failed: {e}", exc_info=True)


# 使用线程本地存储，确保每个线程有独立的回调管理器
import threading
_thread_local = threading.local()


def _get_transaction_callbacks():
    """获取当前线程的事务回调管理器"""
    if not hasattr(_thread_local, 'callbacks'):
        _thread_local.callbacks = TransactionCallbacks()
    return _thread_local.callbacks


def add_transaction_callback(callback, *args, **kwargs):
    """添加事务提交后的回调函数"""
    callbacks = _get_transaction_callbacks()
    callbacks.add_callback(callback, *args, **kwargs)


def db_transaction_with_callbacks(func):
    """
    支持事务后回调的装饰器

    用法：
    @db_transaction_with_callbacks
    def some_data_operation():
        # 数据库操作
        do_database_work()

        # 添加事务提交后的回调
        add_transaction_callback(enqueue_task, 'transcription', task_data)
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            db.session.commit()

            # 事务提交成功后执行回调
            callbacks = _get_transaction_callbacks()
            callbacks.execute_callbacks()

            return result
        except Exception as e:
            db.session.rollback()
            # 清除回调，因为事务失败了
            callbacks = _get_transaction_callbacks()
            callbacks._callbacks.clear()
            raise e
    return wrapper


@contextmanager
def db_transaction_with_callbacks_context():
    """
    支持事务后回调的上下文管理器

    用法：
    with db_transaction_with_callbacks_context():
        # 数据库操作
        do_database_work()

        # 添加事务提交后的回调
        add_transaction_callback(enqueue_task, 'transcription', task_data)
    """
    try:
        yield
        db.session.commit()

        # 事务提交成功后执行回调
        callbacks = _get_transaction_callbacks()
        callbacks.execute_callbacks()

    except Exception as e:
        db.session.rollback()
        # 清除回调，因为事务失败了
        callbacks = _get_transaction_callbacks()
        callbacks._callbacks.clear()
        raise e


def insert_records(records):
    session = db.session()
    for record in records:
        session.add(record)
    session.flush()
    return [record.id for record in records]


def insert_record(record):
    return insert_records([record])[0]
