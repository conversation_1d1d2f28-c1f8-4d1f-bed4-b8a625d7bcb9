from datetime import datetime

from models import db
from constants.file_storage import FileState


class FileStorage(db.Model):
    """
    文件存储模型，用于跟踪存储在CF R2中的实际文件及其引用计数
    """

    __tablename__ = "file_storage"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("user_id", "fingerprint", name="uk_user_fingerprint"),
        db.Index("idx_userId", "user_id"),
        db.Index("idx_fingerprint", "fingerprint"),
        db.Index("idx_createdTime", "created_time"),
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=False)
    fingerprint = db.Column(db.String(32), nullable=False, comment="文件MD5指纹")
    file_type = db.Column(db.String(20), nullable=False)
    file_key = db.Column(db.String(255), nullable=False, comment="存储系统中的文件键")
    file_size = db.Column(db.Integer, nullable=False)
    reference_count = db.Column(
        db.Integer, nullable=False, default=1, comment="引用计数"
    )
    state = db.Column(
        db.Enum(*[e.value for e in FileState], name="file_state"),
        nullable=False,
        default=FileState.ACTIVE.value,
        comment="文件状态",
    )
    created_time = db.Column(db.TIMESTAMP, nullable=False, default=datetime.now)
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
    )
    last_access_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        default=datetime.now,
        comment="最后访问时间，用于判断文件是否过期",
    )

    def __repr__(self):
        return f"<FileStorage {self.id} user_id={self.user_id} fingerprint={self.fingerprint} refs={self.reference_count}>"

    @classmethod
    def get_by_user_id_and_fingerprint(cls, user_id, fingerprint):
        """根据用户ID和文件指纹获取活跃状态的文件存储记录"""
        return cls.query.filter_by(
            user_id=user_id,
            fingerprint=fingerprint,
            state=FileState.ACTIVE.value,  # 只查询活跃状态的文件
        ).first()

    @classmethod
    def get_by_user_id_and_fingerprint_any_state(cls, user_id, fingerprint):
        """根据用户ID和文件指纹获取文件存储记录，不考虑状态"""
        return cls.query.filter_by(user_id=user_id, fingerprint=fingerprint).first()

    @classmethod
    def increment_reference(cls, user_id, fingerprint):
        """增加文件引用计数"""
        file_storage = cls.get_by_user_id_and_fingerprint(user_id, fingerprint)
        if file_storage:
            file_storage.reference_count += 1
            file_storage.last_access_time = datetime.now()
            db.session.commit()
            return file_storage
        return None

    @classmethod
    def decrement_reference(cls, user_id, fingerprint):
        """减少文件引用计数，如果计数为0则标记为待删除"""
        file_storage = cls.get_by_user_id_and_fingerprint(user_id, fingerprint)
        if file_storage:
            file_storage.reference_count -= 1
            if file_storage.reference_count <= 0:
                # 更新状态为待删除
                file_storage.state = FileState.PENDING_DELETION.value

                # 检查并清理相关的活跃 multipart upload 会话
                cls._cleanup_active_multipart_uploads(file_storage)

            db.session.commit()
            return file_storage
        return None

    @classmethod
    def get_expired_files(cls, days=30):
        """获取超过指定天数未访问的文件"""
        from datetime import timedelta

        expiry_date = datetime.now() - timedelta(days=days)
        return cls.query.filter(
            cls.last_access_time < expiry_date,
            cls.state == FileState.ACTIVE.value,  # 只查询活跃状态的文件
        ).all()

    @classmethod
    def create_or_update_storage(
        cls, user_id, fingerprint, file_type, file_key, file_size
    ):
        """创建或更新文件存储记录

        如果文件已存在，则增加引用计数或恢复删除状态
        如果文件不存在，则创建新记录

        Args:
            user_id: 用户ID
            fingerprint: 文件MD5指纹
            file_type: 文件类型
            file_key: 存储系统中的文件键
            file_size: 文件大小

        Returns:
            FileStorage: 创建或更新的文件存储记录
        """
        now = datetime.now()
        # 检查是否已存在相同文件的存储记录，不考虑状态
        file_storage = cls.get_by_user_id_and_fingerprint_any_state(
            user_id, fingerprint
        )

        if file_storage:
            # 已存在存储记录
            if file_storage.state != FileState.ACTIVE.value:
                # 如果文件已被标记为删除或待删除，将其恢复为活跃状态
                # 重置引用计数为1是正确的，因为这表示有一个新的文件引用
                file_storage.state = FileState.ACTIVE.value
                file_storage.reference_count = 1
                file_storage.last_access_time = now
                file_storage.file_key = file_key  # 更新文件键
                file_storage.file_size = file_size  # 更新文件大小
                file_storage.file_type = file_type  # 更新文件类型
                file_storage.updated_time = now
            else:
                # 已存在活跃记录，增加引用计数
                file_storage.reference_count += 1
                file_storage.last_access_time = now
                file_storage.updated_time = now
        else:
            # 创建新的存储记录
            file_storage = cls(
                user_id=user_id,
                fingerprint=fingerprint,
                file_type=file_type,
                file_key=file_key,
                file_size=file_size,
                reference_count=1,
                state=FileState.ACTIVE.value,
                created_time=now,
                updated_time=now,
                last_access_time=now,
            )
            db.session.add(file_storage)

        return file_storage

    @classmethod
    def get_user_storage_usage(cls, user_id):
        """获取用户的存储使用量（字节）

        只统计 state=active 的文件

        Args:
            user_id: 用户ID

        Returns:
            int: 用户存储使用量（字节）
        """
        from sqlalchemy import func

        result = (
            db.session.query(func.sum(cls.file_size))
            .filter(cls.user_id == user_id, cls.state == FileState.ACTIVE.value)
            .scalar()
        )

        return result or 0

    @classmethod
    def _cleanup_active_multipart_uploads(cls, file_storage):
        """清理与文件存储相关的活跃 multipart upload 会话

        当文件被标记为待删除时，需要取消所有相关的活跃上传会话

        Args:
            file_storage: FileStorage 实例
        """
        from models.multipart_upload import MultipartUpload, MultipartUploadStatus
        from flask import current_app
        import logging

        logger = logging.getLogger(__name__)

        # 查找与此文件相关的活跃 multipart upload 会话
        active_uploads = MultipartUpload.query.filter_by(
            user_id=file_storage.user_id,
            fingerprint=file_storage.fingerprint,
            status=MultipartUploadStatus.ACTIVE.value,
        ).all()

        if not active_uploads:
            return

        logger.info(
            f"Found {len(active_uploads)} active multipart uploads for file {file_storage.fingerprint}, cleaning up..."
        )

        storage = current_app.storage

        for upload in active_uploads:
            try:
                # 取消云存储中的分块上传
                storage.abort_multipart_upload(upload.file_key, upload.upload_id)
                logger.info(f"Aborted multipart upload {upload.upload_id}")

            except Exception as e:
                logger.error(
                    f"Failed to abort multipart upload {upload.upload_id}: {e}"
                )
            finally:
                # 无论云存储操作成功还是失败，都要更新数据库状态
                upload.mark_aborted()
