from . import db
from sqlalchemy import func
from enum import Enum


class PlanType(str, Enum):
    SUBSCRIPTION = "subscription"
    ONE_TIME = "one_time"
    FREE = "free"
    LTD = "ltd"


class Plan(db.Model):
    __tablename__ = "plan"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("stripe_price_id"),
        {"comment": "套餐信息表"},
    )

    id = db.Column(
        db.BigInteger, primary_key=True, autoincrement=True, comment="套餐 ID"
    )
    name = db.Column(db.String(50), nullable=False, comment="套餐名称")
    tier = db.Column(db.String(20), nullable=False, comment="套餐等级, basic / pro ")
    plan_type = db.Column(
        db.Enum(
            "subscription",
            "one_time",
            "free",
            "ltd",
            name="plan_type",
        ),
        nullable=False,
        comment="套餐类型: subscription/one_time/free",
    )
    credit_amount = db.Column(db.Integer, nullable=False, comment="包含积分数量")
    interval = db.Column(
        db.String(20),
        nullable=True,  # 改为可空
        comment="计费周期: month/year（仅订阅套餐）",
    )
    stripe_price_id = db.Column(
        db.String(255), unique=True, nullable=False, comment="Stripe价格ID"
    )
    validity_days = db.Column(
        db.Integer, nullable=True, comment="有效天数（仅一次性套餐）"
    )
    max_quantity = db.Column(db.Integer, default=5, comment="单次最大购买数量")
    currency = db.Column(db.String(3), nullable=False, comment="货币代码")
    description = db.Column(db.Text, nullable=True, comment="套餐描述")
    is_active = db.Column(db.Boolean, nullable=False, default=True, comment="是否激活")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    amount = db.Column(
        db.Integer, nullable=False, comment="价格（单位：分）"
    )  # deprecated
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    def __repr__(self):
        return f"<Plan {self.id}>"

    def to_dict(self):
        return {c.name: getattr(self, c.name) for c in self.__table__.columns}

    @classmethod
    def get_by_id(cls, id):
        return cls.query.get(id)

    @classmethod
    def get_by_stripe_price_id(cls, stripe_price_id):
        return cls.query.filter_by(stripe_price_id=stripe_price_id).first()

    @classmethod
    def get_active_plans(cls, plan_type=None):
        query = cls.query.filter_by(is_active=True)
        if plan_type:
            query = query.filter_by(plan_type=plan_type)
        return query.all()

    def validate(self):
        if self.plan_type == "subscription":
            if not self.interval:
                raise ValueError("订阅套餐必须设置计费周期")
        elif self.plan_type == "one_time":
            if self.interval:
                raise ValueError("一次性套餐不应设置计费周期")
