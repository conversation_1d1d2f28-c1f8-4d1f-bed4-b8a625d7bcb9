from sqlalchemy import func
from datetime import datetime, timedelta

from . import db


class Usage(db.Model):
    __tablename__ = "usage"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("user_id"),
        db.Index("idx_createdTime", "created_time"),
        {"comment": "使用量表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=False)
    transcription_minutes_used = db.Column(
        db.Integer, nullable=False, default=0, comment="已使用转写分钟数"
    )
    transcription_minutes_quota = db.Column(
        db.Integer, nullable=False, default=120, comment="转写分钟数配额"
    )
    last_reset_time = db.Column(db.TIMESTAMP, nullable=True, comment="最后重置时间")
    next_reset_time = db.Column(db.TIMESTAMP, nullable=True, comment="下次重置时间")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    @classmethod
    def get_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id).first()

    def should_reset(self):
        """检查是否应该重置使用量"""
        if not self.next_reset_time:
            return True
        return datetime.now() >= self.next_reset_time
