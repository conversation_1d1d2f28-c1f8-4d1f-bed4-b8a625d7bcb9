from datetime import datetime
from sqlalchemy import func

from . import db


class Share(db.Model):
    __tablename__ = "shares"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.Index("idx_userId", "user_id"),
        db.UniqueConstraint("share_code", name="uniq_shareCode"),
        db.UniqueConstraint("file_id", name="uniq_fileId"),
    )
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    file_id = db.Column(db.BigInteger, nullable=False)
    user_id = db.Column(db.BigInteger, nullable=False)
    share_code = db.Column(
        db.String(36), unique=True, nullable=False, comment="分享码(UUID格式)"
    )
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    def __repr__(self):
        return f"<Share {self.id}>"

    @classmethod
    def get_by_share_code(cls, share_code):
        return cls.query.filter_by(share_code=share_code).first()

    @classmethod
    def get_by_file_id(cls, file_id):
        return cls.query.filter_by(file_id=file_id).first()
