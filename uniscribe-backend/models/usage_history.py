from sqlalchemy import func

from . import db


class UsageHistory(db.Model):
    __tablename__ = "usage_history"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.Index("idx_userId", "user_id"),
        db.Index("idx_createdTime", "created_time"),
        {"comment": "使用量历史记录表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=False, comment="用户ID")
    transcription_minutes_used = db.Column(
        db.Integer, nullable=False, comment="重置前已使用转写分钟数"
    )
    transcription_minutes_quota = db.Column(
        db.Integer, nullable=False, comment="重置前转写分钟数配额"
    )
    reset_time = db.Column(db.TIMESTAMP, nullable=False, comment="重置时间")
    reset_reason = db.Column(db.String(255), nullable=True, comment="重置原因")

    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    @classmethod
    def create_history(cls, usage, reason=None):
        history = cls(
            user_id=usage.user_id,
            transcription_minutes_used=usage.transcription_minutes_used,
            transcription_minutes_quota=usage.transcription_minutes_quota,
            reset_time=func.now(),
            reset_reason=reason,
        )
        db.session.add(history)
        return history
