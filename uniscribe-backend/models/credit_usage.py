# models/credit_usage.py
from sqlalchemy import func
from . import db


class CreditUsage(db.Model):
    __tablename__ = "credit_usages"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.Index("idx_user_service", "user_id", "service_type"),
        {"comment": "积分消费明细表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=False, comment="用户ID")
    entitlement_id = db.Column(db.BigInteger, nullable=False, comment="权益ID")
    service_type = db.Column(db.String(20), nullable=False, comment="服务类型")
    credits_used = db.Column(db.Integer, nullable=False, comment="消耗积分")
    usage_time = db.Column(db.TIMESTAMP, server_default=func.now(), comment="使用时间")
    related_file_id = db.Column(db.BigInteger, comment="关联文件ID")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )

    @classmethod
    def get_user_usage(cls, user_id, days=30):
        return (
            cls.query.filter(
                cls.user_id == user_id,
                cls.usage_time >= func.date_sub(func.now(), days=days),
            )
            .order_by(cls.usage_time.desc())
            .all()
        )
