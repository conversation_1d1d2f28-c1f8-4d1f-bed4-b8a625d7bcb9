from datetime import datetime
from enum import Enum

from models import db


class MultipartUploadStatus(Enum):
    """分块上传状态枚举"""

    ACTIVE = "active"  # 上传进行中
    COMPLETED = "completed"  # 上传已完成
    ABORTED = "aborted"  # 上传已取消


class MultipartUpload(db.Model):
    """
    分块上传会话模型，用于跟踪大文件的分块上传进度
    """

    __tablename__ = "multipart_uploads"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("upload_id", name="uk_upload_id"),
        db.Index("idx_user_id", "user_id"),
        db.Index("idx_transcription_file_id", "transcription_file_id"),
        db.Index("idx_status", "status"),
        db.Index("idx_created_time", "created_time"),
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    upload_id = db.Column(
        db.String(512), nullable=False, unique=True, comment="S3分块上传会话ID"
    )
    transcription_file_id = db.Column(
        db.BigInteger, nullable=False, comment="关联的转录文件ID"
    )
    user_id = db.Column(db.BigInteger, nullable=False, comment="用户ID")
    file_key = db.Column(db.String(255), nullable=False, comment="存储系统中的文件键")
    filename = db.Column(db.String(100), nullable=False, comment="原始文件名")
    file_type = db.Column(db.String(20), nullable=False, comment="文件类型")
    file_size = db.Column(db.BigInteger, nullable=False, comment="文件总大小")
    fingerprint = db.Column(db.String(32), nullable=False, comment="文件MD5指纹")
    status = db.Column(
        db.Enum(
            *[e.value for e in MultipartUploadStatus], name="multipart_upload_status"
        ),
        nullable=False,
        default=MultipartUploadStatus.ACTIVE.value,
        comment="上传状态",
    )
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, default=datetime.now, comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )
    completed_time = db.Column(db.TIMESTAMP, nullable=True, comment="完成时间")

    def __repr__(self):
        return f"<MultipartUpload {self.id} upload_id={self.upload_id} status={self.status}>"

    @classmethod
    def get_by_upload_id(cls, upload_id):
        """根据分块上传会话ID获取分块上传记录"""
        return cls.query.filter_by(upload_id=upload_id).first()

    @classmethod
    def get_active_by_user_and_fingerprint(cls, user_id, fingerprint):
        """获取用户指定文件指纹的活跃分块上传记录（用于断点续传）"""
        return cls.query.filter_by(
            user_id=user_id,
            fingerprint=fingerprint,
            status=MultipartUploadStatus.ACTIVE.value,
        ).first()

    def mark_completed(self):
        """标记上传为已完成"""
        self.status = MultipartUploadStatus.COMPLETED.value
        self.completed_time = datetime.now()
        self.updated_time = datetime.now()

    def mark_aborted(self):
        """标记上传为已取消"""
        self.status = MultipartUploadStatus.ABORTED.value
        self.updated_time = datetime.now()
