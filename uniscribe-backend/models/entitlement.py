# models/entitlement.py
from sqlalchemy import func
from enum import Enum
from . import db


class EntitlementSource(str, Enum):
    SUBSCRIPTION = "subscription"
    ONE_TIME = "one_time"
    FREE_PLAN = "free_plan"
    BONUS = "bonus"
    REFERRAL = "referral"
    LTD = "ltd"


class Entitlement(db.Model):
    __tablename__ = "entitlements"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.Index("idx_user_valid", "user_id", "valid_until"),
        db.Index("idx_source", "source_type", "source_id", "is_recurring"),
        {"comment": "用户权益表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=False, comment="用户ID")
    total_credits = db.Column(db.Integer, nullable=False, comment="总积分")
    consumed_credits = db.Column(db.Integer, default=0, comment="已用积分")
    valid_from = db.Column(db.TIMESTAMP, server_default=func.now(), comment="生效时间")
    valid_until = db.Column(db.TIMESTAMP, nullable=False, comment="过期时间")
    source_type = db.Column(
        db.Enum(
            "subscription",
            "one_time",
            "free_plan",
            "bonus",
            "referral",
            "ltd",
            name="entitlement_source",
        ),
        nullable=False,
        comment="来源类型",
    )
    source_id = db.Column(db.BigInteger, nullable=False, comment="来源ID")
    is_recurring = db.Column(db.Boolean, default=False, comment="是否自动续期")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="更新时间"
    )

    @classmethod
    def get_active_entitlements(cls, user_id):
        return (
            cls.query.filter(
                cls.user_id == user_id,
                cls.valid_until > func.now(),
            )
            .order_by(cls.valid_until.asc())
            .all()
        )

    @classmethod
    def get_next_reset_time(cls, user_id):
        return (
            cls.query.filter(
                cls.user_id == user_id,
                cls.valid_until > func.now(),
                cls.is_recurring == True,
            )
            .order_by(cls.valid_until.asc())
            .first()
        )
