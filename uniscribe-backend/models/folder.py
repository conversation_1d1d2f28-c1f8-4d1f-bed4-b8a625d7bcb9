from datetime import datetime
from sqlalchemy import func
from models import db


class Folder(db.Model):
    __tablename__ = "folders"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.Index("idx_user_id", "user_id"),
        db.UniqueConstraint("user_id", "name", name="uix_user_folder_name"),
        {"comment": "用户文件夹表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment="文件夹ID")
    user_id = db.Column(db.BigInteger, nullable=False, comment="用户ID")
    name = db.Column(db.String(40), nullable=False, comment="文件夹名称，最大40个Unicode字符")
    is_default = db.Column(
        db.<PERSON>, nullable=False, default=False, comment="是否为默认文件夹"
    )
    is_deleted = db.Column(db.<PERSON>, nullable=False, default=False, comment="是否已删除")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    def __repr__(self):
        return f"<Folder {self.id}: {self.name}>"

    def to_dict(self):
        """将文件夹对象转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "isDefault": self.is_default,
            "createdTime": self.created_time.isoformat() if self.created_time else None,
            "updatedTime": self.updated_time.isoformat() if self.updated_time else None,
        }

    @classmethod
    def get_by_id(cls, folder_id):
        """根据ID获取文件夹"""
        return cls.query.filter_by(id=folder_id, is_deleted=False).first()

    @classmethod
    def get_by_user_id(cls, user_id):
        """获取用户的所有文件夹（不包括默认文件夹）"""
        return cls.query.filter_by(
            user_id=user_id, is_deleted=False, is_default=False
        ).order_by(cls.created_time.asc()).all()

    @classmethod
    def get_default_folder(cls, user_id):
        """获取用户的默认文件夹"""
        return cls.query.filter_by(
            user_id=user_id, is_default=True, is_deleted=False
        ).first()

    @classmethod
    def get_by_user_and_name(cls, user_id, name):
        """根据用户ID和文件夹名称获取文件夹"""
        return cls.query.filter_by(
            user_id=user_id, name=name, is_deleted=False
        ).first()

    # 移除默认文件夹相关方法，不再需要

    def get_transcription_count(self):
        """获取文件夹中的转录记录数量"""
        from models.transcription_file import TranscriptionFile

        return TranscriptionFile.query.filter(
            TranscriptionFile.user_id == self.user_id,
            TranscriptionFile.folder_id == self.id,
            TranscriptionFile.is_deleted == False,
        ).count()
