from datetime import datetime
from sqlalchemy.dialects.mysql import LONGTEXT
from models import db


class TaskResult(db.Model):
    __tablename__ = "task_result"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("file_id", name="uniq_fileId"),
    )
    id = db.Column(db.BigInteger, autoincrement=True, comment="明细ID")
    file_id = db.Column(db.BigInteger, nullable=False, comment="转录ID")
    duration = db.Column(db.Float, nullable=False, comment="文件时长")
    language = db.Column(db.String(20), nullable=False, comment="语言")
    detected_language = db.Column(db.String(20), nullable=True, comment="检测语言")
    original_text = db.Column(LONGTEXT, nullable=False, comment="原始转录文本")
    segments = db.Column(db.JSO<PERSON>, nullable=False, comment="分段")
    corrected_text = db.Column(LONGTEXT, nullable=True, comment="校正后文本")
    summary = db.Column(db.Text, nullable=True, comment="摘要")
    keywords = db.Column(db.JSON, nullable=True, comment="关键词")
    outline = db.Column(db.Text, nullable=True, comment="大纲(markdown格式)")
    qa_extraction = db.Column(db.JSON, nullable=True, comment="问答提取")
    original_segments = db.Column(
        db.JSON, nullable=True, comment="原始转录结果(用于调试)"
    )
    diarization_segments = db.Column(db.JSON, nullable=True, comment="说话人识别分段")
    is_aligned = db.Column(
        db.Boolean, nullable=False, default=False, comment="是否已完成对齐处理"
    )
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, default=datetime.now, comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )

    def __repr__(self):
        return f"<TranscriptionResult {self.id}>"

    @classmethod
    def get_by_file_id(cls, file_id, refresh=False):
        """
        根据 file_id 查询 task_result

        Args:
            file_id: 文件ID
            refresh: 是否强制从数据库刷新，避免缓存问题
        """
        if refresh:
            # 使用 populate_existing() 强制从数据库重新加载数据，覆盖缓存
            result = cls.query.filter_by(file_id=file_id).populate_existing().first()
        else:
            result = cls.query.filter_by(file_id=file_id).first()

        return result
