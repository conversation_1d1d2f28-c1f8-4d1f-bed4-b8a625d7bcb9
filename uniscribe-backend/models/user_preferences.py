from sqlalchemy import func, text
from . import db


class UserPreferences(db.Model):
    __tablename__ = "user_preferences"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("user_id"),
        db.ForeignKeyConstraint(["user_id"], ["user.id"], ondelete="CASCADE"),
        {"comment": "用户偏好设置表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment="ID")
    user_id = db.Column(db.BigInteger, nullable=False, comment="用户ID")
    timezone = db.Column(
        db.String(50), nullable=False, server_default="", comment="用户时区"
    )
    notify_transcription_success = db.Column(
        db.<PERSON>, nullable=False, server_default=text("true"), comment="转录成功通知"
    )
    notify_quota_reset = db.Column(
        db.<PERSON>,
        nullable=False,
        server_default=text("true"),
        comment="转录配额重置通知",
    )
    notify_product_updates = db.Column(
        db.<PERSON>,
        nullable=False,
        server_default=text("true"),
        comment="产品更新和新功能通知",
    )
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    @classmethod
    def get_by_user_id(cls, user_id):
        """根据用户ID获取偏好设置"""
        return cls.query.filter_by(user_id=user_id).first()

    @classmethod
    def create_default(cls, user_id):
        """为用户创建默认偏好设置"""
        preferences = cls(user_id=user_id)
        db.session.add(preferences)
        db.session.flush()  # 使用flush而不是commit，让外部控制事务
        return preferences

    def to_dict(self):
        """将偏好设置对象转换为字典"""
        return {
            c.name: getattr(self, c.name)
            for c in self.__table__.columns
            if c.name not in ["id", "created_time", "updated_time"]
        }
