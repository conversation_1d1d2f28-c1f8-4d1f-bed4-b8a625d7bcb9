from models import db
from datetime import datetime
from sqlalchemy import func

class AppSumoLicense(db.Model):
    __tablename__ = "appsumo_license"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("license_key"),
        {"comment": "AppSumo license 表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=True, comment="用户ID，可能为空，表示尚未激活")
    license_key = db.Column(db.String(255), nullable=False, comment="AppSumo license key")
    prev_license_key = db.Column(db.String(255), nullable=True, comment="之前的 license key（升级/降级时）")
    tier = db.Column(db.Integer, nullable=False, comment="AppSumo 产品等级")
    status = db.Column(db.String(50), nullable=False, comment="状态：active, inactive, deactivated")
    event = db.Column(db.String(50), nullable=False, comment="最后一次事件：purchase, activate, upgrade, downgrade, deactivate")
    event_timestamp = db.Column(db.BigInteger, nullable=False, comment="事件时间戳")
    created_at = db.Column(db.BigInteger, nullable=False, comment="创建时间戳")
    activation_time = db.Column(db.TIMESTAMP, nullable=True, comment="激活时间")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="记录创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="记录更新时间",
    )

    @classmethod
    def get_by_license_key(cls, license_key):
        return cls.query.filter_by(license_key=license_key).first()

    @classmethod
    def get_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id).all()

    @classmethod
    def get_active_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id, status="active").first()
