"""
API Key model for OpenAPI authentication
"""

from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, Integer, BigInteger
from sqlalchemy.orm import relationship

from models import db
from libs.id_generator import id_generator


class APIKey(db.Model):
    __tablename__ = "api_keys"

    id = Column(String(20), primary_key=True, default=lambda: str(id_generator.get_id()))
    user_id = Column(BigInteger, ForeignKey("user.id"), nullable=False)
    name = Column(String(100), nullable=False)  # User-friendly name for the key
    key_hash = Column(String(64), nullable=False, unique=True)  # SHA256 hash of the key
    is_active = Column(Boolean, default=True, nullable=False)
    created_time = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_used_time = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)  # Optional expiration
    
    # Rate limiting fields (optional for future use)
    rate_limit_per_minute = Column(Integer, default=60)  # Requests per minute
    rate_limit_per_day = Column(Integer, default=1000)   # Requests per day
    
    # Relationship
    user = relationship("User")

    def __repr__(self):
        return f"<APIKey {self.name} for user {self.user_id}>"

    @classmethod
    def get_by_id(cls, api_key_id):
        return cls.query.filter_by(id=api_key_id).first()

    @classmethod
    def get_active_keys_for_user(cls, user_id):
        """Get all active API keys for a user"""
        return cls.query.filter_by(
            user_id=user_id,
            is_active=True
        ).order_by(cls.created_time.desc()).all()

    def update_last_used(self):
        """Update the last used timestamp"""
        self.last_used_time = datetime.utcnow()
        db.session.commit()

    def deactivate(self):
        """Deactivate the API key"""
        self.is_active = False
        db.session.commit()

    def is_expired(self):
        """Check if the API key has expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    @staticmethod
    def generate_key_preview(api_key):
        """Generate masked preview of API key

        Args:
            api_key (str): Full API key (e.g., "us_1234567890abcdef...")

        Returns:
            str: Masked key preview (e.g., "us_****************************abcd")
        """
        if not api_key or len(api_key) < 7:
            return "us_****************************"

        # Show first 3 characters (us_) and last 4 characters
        prefix = api_key[:3]
        suffix = api_key[-4:]
        stars = "*" * (len(api_key) - 7)  # Total length minus prefix and suffix

        return f"{prefix}{stars}{suffix}"

    def to_dict(self, include_full_key=False, full_api_key=None):
        """Convert to dictionary for API responses

        Args:
            include_full_key (bool): Whether to include the full API key (only for creation/reset)
            full_api_key (str): The full API key (only available during creation/reset)

        Returns:
            dict: API key data with camelCase field names
        """
        result = {
            "id": self.id,
            "name": self.name,
            "isActive": self.is_active,
            "createdTime": self.created_time.isoformat(),
            "lastUsedTime": self.last_used_time.isoformat() if self.last_used_time else None,
            "expiresAt": self.expires_at.isoformat() if self.expires_at else None,
            "rateLimitPerMinute": self.rate_limit_per_minute,
            "rateLimitPerDay": self.rate_limit_per_day,
        }

        # Add key preview (always available if we have the full key)
        if full_api_key:
            result["keyPreview"] = self.generate_key_preview(full_api_key)

        # Add full key only during creation/reset
        if include_full_key and full_api_key:
            result["apiKey"] = full_api_key

        return result


class WebhookLog(db.Model):
    __tablename__ = "webhook_logs"

    id = Column(String(20), primary_key=True, default=lambda: str(id_generator.get_id()))
    transcription_id = Column(String(20), nullable=False)
    webhook_url = Column(String(500), nullable=False)
    event_type = Column(String(50), nullable=False)
    status_code = Column(Integer, nullable=True)
    response_body = Column(db.Text, nullable=True)
    error_message = Column(db.Text, nullable=True)
    created_time = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<WebhookLog {self.event_type} for transcription {self.transcription_id}>"

    @classmethod
    def create(cls, transcription_id, webhook_url, event_type, status_code=None, response_body=None, error_message=None):
        """Create a new webhook log entry"""
        log = cls(
            transcription_id=transcription_id,
            webhook_url=webhook_url,
            event_type=event_type,
            status_code=status_code,
            response_body=response_body,
            error_message=error_message
        )
        db.session.add(log)
        db.session.commit()
        return log
