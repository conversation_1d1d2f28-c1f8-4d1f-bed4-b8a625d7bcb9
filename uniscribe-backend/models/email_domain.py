from datetime import datetime
from sqlalchemy import func
from . import db


class EmailDomain(db.Model):
    __tablename__ = "email_domain"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("domain"),
        {"comment": "邮箱域名验证表"},
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    domain = db.Column(db.String(255), nullable=False, comment="域名")
    is_disposable = db.Column(db.<PERSON>, nullable=False, comment="是否为一次性邮箱")
    has_mx = db.Column(
        db.<PERSON>, nullable=False, default=False, comment="是否有 MX 记录"
    )
    is_public_domain = db.Column(
        db.Bo<PERSON>, nullable=False, default=False, comment="是否为公共域名"
    )
    relay_domain = db.Column(
        db.<PERSON>, nullable=False, default=False, comment="是否为中继域名"
    )
    spam = db.Column(
        db.Bo<PERSON>, nullable=False, default=False, comment="是否为垃圾邮件域名"
    )
    last_check_time = db.Column(db.TIMESTAMP, nullable=False, comment="最后检查时间")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )

    @classmethod
    def get_by_domain(cls, domain: str):
        return cls.query.filter_by(domain=domain).first()
