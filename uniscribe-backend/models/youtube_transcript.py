from datetime import datetime
from models import db
from sqlalchemy.dialects.mysql import LONGTEXT


class YoutubeTranscript(db.Model):
    __tablename__ = "youtube_transcripts"

    id = db.Column(db.Integer, primary_key=True)
    video_id = db.Column(db.String(32), nullable=False, index=True)
    lang_code = db.Column(db.String(10), nullable=False)
    lang_name = db.Column(db.String(50), nullable=False)  # 添加语言名称字段
    is_auto = db.Column(
        db.<PERSON><PERSON>, nullable=False, default=False
    )  # 添加是否为自动生成字幕的标识
    vtt_content = db.Column(LONGTEXT, nullable=False)  # 使用 LONGTEXT
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    updated_at = db.Column(
        db.DateTime, nullable=False, default=datetime.now, onupdate=datetime.now
    )

    __table_args__ = (
        db.UniqueConstraint("video_id", "lang_code", name="uix_video_lang"),
    )

    @staticmethod
    def extract_video_id(url: str) -> str:
        """从 YouTube URL 中提取视频 ID"""
        import re

        # 匹配常规 watch 链接
        match = re.search(r"[?&]v=([a-zA-Z0-9_-]+)", url)
        if match:
            return match.group(1)

        # 匹配 youtu.be 短链接
        match = re.search(r"youtu\.be/([a-zA-Z0-9_-]+)", url)
        if match:
            return match.group(1)

        # 匹配 shorts 链接
        match = re.search(r"shorts/([a-zA-Z0-9_-]+)", url)
        if match:
            return match.group(1)

        return None
