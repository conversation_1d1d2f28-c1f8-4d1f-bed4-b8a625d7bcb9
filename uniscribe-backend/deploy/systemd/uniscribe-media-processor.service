[Unit]
Description=Uniscribe Media Preprocessing Consumer
Requires=network.target

[Service]
Type=simple
User=chendahui
Group=chendahui
WorkingDirectory=/home/<USER>/uniscribe-mono/uniscribe-backend

# 环境变量
Environment=PYTHONPATH=/home/<USER>/uniscribe-mono/uniscribe-backend
EnvironmentFile=/home/<USER>/uniscribe-mono/uniscribe-backend/.env

# 主进程 - 支持并发配置
# 默认使用 3 个工作线程，可以通过修改 --workers 参数调整
ExecStart=/home/<USER>/uniscribe-mono/uniscribe-backend/.venv/bin/python workers/media_preprocessing_consumer.py --workers 3 --consumer-name media-processor-1

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3
# 日志配置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=uniscribe-media-processor

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/uniscribe-mono/uniscribe-backend/logs
ReadWritePaths=/tmp

# 进程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
