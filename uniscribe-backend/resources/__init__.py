import inspect
from flask_restful import Api
import logging
from exceptions import BaseAPIException
import exceptions

logger = logging.getLogger(__name__)


def handle_error(err):
    if isinstance(err, BaseAPIException):  # 处理所有继承自 BaseAPIException 的异常
        logger.info(
            f"API Exception ({type(err).__name__}): {err.description}",
            extra={"error_code": err.error_code},
        )
        return err.get_response()

    # 处理其他异常
    logger.error(f"Unexpected error: {str(err)}", exc_info=True)
    return {"message": "Internal Server Error"}, 500


api = Api(catch_all_404s=True)


def register_error_handlers():
    """注册所有错误处理器"""
    # 注册通用错误处理器
    api.errors = {
        "Exception": handle_error,
    }

    # 自动注册所有 BaseAPIException 的子类
    for name, obj in inspect.getmembers(exceptions):
        if (
            inspect.isclass(obj)
            and issubclass(obj, BaseAPIException)
            and obj != BaseAPIException
        ):
            logger.debug(f"register {obj.__name__} error handler")
            api.errors[obj.__name__] = handle_error
