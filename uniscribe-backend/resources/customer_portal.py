from flask_restful import Resource
from flask import g
from resources.auth import auth_required
from controllers.stripe import StripeController


class CustomerPortalResource(Resource):
    @auth_required
    def post(self):
        stripe_portal_session = StripeController.create_portal_session(g.user)
        if stripe_portal_session:
            return {"url": stripe_portal_session.url}
        else:
            return {"url": None}
