from datetime import datetime
from flask import g
from flask_restful import Resource, marshal_with, abort, marshal

from constants.common import DEFAULT_LIMIT, MAX_LIMIT
from constants.transcription import TranscriptionFileStatus, DELETABLE_FILE_STATUSES
from constants.transcription import OriginalFileDeleteReason
from controllers.transcription import (
    format_transcription_file,
    list_transcription_files,
    get_transcription_file_with_result,
    migrate_anonymous_transcription_file,
    rename_transcription_file,
    update_language_code,
    list_transcription_files_by_page,
    update_transcription_type,
    unlock_transcription,
    update_segment_text,
    update_speaker_diarization_setting,
    update_segments_batch,
    update_speakers_batch,
)
from fields.transcription import (
    transcription_file_fields,
    pagination_fields,
    page_based_pagination_fields,
)
from libs import reqparse
from libs.id_generator import id_generator
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from models import db
from resources.auth import auth_required


class TranscriptionsResource(Resource):
    @auth_required
    @marshal_with(pagination_fields)
    def get(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        cursor = parser.get_argument(
            "cursor", type=int, location="args", default=id_generator.get_id()
        )
        limit = parser.get_argument(
            "limit", type=int, location="args", default=DEFAULT_LIMIT
        )

        # 限制 limit 的范围
        limit = min(max(1, limit), MAX_LIMIT)

        return list_transcription_files(user_id, cursor, limit)


class TranscriptionResource(Resource):
    @auth_required
    @marshal_with(transcription_file_fields)
    def get(self, transcription_file_id):
        user_id = g.user.id
        transcription_file_with_result = get_transcription_file_with_result(
            user_id, transcription_file_id
        )

        return transcription_file_with_result

    @auth_required
    @marshal_with(transcription_file_fields)
    def patch(self, transcription_file_id):
        user_id = g.user.id

        # Parse the request body
        parser = reqparse.RequestParser()
        parser.add_argument(
            "filename", type=str, required=False, help="New filename is required"
        )
        parser.add_argument(
            "languageCode",
            type=str,
            required=False,
            help="New language code is required",
        )
        parser.add_argument(
            "transcriptionType",
            type=str,
            required=False,
            help="New transcription type is required",
        )
        parser.add_argument(
            "enableSpeakerDiarization",
            type=bool,
            required=False,
            help="Enable or disable speaker diarization",
        )
        args = parser.parse_args()

        tf = None
        activity_recorded = False

        if args.languageCode:
            tf = update_language_code(user_id, transcription_file_id, args.languageCode)
        if args.filename:
            tf = rename_transcription_file(
                user_id, transcription_file_id, args.filename
            )

        if args.transcriptionType:
            tf = update_transcription_type(
                user_id, transcription_file_id, args.transcriptionType
            )
        if args.enableSpeakerDiarization is not None:
            tf = update_speaker_diarization_setting(
                user_id, transcription_file_id, args.enableSpeakerDiarization
            )
        return tf


class TranscriptionStatusResource(Resource):
    @auth_required
    def get(self, transcription_file_id):
        user_id = g.user.id
        transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
        if not transcription_file:
            abort(404, message="Transcription file not found")
        return {"status": TranscriptionFileStatus.by_id(transcription_file.status).name}


class DeleteTranscriptionResource(Resource):

    allow_deleted_status = DELETABLE_FILE_STATUSES

    @auth_required
    def delete(self, transcription_file_id):
        user_id = g.user.id

        # 获取文件并验证所有权
        file = TranscriptionFile.get_by_id(transcription_file_id)
        if not file:
            abort(404, message="File not found")

        if file.user_id != user_id:
            abort(403, message="Unauthorized")

        # 检查文件状态是否允许删除
        if file.status not in self.allow_deleted_status:
            abort(403, message="File status is not allowed to delete")

        # 减少文件存储引用计数（只有在原文件未被单独删除时才减少）
        if file.fingerprint and not file.original_file_deleted:
            FileStorage.decrement_reference(file.user_id, file.fingerprint)

        # 软删除文件
        file.is_deleted = True
        db.session.commit()

        return {"message": "File deleted successfully"}, 200


class DeleteOriginalFileResource(Resource):
    """Delete original file while preserving transcription record"""

    @auth_required
    def delete(self, transcription_file_id):
        user_id = g.user.id

        # Get file and verify ownership
        file = TranscriptionFile.get_by_id(transcription_file_id)
        if not file:
            abort(404, message="File not found")

        if file.user_id != user_id:
            abort(403, message="Unauthorized")

        # Check if original file is already deleted
        if file.original_file_deleted:
            abort(400, message="Original file already deleted")

        # Only allow deleting original file for completed transcriptions
        allowed_statuses = [
            TranscriptionFileStatus.completed.id,
            TranscriptionFileStatus.completed_with_errors.id,
            TranscriptionFileStatus.partially_completed.id
        ]
        if file.status not in allowed_statuses:
            abort(403, message="Cannot delete original file: transcription not completed")

        # Decrement file storage reference count
        if file.fingerprint:
            FileStorage.decrement_reference(file.user_id, file.fingerprint)

        # Mark original file as deleted but preserve transcription record
        file.original_file_deleted = True
        file.original_file_delete_reason = OriginalFileDeleteReason.USER_MANUAL.value
        file.original_file_deleted_at = datetime.now()
        # Clear file-related fields but keep transcription results
        file.file_url = ""
        file.file_key = ""

        db.session.commit()

        return {"message": "Original file deleted successfully, transcription preserved"}, 200


class BatchDeleteTranscriptionResource(Resource):
    """批量删除转录文件资源"""

    allow_deleted_status = DELETABLE_FILE_STATUSES

    @auth_required
    def delete(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        transcription_ids = parser.get_argument(
            "transcriptionIds", type=list, required=True, location="json"
        )

        if not transcription_ids or len(transcription_ids) == 0:
            abort(400, message="transcriptionIds cannot be empty")

        # 限制批量删除的数量，防止一次删除过多转录记录
        max_batch_size = 100
        if len(transcription_ids) > max_batch_size:
            abort(
                400,
                message=f"Cannot delete more than {max_batch_size} transcriptions at once",
            )

        # 验证所有ID都是有效的整数（支持字符串格式的大整数）
        try:
            # 将字符串或数字转换为整数，支持大整数
            converted_ids = []  # 使用新的变量名避免覆盖原数组
            for transcription_id in transcription_ids:  # 循环原始数组
                if isinstance(transcription_id, str):
                    # 字符串格式，直接转换
                    converted_ids.append(int(transcription_id))
                elif isinstance(transcription_id, (int, float)):
                    # 数字格式，转换为整数
                    converted_ids.append(int(transcription_id))
                else:
                    raise ValueError(
                        f"Invalid transcription ID type: {type(transcription_id)}"
                    )

            # 用转换后的ID替换原数组
            transcription_ids = converted_ids
        except (ValueError, TypeError) as e:
            abort(400, message=f"All transcriptionIds must be valid integers: {str(e)}")

        # 统计结果
        result = {
            "total": len(transcription_ids),
            "deleted": 0,
            "failed": 0,
            "errors": [],
        }

        # 批量获取转录记录
        transcriptions = TranscriptionFile.query.filter(
            TranscriptionFile.id.in_(transcription_ids),
            TranscriptionFile.is_deleted == False,
        ).all()

        # 创建转录记录ID到对象的映射
        transcription_map = {
            transcription.id: transcription for transcription in transcriptions
        }

        # 处理每个转录记录
        for transcription_id in transcription_ids:
            try:
                transcription = transcription_map.get(transcription_id)

                if not transcription:
                    result["failed"] += 1
                    result["errors"].append(
                        {
                            "transcriptionId": str(transcription_id),  # 转换为字符串
                            "error": "Transcription not found",
                        }
                    )
                    continue

                # 验证转录记录所有权
                if transcription.user_id != user_id:
                    result["failed"] += 1
                    result["errors"].append(
                        {
                            "transcriptionId": str(transcription_id),  # 转换为字符串
                            "error": "Unauthorized",
                        }
                    )
                    continue

                # 检查转录记录状态是否允许删除
                if transcription.status not in self.allow_deleted_status:
                    result["failed"] += 1
                    result["errors"].append(
                        {
                            "transcriptionId": str(transcription_id),  # 转换为字符串
                            "error": "Transcription status is not allowed to delete",
                        }
                    )
                    continue

                # 减少文件存储引用计数（只有在原文件未被单独删除时才减少）
                if transcription.fingerprint and not transcription.original_file_deleted:
                    FileStorage.decrement_reference(
                        transcription.user_id, transcription.fingerprint
                    )

                # 软删除转录记录
                transcription.is_deleted = True
                result["deleted"] += 1

            except Exception as e:
                result["failed"] += 1
                result["errors"].append(
                    {
                        "transcriptionId": str(transcription_id),  # 转换为字符串
                        "error": str(e),
                    }
                )

        # 提交所有更改
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            abort(500, message=f"Failed to commit batch deletion: {str(e)}")

        return result, 200


class TranscriptionsByPageResource(Resource):
    @auth_required
    @marshal_with(page_based_pagination_fields)
    def get(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        page = parser.get_argument("page", type=int, location="args", default=1)
        page_size = parser.get_argument(
            "pageSize", type=int, location="args", default=DEFAULT_LIMIT
        )
        folder_filter = parser.get_argument(
            "folder", type=str, location="args", default=None
        )

        # 限制每页数量范围
        page_size = min(max(1, page_size), MAX_LIMIT)
        # 确保页码为正数
        page = max(1, page)

        offset = (page - 1) * page_size
        return list_transcription_files_by_page(
            user_id, page, page_size, offset, folder_filter
        )


class TranscriptionSearchResource(Resource):
    @auth_required
    @marshal_with(page_based_pagination_fields)
    def get(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        parser.add_argument(
            "keyword",
            type=str,
            required=True,
            location="args",
            help="Search keyword is required",
        )
        parser.add_argument("page", type=int, location="args", default=1)
        parser.add_argument(
            "pageSize", type=int, location="args", default=DEFAULT_LIMIT
        )
        parser.add_argument(
            "folderId",
            type=str,
            location="args",
            default=None,
            help="Optional folder filter: 'unclassified' for uncategorized files, folder_id for specific folder, or None for all files",
        )
        args = parser.parse_args()

        # 限制每页数量范围
        page_size = min(max(1, args.pageSize), MAX_LIMIT)
        # 确保页码为正数
        page = max(1, args.page)

        # 计算偏移量
        offset = (page - 1) * page_size

        # 构建基础查询条件
        query = TranscriptionFile.query.filter(
            TranscriptionFile.user_id == user_id,
            TranscriptionFile.filename.like(f"%{args.keyword}%"),
            TranscriptionFile.is_deleted == False,
        )

        # 根据文件夹过滤器调整查询条件
        if args.folderId == "unclassified":
            # 只搜索未分类的文件（folder_id 为 NULL）
            query = query.filter(TranscriptionFile.folder_id == None)
        elif args.folderId and args.folderId != "unclassified":
            # 搜索指定文件夹中的文件
            try:
                folder_id = int(args.folderId)
                # 验证文件夹是否存在且属于当前用户
                from models.folder import Folder

                folder = Folder.get_by_id(folder_id)
                if not folder or folder.user_id != user_id:
                    abort(404, message="Folder not found")

                query = query.filter(TranscriptionFile.folder_id == folder_id)
            except ValueError:
                abort(400, message="Invalid folder ID")

        # 获取总数和分页数据
        total = query.count()

        items = (
            query.order_by(TranscriptionFile.created_time.desc())
            .offset(offset)
            .limit(page_size)
            .all()
        )
        items = [format_transcription_file(item) for item in items]
        total_pages = (total + page_size - 1) // page_size

        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
        }


class AnonymousLatestTranscriptionResource(Resource):
    @auth_required
    def get(self):
        user = g.user
        if not user.is_anonymous:
            abort(403, message="This endpoint is only available for anonymous users")

        # 获取该匿名用户最新的一个文件
        latest_file = (
            TranscriptionFile.query.filter(
                TranscriptionFile.user_id == user.id,
                TranscriptionFile.is_deleted == False,
            )
            .order_by(TranscriptionFile.created_time.desc())
            .first()
        )
        if not latest_file:
            # FIXME: 如果用 marshal_with, 会导致这里也被 marshal，而不是返回 None
            return None

        return marshal(
            format_transcription_file(latest_file), transcription_file_fields
        )


# 迁移匿名用户的文件到新的登录用户
class MigrateAnonymousTranscriptionResource(Resource):
    @auth_required
    def post(self):
        user = g.user
        if user.is_anonymous:
            abort(403, message="This endpoint is for regular user")

        parser = reqparse.RequestParser()
        file_id = parser.get_argument(
            "fileId", type=int, required=True, location="json"
        )

        migrate_anonymous_transcription_file(user, file_id)
        return {
            "message": "File migrated and transcription task created successfully",
        }


class UnlockTranscriptionResource(Resource):
    @auth_required
    def post(self, transcription_file_id):
        """解锁因配额不足而未完全处理的转录文件"""
        user_id = g.user.id
        return unlock_transcription(user_id, transcription_file_id)


class TranscriptionSegmentResource(Resource):
    @auth_required
    def patch(self, transcription_file_id, segment_id):
        """更新单个segment的文本内容"""
        user_id = g.user.id
        parser = reqparse.RequestParser()
        text = parser.get_argument("text", type=str, required=True, location="json")

        # 更新segment
        updated_segment = update_segment_text(
            user_id, transcription_file_id, segment_id, text
        )

        # 确保返回的字段名称与前端期望的一致
        return {
            "success": True,
            "segment": {
                "id": str(updated_segment["id"]),
                "start": str(updated_segment["start_time"]),
                "end": str(updated_segment["end_time"]),
                "text": updated_segment["text"],
            },
        }


class TranscriptionSegmentsBatchResource(Resource):
    @auth_required
    def patch(self, transcription_file_id):
        """批量更新segments的文本内容"""
        user_id = g.user.id
        parser = reqparse.RequestParser()
        segments = parser.get_argument(
            "segments", type=list, required=True, location="json"
        )

        # 验证segments数据格式
        if not segments:
            abort(400, message="segments array cannot be empty")

        # 批量更新segments
        result = update_segments_batch(user_id, transcription_file_id, segments)

        # 格式化返回数据，确保字段名称与前端期望的一致
        formatted_segments = []
        for segment in result["segments"]:
            formatted_segments.append(
                {
                    "id": str(segment["id"]),
                    "start": str(segment["start_time"]),
                    "end": str(segment["end_time"]),
                    "text": segment["text"],
                    "speaker": segment.get("speaker", ""),
                }
            )

        return {
            "success": True,
            "updated_count": result["updated_count"],
            "segments": formatted_segments,
        }


class TranscriptionSpeakersBatchResource(Resource):
    @auth_required
    def patch(self, transcription_file_id):
        """批量更新segments的说话人信息"""
        user_id = g.user.id
        parser = reqparse.RequestParser()
        speaker_updates = parser.get_argument(
            "speakers", type=list, required=True, location="json"
        )

        # 验证speaker_updates数据格式
        if not speaker_updates:
            abort(400, message="speakers array cannot be empty")

        # 批量更新说话人信息
        result = update_speakers_batch(user_id, transcription_file_id, speaker_updates)

        # 格式化返回数据，确保字段名称与前端期望的一致
        formatted_segments = []
        for segment in result["segments"]:
            formatted_segment = {
                "id": str(segment["id"]),
                "start": str(segment["start_time"]),
                "end": str(segment["end_time"]),
                "text": segment["text"],
            }
            # 如果有说话人信息，添加到返回数据中
            if "speaker" in segment:
                formatted_segment["speaker"] = segment["speaker"]
            formatted_segments.append(formatted_segment)

        return {
            "success": True,
            "updated_count": result["updated_count"],
            "segments": formatted_segments,
        }
