"""
Task retry related API resources
"""

from flask import g
from flask_restful import Resource, abort
from libs import reqparse
from controllers.task import retry_task
from resources.auth import auth_required
from exceptions.task import (
    TaskNotFoundError,
    TaskPermissionDeniedError,
    TaskStatusNotRetryableError,
    TaskRetryLimitExceededError,
    TaskFileNotFoundError,
    TaskUnsupportedTypeError
)
import logging

logger = logging.getLogger(__name__)


class TaskRetryResource(Resource):
    """Single task retry API"""
    
    @auth_required
    def post(self):
        """Retry a specific task

        Request body:
        {
            "taskId": 12345
        }

        Response:
        {
            "message": "Task retry successful",
            "taskId": 12345
        }
        """
        user_id = g.user.id
        parser = reqparse.RequestParser()
        task_id = parser.get_argument("taskId", type=int, required=True, location="json")

        logger.info(f"User {user_id} requested task retry: {task_id}")

        try:
            # Call retry logic in controller
            success = retry_task(user_id, task_id)

            if success:
                logger.info(f"Task retry successful: task_id={task_id}, user_id={user_id}")
                return {
                    "message": "Task retry successful",
                    "taskId": str(task_id)
                }
            else:
                logger.error(f"Task retry failed: task_id={task_id}, user_id={user_id}")
                abort(500, message="Task retry failed, please try again later")

        except (TaskNotFoundError, TaskPermissionDeniedError, TaskStatusNotRetryableError,
                TaskRetryLimitExceededError, TaskFileNotFoundError, TaskUnsupportedTypeError) as e:
            # Business logic errors - these are already properly formatted exceptions
            logger.warning(f"Task retry business logic error: task_id={task_id}, user_id={user_id}, error={str(e)}")
            # Re-raise the exception to let Flask handle it with proper HTTP status codes
            raise e
        except Exception as e:
            # System errors
            logger.exception(f"Exception occurred during task retry: task_id={task_id}, user_id={user_id}, error={str(e)}")
            abort(500, message="Task retry failed, please try again later")
