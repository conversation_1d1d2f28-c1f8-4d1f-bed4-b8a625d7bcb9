import logging

from flask_restful import Resource, abort
from werkzeug.exceptions import HTTPException
from flask import g

from libs import reqparse
from controllers.export import export_text, export_batch_text
from models.share import Share
from resources.auth import auth_required


logger = logging.getLogger(__name__)


class ExportResource(Resource):
    allowed_file_types = ["pdf", "docx", "txt", "srt", "vtt", "csv"]

    @auth_required
    def post(self):
        parser = reqparse.RequestParser()
        file_type = parser.get_argument(
            "fileType", type=str, required=True, location="json"
        )
        file_id = parser.get_argument(
            "fileId", type=str, required=True, location="json"
        )
        show_speaker_name = parser.get_argument(
            "showSpeakerName", type=bool, required=False, location="json", default=True
        )
        show_timestamps = parser.get_argument(
            "showTimestamps", type=bool, required=False, location="json", default=True
        )
        if file_type not in self.allowed_file_types:
            abort(400, message="Invalid file type")

        # 打印参数
        logger.info(
            f"file_type: {file_type}, file_id: {file_id}, show_speaker_name: {show_speaker_name}, show_timestamps: {show_timestamps}"
        )

        try:
            return export_text(
                file_type,
                file_id,
                show_speaker_name if show_speaker_name is not None else True,
                show_timestamps if show_timestamps is not None else True,
            )
        except NotImplementedError as e:
            abort(400, message=str(e))
        except HTTPException as e:
            raise
        except Exception as e:
            logger.exception("An error occurred during file export")
            abort(500, message="An error occurred during file export")


class ExportShareResource(Resource):
    allowed_file_types = ["pdf", "docx", "txt", "srt", "vtt", "csv"]

    # no auth required
    def post(self):
        parser = reqparse.RequestParser()
        file_type = parser.get_argument(
            "fileType", type=str, required=True, location="json"
        )
        file_id = parser.get_argument(
            "fileId", type=str, required=True, location="json"
        )
        show_speaker_name = parser.get_argument(
            "showSpeakerName", type=bool, required=False, location="json", default=True
        )
        show_timestamps = parser.get_argument(
            "showTimestamps", type=bool, required=False, location="json", default=True
        )
        if file_type not in self.allowed_file_types:
            abort(400, message="Invalid file type")

        # check file shared
        share = Share.get_by_file_id(file_id)
        if not share:
            abort(404, message="File not shared")

        try:
            return export_text(
                file_type,
                file_id,
                show_speaker_name if show_speaker_name is not None else True,
                show_timestamps if show_timestamps is not None else True,
            )
        except NotImplementedError as e:
            abort(400, message=str(e))
        except HTTPException as e:
            raise
        except Exception as e:
            logger.exception("An error occurred during file export")
            abort(500, message="An error occurred during file export")


class BatchExportResource(Resource):
    """批量导出转录文件资源"""

    allowed_file_types = ["pdf", "docx", "txt", "srt", "vtt", "csv"]

    @auth_required
    def post(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        file_type = parser.get_argument(
            "fileType", type=str, required=True, location="json"
        )
        file_ids = parser.get_argument(
            "fileIds", type=list, required=True, location="json"
        )
        show_speaker_name = parser.get_argument(
            "showSpeakerName", type=bool, required=False, location="json", default=True
        )
        show_timestamps = parser.get_argument(
            "showTimestamps", type=bool, required=False, location="json", default=True
        )

        if file_type not in self.allowed_file_types:
            abort(400, message="Invalid file type")

        if not file_ids or len(file_ids) == 0:
            abort(400, message="fileIds cannot be empty")

        # 限制批量导出的数量，防止一次导出过多文件
        max_batch_size = 50
        if len(file_ids) > max_batch_size:
            abort(
                400,
                message=f"Cannot export more than {max_batch_size} files at once",
            )

        # 打印参数
        logger.info(
            f"Batch export request - user_id: {user_id}, file_type: {file_type}, "
            f"file_count: {len(file_ids)}, show_speaker_name: {show_speaker_name}, "
            f"show_timestamps: {show_timestamps}"
        )

        try:
            response = export_batch_text(
                file_type,
                file_ids,
                user_id,
                show_speaker_name if show_speaker_name is not None else True,
                show_timestamps if show_timestamps is not None else True,
            )

            # 记录导出结果统计
            total = response.headers.get("X-Export-Total", "0")
            successful = response.headers.get("X-Export-Successful", "0")
            failed = response.headers.get("X-Export-Failed", "0")

            logger.info(
                f"Batch export completed - user_id: {user_id}, total: {total}, "
                f"successful: {successful}, failed: {failed}"
            )

            # 如果有失败的文件，记录警告
            if response.headers.get("X-Export-Has-Failures") == "true":
                logger.warning(
                    f"Batch export partial failure - user_id: {user_id}, "
                    f"failed_count: {failed}, errors: {response.headers.get('X-Export-Errors', 'N/A')}"
                )

            return response

        except NotImplementedError as e:
            logger.error(
                f"Batch export not implemented - file_type: {file_type}, error: {str(e)}"
            )
            abort(400, message=str(e))
        except HTTPException as e:
            logger.error(
                f"Batch export HTTP error - user_id: {user_id}, error: {str(e)}"
            )
            raise
        except Exception as e:
            logger.exception(f"Batch export unexpected error - user_id: {user_id}")
            abort(500, message="An error occurred during batch file export")
