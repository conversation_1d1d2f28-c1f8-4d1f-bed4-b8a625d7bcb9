from flask import g
from flask_restful import Resource, marshal_with, marshal, abort
from constants.common import DEFAULT_LIMIT, MAX_LIMIT
from controllers.folder import (
    create_folder,
    delete_folder,
    update_folder_name,
    get_user_folders,
    get_folder_transcriptions,
    batch_move_transcriptions_to_folder,
)
from fields.folder import (
    folder_fields,
    folder_list_fields,
)

from libs import reqparse
from resources.auth import auth_required


class FoldersResource(Resource):
    @auth_required
    @marshal_with(folder_list_fields)
    def get(self):
        """获取用户的所有文件夹"""
        user_id = g.user.id
        folders = get_user_folders(user_id)
        return {"folders": folders}

    @auth_required
    @marshal_with(folder_fields)
    def post(self):
        """创建新文件夹"""
        user_id = g.user.id
        parser = reqparse.RequestParser()
        name = parser.get_argument("name", type=str, required=True, location="json")

        folder = create_folder(user_id, name)
        return folder


class FolderResource(Resource):
    @auth_required
    @marshal_with(folder_fields)
    def patch(self, folder_id):
        """修改文件夹名称"""
        user_id = g.user.id
        parser = reqparse.RequestParser()
        new_name = parser.get_argument("name", type=str, required=True, location="json")

        folder = update_folder_name(user_id, folder_id, new_name)
        return folder

    @auth_required
    def delete(self, folder_id):
        """删除文件夹及其中的所有转录记录"""
        user_id = g.user.id
        result = delete_folder(user_id, folder_id)
        return result


class FolderTranscriptionsResource(Resource):
    @auth_required
    def get(self, folder_id):
        """获取文件夹中的转录记录"""
        try:
            user_id = g.user.id
            parser = reqparse.RequestParser()
            page = parser.get_argument("page", type=int, location="args", default=1)
            page_size = parser.get_argument(
                "pageSize", type=int, location="args", default=DEFAULT_LIMIT
            )

            # 限制每页数量范围
            page_size = min(max(1, page_size), MAX_LIMIT)
            # 确保页码为正数
            page = max(1, page)

            offset = (page - 1) * page_size
            result = get_folder_transcriptions(
                user_id, folder_id, page, page_size, offset
            )

            # 使用 marshal 来序列化转录记录
            from fields.transcription import transcription_file_fields

            return {
                "items": [
                    marshal(item, transcription_file_fields) for item in result["items"]
                ],
                "total": result["total"],
                "page": result["page"],
                "pageSize": result["page_size"],
                "totalPages": result["total_pages"],
                "folder": {
                    "id": str(result["folder"].id),
                    "name": result["folder"].name,
                    "isDefault": result["folder"].is_default,
                    "transcriptionCount": result["folder"].transcription_count,
                },
            }
        except Exception as e:
            # 添加错误日志
            import logging

            logger = logging.getLogger(__name__)
            logger.error(
                f"Error in FolderTranscriptionsResource: {str(e)}", exc_info=True
            )
            raise


class BatchTranscriptionFolderResource(Resource):
    """批量移动转录记录到文件夹资源"""

    @auth_required
    def patch(self):
        """批量将转录记录移动到指定文件夹"""
        user_id = g.user.id
        parser = reqparse.RequestParser()
        transcription_ids = parser.get_argument(
            "transcriptionIds", type=list, required=True, location="json"
        )
        folder_id = parser.get_argument("folderId", type=int, location="json")

        if not transcription_ids or len(transcription_ids) == 0:
            abort(400, message="transcriptionIds cannot be empty")

        # 限制批量移动的数量，防止一次移动过多转录记录
        max_batch_size = 100
        if len(transcription_ids) > max_batch_size:
            abort(
                400,
                message=f"Cannot move more than {max_batch_size} transcriptions at once",
            )

        # 执行批量移动操作
        result = batch_move_transcriptions_to_folder(
            user_id, transcription_ids, folder_id
        )

        return result, 200
