"""
重定向服务模块

提供一个简单的重定向服务，用于处理邮件中的外部链接，
确保所有链接都通过我们自己的域名进行重定向，
符合 Resend 的最佳实践建议。
"""

import logging
from urllib.parse import urlparse

from flask import redirect
from flask_restful import Resource

logger = logging.getLogger(__name__)

# URL 映射配置
URL_MAPPINGS = {
    "appsumo": {
        "review": "https://appsumo.com/products/uniscribe/#reviews",
        "default": "https://appsumo.com/products/uniscribe/",
    }
}


class RedirectResource(Resource):
    """
    处理重定向请求的资源类

    用法示例:
    - /go/appsumo/review -> 重定向到 AppSumo 评论页面
    """

    def get(self, target, campaign=None):
        """
        处理重定向请求

        Args:
            target: 目标类型，如 'appsumo' 等
            campaign: 可选的活动标识符，如 'review' 等

        Returns:
            重定向响应
        """
        # 获取目标 URL
        target_url = self._get_target_url(target, campaign)

        # 记录重定向信息
        self._log_redirect(target, campaign, target_url)

        # 重定向到目标 URL
        return redirect(target_url, code=302)

    def _get_target_url(self, target, campaign):
        """
        根据目标类型和活动标识符获取目标 URL

        Args:
            target: 目标类型
            campaign: 活动标识符

        Returns:
            目标 URL
        """
        if target in URL_MAPPINGS:
            if campaign and campaign in URL_MAPPINGS[target]:
                return URL_MAPPINGS[target][campaign]
            return URL_MAPPINGS[target]["default"]

        # 如果没有找到映射，返回默认页面
        return "https://www.uniscribe.co"

    def _log_redirect(self, target, campaign, target_url):
        """
        记录重定向信息

        Args:
            target: 目标类型
            campaign: 活动标识符
            target_url: 目标 URL
        """
        logger.info(
            "Redirect: /go/%s/%s -> %s", target, campaign or "default", target_url
        )
