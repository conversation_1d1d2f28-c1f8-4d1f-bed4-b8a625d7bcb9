"""
队列监控相关的 API 资源
"""

from flask_restful import Resource
from services.task_queue_service import get_queue_stats


class QueueStatsResource(Resource):
    """队列统计和健康检查接口"""

    def get(self):
        """获取队列统计信息和健康状态"""
        try:
            stats = get_queue_stats()

            # 健康检查逻辑
            alerts = []

            # 检查待处理消息积压（这才是真正的积压）
            for queue_type, pending in stats["pending_messages"].items():
                if pending > 50:  # 待处理消息超过50个
                    alerts.append(f"{queue_type} 任务积压严重: {pending} 个待处理任务")

            # 检查未读取消息积压（新增）
            for queue_type, unread in stats["unread_messages"].items():
                if unread > 100:  # 未读取消息超过100个
                    alerts.append(f"{queue_type} 未读取任务过多: {unread} 个等待处理")

            # 检查队列总长度（作为参考，阈值设置更高）
            for queue_type, length in stats["queue_lengths"].items():
                if length > 1000:  # 队列总消息数超过1000个（包括已处理的）
                    alerts.append(
                        f"{queue_type} 队列消息过多: {length} 个消息（建议清理）"
                    )

            status = "healthy" if not alerts else "warning"

            # 添加统计摘要
            summary = {
                "total_queues": len(stats["queue_lengths"]),
                "total_messages": stats["total_queued"],
                "total_pending": stats["total_pending"],
                "total_unread": stats["total_unread"],
                "active_queues": len([q for q, l in stats["queue_lengths"].items() if l > 0]),
                "queues_with_pending": len([q for q, p in stats["pending_messages"].items() if p > 0]),
                "queues_with_unread": len([q for q, u in stats["unread_messages"].items() if u > 0])
            }

            return {
                "status": status,
                "alerts": alerts,
                "summary": summary,
                "stats": stats,
                "message": "队列信息获取成功",
            }
        except Exception as e:
            return {"status": "error", "error": f"获取队列信息失败: {str(e)}"}, 500
