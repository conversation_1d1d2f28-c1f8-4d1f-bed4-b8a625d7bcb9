from functools import wraps
from typing import Optional
import logging

from sqlalchemy.exc import IntegrityError
from flask import request, g
from flask_restful import abort
import jwt

from config import CONFIG
from controllers.user import create_or_update_user
from models.user import User
from services.supabase_service import SupabaseService
from services.activity_service import ActivityService
from exceptions.user import DeactivatedAccountError

INVALID_TOKEN_MESSAGE = "Invalid or expired token"
BEARER_TOKEN_REQUIRED = "Bearer token is required"

logger = logging.getLogger(__name__)


def verify_token(token: str) -> Optional[dict]:
    """Verify JWT token and return payload"""
    try:
        return jwt.decode(
            token,
            CONFIG.SUPABASE["jwt_secret"],
            algorithms=["HS256"],
            options={"verify_exp": True, "verify_aud": True},
            audience="authenticated",
        )
    except (jwt.ExpiredSignatureError, jwt.InvalidTokenError) as e:
        return None


def auth_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return abort(401, message=BEARER_TOKEN_REQUIRED)

        token = auth_header.split(" ")[1]
        payload = verify_token(token)
        if payload:
            g.user = User.get_by_supabase_id(payload["sub"])
            if not g.user:
                supabase_user = SupabaseService.get_user_by_token(token)
                if not supabase_user:
                    abort(401, message=INVALID_TOKEN_MESSAGE)
                try:
                    g.user = create_or_update_user(supabase_user)
                except IntegrityError:
                    g.user = User.get_by_supabase_id(payload["sub"])
                if not g.user:
                    logger.error(
                        "User not found for token: %s, %s" % (token, supabase_user.id)
                    )
                    abort(401, message=INVALID_TOKEN_MESSAGE)

            if g.user.is_deactivated == 1:
                logger.warning(f"Deactivated account access attempt: {g.user.id}")
                raise DeactivatedAccountError(
                    message="Your account has been deleted and associated email cannot be used for registration again"
                )

            # 记录用户活跃行为（统一在认证入口处理）
            try:
                ActivityService.record_user_activity(g.user.id, "api_access")
            except Exception as e:
                # 活跃记录失败不应该影响正常的API调用
                logger.warning(
                    f"Failed to record user activity for user {g.user.id}: {str(e)}"
                )

            return f(*args, **kwargs)

        return abort(401, message=INVALID_TOKEN_MESSAGE)

    return decorated
