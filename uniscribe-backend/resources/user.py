from flask_restful import Resource, marshal_with, reqparse
from flask import g
import logging

from resources.auth import auth_required
from fields.user import user_fields, email_check_fields
from models.user import User
from controllers.email_validator import email_validator
from controllers.user_preferences import update_user_preferences
from controllers.user import update_user


logger = logging.getLogger(__name__)


class UserResource(Resource):
    def __init__(self):
        self.parser = reqparse.RequestParser()
        # 用户基本信息字段
        self.parser.add_argument(
            "firstName", type=str, required=False, location="json", dest="first_name"
        )
        self.parser.add_argument(
            "lastName", type=str, required=False, location="json", dest="last_name"
        )
        # 用户偏好设置字段
        self.parser.add_argument(
            "preferences", type=dict, required=False, location="json"
        )
        super().__init__()

    @auth_required
    @marshal_with(user_fields)
    def get(self):
        """获取当前用户信息，包括详细的计划信息"""
        user = g.user
        user.get_preferences()
        return user

    @auth_required
    @marshal_with(user_fields)
    def put(self):
        """更新用户信息和偏好设置"""
        args = self.parser.parse_args()
        user = g.user

        # 更新用户基本信息
        first_name = args.get("first_name")
        last_name = args.get("last_name")
        if first_name is not None or last_name is not None:
            user = update_user(user, first_name=first_name, last_name=last_name)

        # 更新用户偏好设置
        if args.get("preferences"):
            preferences_data = {}
            prefs = args["preferences"]

            # 转换前端字段名到后端字段名
            if "timezone" in prefs:
                preferences_data["timezone"] = prefs["timezone"]
            if "notifyTranscriptionSuccess" in prefs:
                preferences_data["notify_transcription_success"] = prefs[
                    "notifyTranscriptionSuccess"
                ]
            if "notifyQuotaReset" in prefs:
                preferences_data["notify_quota_reset"] = prefs["notifyQuotaReset"]
            if "notifyProductUpdates" in prefs:
                preferences_data["notify_product_updates"] = prefs[
                    "notifyProductUpdates"
                ]

            # 更新偏好设置
            if preferences_data:
                update_user_preferences(preferences_data)

        # 确保用户偏好设置存在
        user.get_preferences()

        return user


# TODO: add ratelimit
class EmailCheckResource(Resource):
    def __init__(self):
        self.parser = reqparse.RequestParser()
        self.parser.add_argument(
            "email", type=str, required=True, help="Email is required", location="args"
        )
        super().__init__()

    @marshal_with(email_check_fields)
    def get(self):
        """检查邮箱是否已注册、是否被停用及是否为一次性邮箱"""
        args = self.parser.parse_args()
        user = User.get_by_email(args["email"])
        return {
            "email": args["email"],
            "is_deactivated": user.is_deactivated if user else False,
            "is_disposable": not email_validator.validate_email(
                args["email"]
            ),  # 一次性邮箱检查
            "is_registered": user is not None,  # 检查邮箱是否已注册
        }
