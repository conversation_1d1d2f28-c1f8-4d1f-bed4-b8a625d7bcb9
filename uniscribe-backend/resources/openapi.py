"""
OpenAPI resources for external integrations (n8n, Zapier, etc.)
Provides simplified API endpoints for core transcription functionality.
"""

import logging
import os
import time
import urllib.parse

from flask import g, request, current_app
from flask_restful import Resource, marshal

from constants.transcription import TranscriptionFileSourceType
from constants.common import DEFAULT_LIMIT, MAX_LIMIT
from constants.transcription import SUPPORTED_AUDIO_FORMATS
from constants.task import TranscriptionType
from controllers.transcription import (
    list_transcription_files,
    get_transcription_file_with_result,
    create_transcription_file,
)
from controllers.task import create_media_preprocessing_task
from controllers.youtube import YoutubeTranscriber
from fields.openapi import (
    openapi_transcription_fields,
    openapi_transcription_list_fields,
    openapi_status_fields,
)
from libs import reqparse
from libs.id_generator import id_generator
from models.transcription_file import TranscriptionFile
from resources.openapi_auth import openapi_auth_required
from controllers.usage import check_transcription_quota
from utils.api_response import success_response, paginated_response
from exceptions.openapi import (
    InvalidRequestParametersError,
    TranscriptionNotFoundError,
)

logger = logging.getLogger(__name__)

FILE_SIZE_LIMIT_GB = 5
FILE_SIZE_LIMIT = FILE_SIZE_LIMIT_GB * 1024 * 1024 * 1024  # 5GB


class OpenAPITranscriptionListResource(Resource):
    """
    OpenAPI endpoint for listing transcriptions and creating new ones
    GET /api/v1/transcriptions - List transcriptions
    POST /api/v1/transcriptions - Create new transcription from URL
    """

    @openapi_auth_required
    def get(self):
        """List user's transcriptions with pagination"""
        user_id = g.user.id
        parser = reqparse.RequestParser()

        # Cursor-based pagination
        cursor = parser.get_argument(
            "cursor", type=int, location="args", default=id_generator.get_id()
        )
        limit = parser.get_argument(
            "limit", type=int, location="args", default=DEFAULT_LIMIT
        )

        # Limit range
        limit = min(max(1, limit), MAX_LIMIT)

        result = list_transcription_files(user_id, cursor, limit)

        # Format response using OpenAPI fields
        items = [
            marshal(item, openapi_transcription_list_fields) for item in result["items"]
        ]

        return paginated_response(
            items=items,
            has_more=result["hasMore"],
            next_cursor=result.get("nextCursor")
        )

    @openapi_auth_required
    def post(self):
        """Create new transcription from URL"""
        parser = reqparse.RequestParser()

        # Required parameter: file URL
        file_url = parser.get_argument("file_url", type=str, required=True, location="json")
        language_code = parser.get_argument("language_code", required=True, type=str, location="json", default=None)

        # Optional parameters
        filename = parser.get_argument("filename", type=str, location="json")
        webhook_url = parser.get_argument("webhook_url", type=str, location="json")
        transcription_type = parser.get_argument(
            "transcription_type",
            type=str,
            location="json",
            default=TranscriptionType.TRANSCRIPT,
        )
        enable_speaker_diarization = parser.get_argument(
            "enable_speaker_diarization", type=bool, location="json", default=False
        )

        try:
            # Handle URL download
            transcription_file = self._handle_url_download(
                file_url,
                filename,
                webhook_url,
                language_code,
                transcription_type,
                enable_speaker_diarization,
            )
            # 用 openapi_status_fields marshal
            return success_response(marshal(transcription_file, openapi_status_fields))

        except Exception as e:
            logger.error(f"Transcription creation failed: {str(e)}")
            # Re-raise as a generic server error - this will be handled by the exception handler
            raise



    def _is_youtube_url(self, url: str) -> bool:
        """Check if the URL is a YouTube URL"""
        import re

        patterns = [
            # youtube.com/watch?v=VIDEO_ID (支持 www. 和 m. 前缀)
            r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/watch\?(?=.*v=([A-Za-z0-9_-]{11,}))(?:\S+)?)$",
            # youtu.be/VIDEO_ID
            r"^(?:https?://)?(?:www\.)?youtu\.be/([A-Za-z0-9_-]{11,})(?:\?.*)?$",
            # youtube.com/embed/VIDEO_ID (支持 www. 和 m. 前缀)
            r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/embed/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
            # youtube.com/shorts/VIDEO_ID (支持 www. 和 m. 前缀)
            r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/shorts/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
            # youtube.com/live/VIDEO_ID (支持 www. 和 m. 前缀)
            r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/live/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
        ]

        for pattern in patterns:
            match = re.match(pattern, url)
            if match:
                video_id = match.group(1)
                # 验证视频ID格式（至少11位，包含字母数字下划线连字符）
                if video_id and re.match(r"^[A-Za-z0-9_-]{11,}$", video_id):
                    return True
        return False

    def _handle_url_download(
        self,
        file_url,
        filename,
        webhook_url,
        language_code,
        transcription_type,
        enable_speaker_diarization,
    ):
        """Handle file URL download with preprocessing support"""


        # Validate URL
        if not file_url.startswith(('http://', 'https://')):
            raise InvalidRequestParametersError("Invalid URL format. URL must start with http:// or https://")

        # Check if this is a YouTube URL and provide helpful error message
        if self._is_youtube_url(file_url):
            raise InvalidRequestParametersError(
                "YouTube URLs are not supported in this endpoint. "
                "Please use the dedicated YouTube transcription endpoint: POST /api/v1/transcriptions/youtube"
            )

        try:
            # Use provided filename if available, otherwise extract from URL
            if filename:
                # Use provided filename
                final_filename = filename
                # Get file extension from provided filename
                file_ext = os.path.splitext(filename)[1].lower()
                if not file_ext:
                    file_ext = '.unknown'
            else:
                # Parse URL to extract filename
                parsed_url = urllib.parse.urlparse(file_url)
                path = parsed_url.path
                final_filename = os.path.basename(path)

                # If no filename could be extracted, use a generic name
                if not final_filename or final_filename == '':
                    final_filename = f"download_{int(time.time())}"

                # Get file extension from URL path or default to unknown
                file_ext = os.path.splitext(path)[1].lower()
                if not file_ext:
                    file_ext = '.unknown'

            file_type = file_ext[1:] if file_ext.startswith('.') else file_ext

            # Validate file type against supported formats (if not unknown)
            # Note: file_type will be updated after download based on actual file content
            if file_type != 'unknown' and file_type not in SUPPORTED_AUDIO_FORMATS:
                raise InvalidRequestParametersError(
                    f"Unsupported file type: {file_type}. Supported formats: {', '.join(SUPPORTED_AUDIO_FORMATS)}"
                )

            # Create transcription file record with source_type=UPLOAD (for now, since URL is not in enum)
            # We'll use source_url to track the original URL
            transcription_file = create_transcription_file(
                user_id=g.user.id,
                filename=final_filename,
                file_type=file_type,
                file_size=0,  # Will be updated after download
                fingerprint="",  # Will be updated after download
                duration=0,  # Will be updated after download
                source_type=TranscriptionFileSourceType.UPLOAD,  # Use UPLOAD for now
                source_url=file_url,  # Store the URL here
                language_code=language_code,
                transcription_type=transcription_type,
                enable_speaker_diarization=enable_speaker_diarization,
            )

            # Store webhook URL if provided
            if webhook_url:
                transcription_file.webhook_url = webhook_url
                from models import db
                db.session.commit()

            # Create media preprocessing task for URL download
            # Note: We use a dedicated "url_download" type to distinguish from regular ffmpeg preprocessing
            create_media_preprocessing_task(
                user_id=g.user.id,
                transcription_file_id=transcription_file.id,
                preprocessing_type="url_download",  # New type specifically for URL downloads
                preprocessing_reason="remote_url"
            )

            # Return transcription status
            return transcription_file  # 直接返回 TranscriptionFile 实例

        except Exception as e:
            logger.error(f"Error handling URL download: {e}")
            raise InvalidRequestParametersError(f"Failed to process URL: {str(e)}")


class OpenAPITranscriptionResource(Resource):
    """
    OpenAPI endpoint for individual transcription operations
    GET /api/v1/transcriptions/{id} - Get transcription with results
    """

    @openapi_auth_required
    def get(self, transcription_id):
        """Get transcription file with results"""
        user_id = g.user.id
        transcription = get_transcription_file_with_result(user_id, transcription_id)

        response_data = marshal(transcription, openapi_transcription_fields)
        return success_response(response_data)


class OpenAPITranscriptionStatusResource(Resource):
    """
    OpenAPI endpoint for transcription status
    GET /api/v1/transcriptions/{id}/status - Get transcription status
    """

    @openapi_auth_required
    def get(self, transcription_id):
        """Get transcription status"""
        user_id = g.user.id
        transcription_file = TranscriptionFile.get_by_id(transcription_id)

        if not transcription_file:
            raise TranscriptionNotFoundError("Transcription not found")

        if transcription_file.user_id != user_id:
            raise TranscriptionNotFoundError("Transcription not found")  # Don't reveal existence

        # 直接 marshal TranscriptionFile
        return success_response(marshal(transcription_file, openapi_status_fields))

   

class OpenAPIYoutubeTranscriptionResource(Resource):
    """
    OpenAPI endpoint for YouTube transcription
    POST /api/v1/transcriptions/youtube - Create transcription from YouTube URL
    """

    @openapi_auth_required
    def post(self):
        """Create transcription from YouTube URL"""
        user_id = g.user.id
        parser = reqparse.RequestParser()

        # Required parameters
        url = parser.get_argument("url", type=str, required=True, location="json")
        language_code = parser.get_argument("language_code", type=str, required=True, location="json", default=None)
        
        # Optional parameters
        webhook_url = parser.get_argument("webhook_url", type=str, location="json")
        transcription_type = parser.get_argument(
            "transcription_type",
            type=str,
            location="json",
            default=TranscriptionType.TRANSCRIPT,
        )
        enable_speaker_diarization = parser.get_argument(
            "enable_speaker_diarization", type=bool, location="json", default=False
        )

        # Extract video information from URL
        from controllers.youtube import YoutubeInfoExtractor

        try:
            extractor = YoutubeInfoExtractor(url)
            video_info = extractor.extract()

            # Get title and duration from extracted info
            title = video_info.get("title", "Unknown Title")
            duration = video_info.get("duration", 0)

            # Ensure duration is a number (convert from string if necessary)
            try:
                duration = int(float(duration)) if duration else 0
            except (ValueError, TypeError):
                duration = 0

            if not duration or duration <= 0:
                raise InvalidRequestParametersError("Could not determine video duration")

        except Exception as e:
            logger.error(f"Failed to extract YouTube video info: {str(e)}")
            raise InvalidRequestParametersError(f"Failed to extract video information: {str(e)}")

        check_transcription_quota(user_id, duration)
        storage = current_app.storage
        transcriber = YoutubeTranscriber(user_id, url, title, duration, storage)

        transcription_file = transcriber.create_transcription_file(
            transcription_type, language_code, enable_speaker_diarization, None
        )

        # Store webhook URL if provided
        if webhook_url:
            transcription_file.webhook_url = webhook_url
            from models import db
            db.session.commit()

        # Create media preprocessing task for YouTube download (async processing)
        create_media_preprocessing_task(
            user_id=user_id,
            transcription_file_id=transcription_file.id,
            preprocessing_type="youtube",
            preprocessing_reason="youtube_download",
            youtube_url=url,
            title=title
        )

        # Return transcription status instead of processing synchronously
        return success_response(marshal(transcription_file, openapi_status_fields))
