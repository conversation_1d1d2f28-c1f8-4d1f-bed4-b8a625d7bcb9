from flask_restful import Resource, marshal_with
from flask import g
from datetime import datetime
from constants.task import TaskType, TaskStatus
from controllers.task import (
    get_next_task,
    save_task_result,
    update_file_status,
    create_transcription_task,
    create_text_tasks,
)
from libs import reqparse
from models.task import Task
from models.task_result import TaskResult
from models.transcription_file import TranscriptionFile
from models import db
from resources.auth import auth_required
from exceptions.task import TaskNotFoundError, TaskPermissionDeniedError
from fields.task import task_status_fields

# 无论是 提供给前端还是 go 服务，统一用驼峰风格命名字段


# 这个接口提供给前端，用于创建任务
class CreateTranscriptionTaskResource(Resource):
    @auth_required
    def post(self):
        user_id = g.user.id
        parser = reqparse.RequestParser()
        transcription_file_id = parser.get_argument(
            "transcriptionFileId", type=int, required=True, location="json"
        )
        task = create_transcription_task(user_id, transcription_file_id)
        return {"data": {"taskId": task.id, "status": task.status}}


# 这个接口提供给 go 服务，用于创建文本处理任务
class CreateTextTasksResource(Resource):
    def post(self):
        # TODO： API 鉴权
        parser = reqparse.RequestParser()
        transcription_file_id = parser.get_argument(
            "transcriptionFileId", type=int, required=True, location="json"
        )
        tasks = create_text_tasks(transcription_file_id)
        return {"data": [task.id for task in tasks]}


class TaskNextResource(Resource):
    def post(self):
        # Note: 由于不是幂等的，不能用 GET，用 POST 代替
        # TODO：API 鉴权

        # 获取任务类型过滤参数
        parser = reqparse.RequestParser()
        task_type_filter = parser.get_argument(
            "type", type=str, required=False, location="args"
        )

        # 验证参数值
        if task_type_filter and task_type_filter not in [
            "transcription",
            "text",
            "speaker_diarization",
        ]:
            return {
                "error": "Invalid task type. Must be 'transcription', 'text', or 'speaker_diarization'"
            }, 400

        next_task = get_next_task(task_type_filter)
        update_file_status(next_task.file_id)
        task_result = TaskResult.get_by_file_id(next_task.file_id)
        transcription_text = task_result.original_text if task_result else ""
        response = {
            "taskId": next_task.id,
            "fileUrl": next_task.file_url,
            "transcriptionFileId": next_task.file_id,
            "taskType": next_task.task_type,  # 不用转换为字符串
            "transcriptionText": transcription_text,
            "fileDuration": next_task.file_duration,
            "languageCode": next_task.language_code,
            "language": next_task.language,
            "transcriptionType": next_task.transcription_type,
        }

        # 如果有请求的服务提供商，添加到响应中
        if next_task.requested_service_provider:
            response["requestedServiceProvider"] = next_task.requested_service_provider

        return response


class TaskResultResource(Resource):
    def post(self):
        # TODO：API 鉴权
        parser = reqparse.RequestParser()
        task_id = parser.get_argument(
            "taskId", type=int, required=True, location="json"
        )
        task_type = parser.get_argument(
            "taskType", type=int, required=True, location="json"
        )

        error_message = parser.get_argument(
            "errorMessage", type=str, location="json", required=False
        )
        if error_message:
            save_task_result(task_id, task_type, error_message=error_message)
        else:
            if task_type == TaskType.transcription.id:
                original_text = parser.get_argument(
                    "transcriptionText", type=str, location="json", required=True
                )
                segments = parser.get_argument(
                    "segments", type=list, location="json", required=True
                )
                detected_language = parser.get_argument(
                    "detectedLanguage", type=str, location="json", required=False
                )
                service_provider = parser.get_argument(
                    "serviceProvider", type=str, location="json", required=False
                )
                save_task_result(
                    task_id,
                    task_type,
                    original_text=original_text,
                    segments=segments,
                    detected_language=detected_language,
                    service_provider=service_provider,
                )
            elif task_type == TaskType.summary.id:
                summary = parser.get_argument(
                    "summary", type=str, location="json", required=True
                )
                save_task_result(task_id, task_type, summary=summary)
            elif task_type == TaskType.outline.id:
                outline = parser.get_argument(
                    "outline", type=str, location="json", required=True
                )
                save_task_result(task_id, task_type, outline=outline)
            elif task_type == TaskType.translation.id:
                # TODO
                pass
            elif task_type == TaskType.qa_extraction.id:
                qa_extraction = parser.get_argument(
                    "qaExtraction", type=list, location="json", required=True
                )
                save_task_result(task_id, task_type, qa_extraction=qa_extraction)
            elif task_type == TaskType.speaker_diarization.id:
                diarization_segments = parser.get_argument(
                    "diarizationSegments", type=dict, location="json", required=True
                )
                save_task_result(
                    task_id,
                    task_type,
                    diarization_segments=diarization_segments,
                )
            else:
                raise NotImplemented

        task = Task.get_by_id(task_id)
        update_file_status(file_id=task.file_id)
        return {"message": "success"}


class TaskStatusResource(Resource):
    """任务状态更新接口 - 供 Go Service 调用"""

    def post(self):
        """更新任务状态"""
        parser = reqparse.RequestParser()
        parser.add_argument("taskId", type=int, required=True, location="json")
        parser.add_argument("status", type=int, required=True, location="json")  # 改为 int 类型
        parser.add_argument("error", type=str, required=False, location="json")
        args = parser.parse_args()

        task_id = args["taskId"]
        status = args["status"]
        error_msg = args.get("error")

        # 查找任务
        task = Task.get_by_id(task_id)
        if not task:
            return {"error": f"Task {task_id} not found"}, 404

        # 更新任务状态 - 直接使用数字 ID
        if status == TaskStatus.processing.id:
            task.status = TaskStatus.processing.id
            task.started_time = datetime.now()
        elif status == TaskStatus.completed.id:
            task.status = TaskStatus.completed.id
            task.completed_time = datetime.now()
        elif status == TaskStatus.failed.id:
            task.status = TaskStatus.failed.id
            task.completed_time = datetime.now()
            if error_msg:
                task.error_message = error_msg
        else:
            return {"error": f"Invalid status: {status}"}, 400

        # 保存到数据库
        db.session.commit()

        # 更新文件状态 - 修复缺失的 file status 更新
        update_file_status(file_id=task.file_id)

        return {
            "message": "Task status updated successfully",
            "taskId": task_id,
            "status": status
        }


class TaskStatusQueryResource(Resource):
    """Task status query API"""

    @auth_required
    @marshal_with(task_status_fields)
    def get(self, task_id):
        """Get task status information

        Args:
            task_id: Task ID from URL path

        Response:
        {
            "taskId": "12345",
            "status": "processing",
            "error": null,
            "startedTime": "2025-01-12T10:25:00.123Z",
            "updatedTime": "2025-01-12T10:30:00.456Z",
            "completedTime": null,
            "retryCount": 1,
            "taskType": "transcription"
        }
        """
        user_id = g.user.id

        # Get task information
        task = Task.get_by_id(task_id)
        if not task:
            raise TaskNotFoundError(f"Task not found: {task_id}")

        # Verify that the task's associated file belongs to the current user
        transcription_file = TranscriptionFile.get_by_id(task.file_id)
        if not transcription_file:
            raise TaskNotFoundError(f"Task associated file not found")

        if transcription_file.user_id != user_id:
            raise TaskPermissionDeniedError(f"No permission to access this task")

        # Return task object directly, marshal_with will handle the formatting
        return task
