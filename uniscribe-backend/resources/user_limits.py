from flask_restful import Resource, marshal_with
from flask import g
from resources.auth import auth_required
from fields.usage import user_limits_fields
from controllers.user_limits import get_user_limits


class UserLimitsResource(Resource):
    """用户使用限制查询接口"""

    @auth_required
    @marshal_with(user_limits_fields)
    def get(self):
        """
        获取当前用户的使用限制信息

        返回:
        - transcriptionMinutesRemaining: 当月剩余转录时长（分钟）
        - dailyTranscriptionCountRemaining: 当天剩余转录次数
        """
        limits = get_user_limits(g.user.id, g.user)
        return limits
