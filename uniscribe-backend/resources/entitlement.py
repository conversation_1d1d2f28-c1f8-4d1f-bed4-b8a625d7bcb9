from flask import g
from flask_restful import Resource, fields, marshal_with
from models.entitlement import Entitlement, EntitlementSource
from models.plan import Plan, PlanType
from models.file_storage import FileStorage
from resources.auth import auth_required
from config import CONFIG
import logging

logger = logging.getLogger(__name__)

plan_info_fields = {
    "id": fields.Integer,
    "name": fields.String,
    "tier": fields.String,
    "planType": fields.String(attribute="plan_type"),
    "creditAmount": fields.Integer(attribute="credit_amount"),
    "interval": fields.String,
    "currency": fields.String,
    "description": fields.String,
}

entitlement_fields = {
    "id": fields.Integer,
    "totalCredits": fields.Integer(attribute="total_credits"),
    "consumedCredits": fields.Integer(attribute="consumed_credits"),
    "remainingCredits": fields.Integer(
        attribute=lambda x: x.total_credits - x.consumed_credits
    ),
    "validFrom": fields.DateTime(attribute="valid_from"),
    "validUntil": fields.DateTime(attribute="valid_until"),
    "isRecurring": fields.Boolean(attribute="is_recurring"),
    "createdTime": fields.DateTime(attribute="created_time"),
    "source": fields.Nested(
        {
            "id": fields.Integer,
            "type": fields.String(attribute="source_type"),
            "plan": fields.Nested(plan_info_fields, allow_null=True),
        },
        allow_null=True,
        attribute="source",
    ),
}

response_fields = {
    "summary": fields.Nested(
        {
            "totalCredits": fields.Integer(attribute="total_credits"),
            "remainingCredits": fields.Integer(attribute="remaining_credits"),
            "consumedCredits": fields.Integer(attribute="consumed_credits"),
            "totalStorage": fields.Float(attribute="total_storage"),
            "consumedStorage": fields.Float(attribute="consumed_storage"),
        }
    ),
    "entitlements": fields.List(fields.Nested(entitlement_fields)),
}


class EntitlementResource(Resource):
    def _get_plan_info(self, entitlement):
        """获取权益相关的套餐信息"""
        plan = None

        # 根据 source_type 获取对应的 plan
        if entitlement.source_type == EntitlementSource.SUBSCRIPTION:
            # 如果是订阅，需要先获取 subscription 再获取 plan
            from models.subscription import Subscription

            subscription = Subscription.get_by_id(entitlement.source_id)
            if subscription:
                plan = Plan.get_by_id(subscription.plan_id)

        elif entitlement.source_type == EntitlementSource.ONE_TIME:
            # 如果是一次性购买，需要先获取 purchase 再获取 plan
            from models.purchase import Purchase

            purchase = Purchase.get_by_id(entitlement.source_id)
            if purchase:
                plan = Plan.get_by_id(purchase.plan_id)

        elif entitlement.source_type == EntitlementSource.LTD:
            # 如果是 LTD 计划，需要先获取 purchase 再获取 plan
            from models.purchase import Purchase

            purchase = Purchase.get_by_id(entitlement.source_id)
            if purchase:
                plan = Plan.get_by_id(purchase.plan_id)

        elif entitlement.source_type == EntitlementSource.FREE_PLAN:
            # 如果是免费计划，直接获取免费计划
            plan = Plan.query.filter_by(plan_type=PlanType.FREE).first()

        # 创建 source 结构
        if entitlement.source_type in [
            EntitlementSource.SUBSCRIPTION,
            EntitlementSource.ONE_TIME,
            EntitlementSource.LTD,
            EntitlementSource.FREE_PLAN,
        ]:
            # 创建一个包含 plan 的 source 结构
            return type(
                "SourceInfo",
                (),
                {
                    "id": entitlement.source_id,
                    "source_type": entitlement.source_type,
                    "plan": plan,
                },
            )()
        if entitlement.source_type in [
            EntitlementSource.BONUS,
            EntitlementSource.REFERRAL,
        ]:
            # 为奖励和推荐类型创建一个简单的对象
            return type(
                "SourceInfo",
                (),
                {
                    "id": entitlement.source_id,
                    "source_type": entitlement.source_type,
                    "plan": None,
                },
            )()
        return None

    def _get_ltd_storage_info(self, user_id, active_entitlements):
        """获取 LTD 用户的存储信息

        Args:
            user_id: 用户ID
            active_entitlements: 用户的活跃权益列表

        Returns:
            tuple: (total_storage_gb, consumed_storage_gb) 或 (None, None) 如果不是LTD用户
        """
        # 检查是否有 LTD 权益
        ltd_entitlement = None
        for entitlement in active_entitlements:
            if entitlement.source_type == EntitlementSource.LTD:
                ltd_entitlement = entitlement
                break

        if not ltd_entitlement:
            return None, None

        # 获取 LTD 计划信息
        from models.purchase import Purchase

        purchase = Purchase.get_by_id(ltd_entitlement.source_id)
        assert purchase, f"Purchase not found for LTD entitlement {ltd_entitlement.id}"

        plan = Plan.get_by_id(purchase.plan_id)
        assert plan, f"Plan not found for LTD purchase {purchase.id}"

        # 从配置中获取存储限制
        total_storage_gb = CONFIG.LTD_STORAGE_LIMITS.get(plan.tier)
        assert (
            total_storage_gb is not None
        ), f"Storage limit not found for tier {plan.tier}"

        # 获取用户已使用的存储空间（字节）
        consumed_storage_bytes = FileStorage.get_user_storage_usage(user_id)
        # 转换为 GB，保留两位小数
        consumed_storage_gb = round(consumed_storage_bytes / (1024**3), 2)

        return total_storage_gb, consumed_storage_gb

    @auth_required
    @marshal_with(response_fields)
    def get(self):
        """获取用户权益信息"""
        user_id = g.user.id

        # 获取权益详细列表
        active_entitlements = Entitlement.get_active_entitlements(user_id)

        # 为每个权益添加 source 信息
        for entitlement in active_entitlements:
            # 获取包含 plan 的 source 结构
            entitlement.source = self._get_plan_info(entitlement)

        total_credits = sum(
            entitlement.total_credits for entitlement in active_entitlements
        )
        consumed_credits = sum(
            entitlement.consumed_credits for entitlement in active_entitlements
        )

        logger.info("total credits: %s", total_credits)

        # 获取存储信息（仅对 LTD 用户）
        total_storage, consumed_storage = self._get_ltd_storage_info(
            user_id, active_entitlements
        )

        summary = type(
            "Summary",
            (),
            {
                "total_credits": total_credits,
                "remaining_credits": total_credits - consumed_credits,
                "consumed_credits": consumed_credits,
                "total_storage": total_storage,
                "consumed_storage": consumed_storage,
            },
        )()

        return {"summary": summary, "entitlements": active_entitlements}
