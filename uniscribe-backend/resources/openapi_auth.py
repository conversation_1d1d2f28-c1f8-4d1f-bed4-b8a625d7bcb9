"""
OpenAPI Authentication module
Provides API Key authentication for external integrations
"""

import hashlib
import secrets
from functools import wraps
from typing import Optional
import logging

from flask import request, g

from models.api_key import APIKey
from exceptions.user import DeactivatedAccountError
from exceptions.openapi import (
    APIKeyRequiredError,
    InvalidAPIKeyError,
    APIAccessDeniedError,
)

logger = logging.getLogger(__name__)

API_KEY_REQUIRED = "API Key is required"
INVALID_API_KEY = "Invalid API Key"
API_ACCESS_DENIED = "API access requires active subscription or LTD plan"


def generate_api_key() -> tuple[str, str]:
    """
    Generate a new API key pair
    Returns: (public_key, hashed_key) tuple
    """
    # Generate a random key with us_ prefix
    random_part = secrets.token_urlsafe(32)
    key = f"us_{random_part}"

    # Create hash for storage
    key_hash = hashlib.sha256(key.encode()).hexdigest()

    return key, key_hash


def verify_api_key(api_key: str) -> Optional[APIKey]:
    """
    Verify API key and return APIKey model if valid
    """
    if not api_key:
        return None

    # Hash the provided key
    key_hash = hashlib.sha256(api_key.encode()).hexdigest()

    # Find matching API key
    api_key_record = APIKey.query.filter_by(key_hash=key_hash, is_active=True).first()

    if api_key_record:
        # Check if expired
        if api_key_record.is_expired():
            return None

        # Update last used timestamp
        api_key_record.update_last_used()
        return api_key_record

    return None


def api_key_required(f):
    """
    Decorator for API Key authentication
    Only supports X-API-Key header authentication
    """

    @wraps(f)
    def decorated(*args, **kwargs):
        # Get API Key from header
        api_key = request.headers.get("X-API-Key")

        if not api_key:
            raise APIKeyRequiredError(API_KEY_REQUIRED)

        api_key_record = verify_api_key(api_key)
        if not api_key_record:
            raise InvalidAPIKeyError(INVALID_API_KEY)

        g.user = api_key_record.user
        g.api_key = api_key_record

        # Check if user account is deactivated
        if g.user.is_deactivated == 1:
            logger.warning(f"Deactivated account API access attempt: {g.user.id}")
            raise DeactivatedAccountError(message="Account has been deactivated")

        # Check API access permission
        if not g.user.has_api_access:
            raise APIAccessDeniedError(API_ACCESS_DENIED)

        return f(*args, **kwargs)

    return decorated


def openapi_auth_required(f):
    """
    Alias for api_key_required for OpenAPI endpoints
    """
    return api_key_required(f)
