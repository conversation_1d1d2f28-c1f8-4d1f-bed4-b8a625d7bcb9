"""
API Key management resources
"""

import logging
from flask_restful import Resource, marshal_with
from flask import g

from resources.auth import auth_required
from controllers.api_key import (
    list_user_api_keys,
    create_api_key,
    get_api_key,
    update_api_key,
    reset_api_key,
    delete_api_key,
)
from fields.api_key import (
    api_key_list_fields,
    api_key_response_fields,
    api_key_creation_response_fields,
    api_key_delete_response_fields,
)
from libs import reqparse
from exceptions.openapi import APIAccessDeniedError

logger = logging.getLogger(__name__)


class APIKeysResource(Resource):
    """API Keys collection resource"""

    def __init__(self):
        self.parser = reqparse.RequestParser()
        # For POST (create)
        self.parser.add_argument(
            "name", type=str, required=True, location="json",
            help="API key name is required"
        )
        self.parser.add_argument(
            "expiresDays", type=int, required=False, location="json",
            help="Number of days until expiration (1-3650)"
        )
        super().__init__()

    @auth_required
    @marshal_with(api_key_list_fields)
    def get(self):
        """List all API keys for the authenticated user"""
        user = g.user
        
        # Check API access permission
        if not user.has_api_access:
            raise APIAccessDeniedError("API access requires active subscription or LTD plan")
        
        api_keys = list_user_api_keys(user.id)
        
        return {
            "success": True,
            "data": {
                "apiKeys": api_keys
            },
            "message": "Success"
        }

    @auth_required
    @marshal_with(api_key_creation_response_fields)
    def post(self):
        """Create a new API key"""
        user = g.user
        
        # Check API access permission
        if not user.has_api_access:
            raise APIAccessDeniedError("API access requires active subscription or LTD plan")
        
        args = self.parser.parse_args()
        name = args["name"]
        expires_days = args.get("expiresDays")
        
        # Validate expires_days
        if expires_days is not None:
            if expires_days < 1 or expires_days > 3650:
                raise ValueError("expiresDays must be between 1 and 3650")
        
        # Validate name length
        if len(name) > 100:
            raise ValueError("API key name must be 100 characters or less")
        
        api_key_data = create_api_key(user.id, name, expires_days)
        
        return {
            "success": True,
            "data": api_key_data,
            "message": "API key created successfully"
        }


class APIKeyResource(Resource):
    """Individual API Key resource"""

    def __init__(self):
        self.parser = reqparse.RequestParser()
        # For PUT (update)
        self.parser.add_argument(
            "name", type=str, required=True, location="json",
            help="API key name is required"
        )
        super().__init__()

    @auth_required
    @marshal_with(api_key_response_fields)
    def get(self, api_key_id):
        """Get a specific API key"""
        user = g.user
        
        # Check API access permission
        if not user.has_api_access:
            raise APIAccessDeniedError("API access requires active subscription or LTD plan")
        
        api_key_data = get_api_key(user.id, api_key_id)
        
        return {
            "success": True,
            "data": api_key_data,
            "message": "Success"
        }

    @auth_required
    @marshal_with(api_key_response_fields)
    def put(self, api_key_id):
        """Update an API key (name only)"""
        user = g.user
        
        # Check API access permission
        if not user.has_api_access:
            raise APIAccessDeniedError("API access requires active subscription or LTD plan")
        
        args = self.parser.parse_args()
        name = args["name"]
        
        # Validate name length
        if len(name) > 100:
            raise ValueError("API key name must be 100 characters or less")
        
        api_key_data = update_api_key(user.id, api_key_id, name)
        
        return {
            "success": True,
            "data": api_key_data,
            "message": "API key updated successfully"
        }

    @auth_required
    @marshal_with(api_key_delete_response_fields)
    def delete(self, api_key_id):
        """Delete (deactivate) an API key"""
        user = g.user
        
        # Check API access permission
        if not user.has_api_access:
            raise APIAccessDeniedError("API access requires active subscription or LTD plan")
        
        delete_api_key(user.id, api_key_id)
        
        return {
            "success": True,
            "message": "API key deleted successfully"
        }


class APIKeyResetResource(Resource):
    """API Key reset resource"""

    @auth_required
    @marshal_with(api_key_creation_response_fields)
    def post(self, api_key_id):
        """Reset an API key (generate new key value)"""
        user = g.user
        
        # Check API access permission
        if not user.has_api_access:
            raise APIAccessDeniedError("API access requires active subscription or LTD plan")
        
        api_key_data = reset_api_key(user.id, api_key_id)
        
        return {
            "success": True,
            "data": api_key_data,
            "message": "API key reset successfully"
        }
