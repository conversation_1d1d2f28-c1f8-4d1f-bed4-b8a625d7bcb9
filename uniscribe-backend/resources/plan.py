from flask_restful import Resource, marshal_with, abort
from flask import g

from config import CONFIG
from models.share import Share
from models.user import User
from resources.auth import auth_required
from fields.plan import plan_config_fields
from libs import reqparse


class PlanConfigResource(Resource):

    @auth_required
    @marshal_with(plan_config_fields)
    def get(self):
        plan_config = CONFIG.PLAN_FEATURES[g.user.primary_plan]
        return plan_config


class SharerPlanConfigResource(Resource):
    # no auth required
    @marshal_with(plan_config_fields)
    def get(self):
        parser = reqparse.RequestParser()
        share_code = parser.get_argument(
            "shareCode", type=str, required=True, location="args"
        )

        share = Share.get_by_share_code(share_code)
        if not share:
            abort(404, message="Share not found")
        sharer = User.get_by_id(share.user_id)
        if not sharer:
            abort(404, message="Sharer not found")

        plan_config = CONFIG.PLAN_FEATURES[sharer.primary_plan]
        return plan_config
