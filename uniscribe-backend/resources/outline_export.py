import logging

from flask_restful import Resource, abort
from werkzeug.exceptions import HTTPException
from flask import g

from libs import reqparse
from controllers.export import export_outline
from resources.auth import auth_required

logger = logging.getLogger(__name__)


class OutlineExportResource(Resource):
    """Outline导出资源类"""
    
    allowed_formats = ["md", "xmind"]  # 支持Markdown和XMind格式

    @auth_required
    def post(self):
        """
        导出单个转录文件的outline内容
        
        请求参数:
        - transcriptionId (str, required): 转录文件ID
        - format (str, optional): 导出格式，默认为'md'
        
        返回:
        - 文件下载响应，文件名格式：filename_yymmdd_hhmmss_mindmap.format
        """
        user_id = g.user.id
        parser = reqparse.RequestParser()
        
        transcription_id = parser.get_argument(
            "transcriptionId", type=str, required=True, location="json"
        )
        format_type = parser.get_argument(
            "format", type=str, required=False, location="json", default="md"
        )
        
        # 验证格式类型
        if format_type not in self.allowed_formats:
            abort(400, message=f"Invalid format type. Supported formats: {', '.join(self.allowed_formats)}")
        
        # 记录请求日志
        logger.info(
            f"Outline export request - user_id: {user_id}, transcription_id: {transcription_id}, format: {format_type}"
        )
        
        try:
            response = export_outline(format_type, transcription_id, user_id)
            
            logger.info(
                f"Outline export successful - user_id: {user_id}, transcription_id: {transcription_id}"
            )
            
            return response
            
        except HTTPException as e:
            logger.warning(
                f"Outline export HTTP error - user_id: {user_id}, transcription_id: {transcription_id}, error: {str(e)}"
            )
            raise
        except Exception as e:
            logger.exception(
                f"Outline export unexpected error - user_id: {user_id}, transcription_id: {transcription_id}"
            )
            abort(500, message="An error occurred during outline export")
