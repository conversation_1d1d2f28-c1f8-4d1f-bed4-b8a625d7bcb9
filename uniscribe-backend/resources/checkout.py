from flask_restful import Resource
from flask import g

from controllers.stripe import StripeController
from libs import reqparse
from resources.auth import auth_required


class StripeCheckoutResource(Resource):

    @auth_required
    def post(self):
        user = g.user
        parser = reqparse.RequestParser()
        plan = parser.get_argument("plan", type=str, required=True, location="json")
        mode = parser.get_argument("mode", type=str, required=True, location="json")
        promotion_code = parser.get_argument(
            "promotionCode", type=str, required=False, location="json"
        )
        quantity = parser.get_argument(
            "quantity", type=int, required=False, location="json"
        )
        source = parser.get_argument(
            "source", type=str, required=False, location="json"
        )
        checkout_session = StripeController.create_checkout_session(
            user=user,
            mode=mode,
            plan=plan,
            quantity=quantity,
            promotion_code=promotion_code,
            source=source,
        )
        return {"url": checkout_session.url}
