from flask_restful import Resource, reqparse
from flask import g
from controllers.appsumo_webhook import AppSumoWebhookController
from controllers.appsumo_oauth import AppSumoOAuthController
from resources.auth import auth_required
from exceptions.appsumo import AppSumoLicenseActivationError


class AppSumoWebhookResource(Resource):
    def post(self):
        """处理 AppSumo webhook"""
        return AppSumoWebhookController.handle_webhook()


class AppSumoActivateResource(Resource):
    def __init__(self):
        self.parser = reqparse.RequestParser()
        self.parser.add_argument("code", type=str, required=True, location="json")
        super().__init__()

    @auth_required
    def post(self):
        """为已登录用户激活 AppSumo license"""
        args = self.parser.parse_args()
        user_id = g.user.id

        if g.user.is_anonymous:
            # 报错，匿名用户不能激活 license
            raise AppSumoLicenseActivationError(
                message="Anonymous user cannot activate license. Please register an account first."
            )

        return AppSumoOAuthController.activate_license(user_id, args["code"])
