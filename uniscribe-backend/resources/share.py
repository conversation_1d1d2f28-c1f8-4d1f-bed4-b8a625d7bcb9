import uuid
from flask_restful import Resource, marshal_with, abort
from flask import g
from models import db_transaction
from models.share import Share
from models.transcription_file import TranscriptionFile
from models import db
from resources.auth import auth_required
from fields.transcription import transcription_file_fields
from controllers.transcription import get_transcription_file_with_result
from libs.id_generator import id_generator


class ShareResource(Resource):
    @auth_required
    def post(self, transcription_file_id):
        # 检查 transcript 是否存在且属于当前用户
        transcript = TranscriptionFile.get_by_id(transcription_file_id)
        if not transcript or transcript.user_id != g.user.id:
            abort(404, message="Transcription file not found or no permission")

        # 检查是否已经存在分享
        existing_share = Share.query.filter_by(file_id=transcription_file_id).first()
        if existing_share:
            return {"shareCode": existing_share.share_code}

        # 创建新的分享
        share = Share(
            file_id=transcription_file_id,
            user_id=g.user.id,
            share_code=str(uuid.uuid4()),  # 直接使用完整的 UUID
        )
        with db_transaction():
            db.session.add(share)

        return {"shareCode": share.share_code}

    # no auth required
    @marshal_with(transcription_file_fields)
    def get(self, share_code):
        share = Share.get_by_share_code(share_code)
        if not share:
            abort(404, message="Share not found")

        transcript = TranscriptionFile.get_by_id(share.file_id)
        if not transcript:
            abort(404, message="Transcription file not found")
        # 格式化转录文件信息（包括生成预签名URL等）
        transcript = get_transcription_file_with_result(
            transcript.user_id, transcript.id
        )
        return transcript
