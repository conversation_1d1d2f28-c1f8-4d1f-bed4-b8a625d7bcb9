from flask_restful import Resource, reqparse
from flask import g
import logging

from resources.auth import auth_required
from controllers.user import deactivate_account

logger = logging.getLogger(__name__)


class DeactivateAccountResource(Resource):
    def __init__(self):
        self.parser = reqparse.RequestParser()
        self.parser.add_argument(
            "confirm",
            type=bool,
            required=True,
            location="json",
            help="Confirmation is required to deactivate account",
        )
        super().__init__()

    @auth_required
    def post(self):
        """注销用户账户

        需要用户确认才能执行注销操作。
        注销后，用户的所有数据将被标记为已删除，无法恢复。
        """
        args = self.parser.parse_args()

        # 检查确认标志
        if not args.get("confirm"):
            return {"message": "Account deactivation requires confirmation"}, 400

        user_id = g.user.id

        try:
            # 执行账户注销操作
            deactivate_account(user_id, dry_run=False)

            logger.info("User %s account deactivated successfully", user_id)
            return {"message": "Account deactivated successfully"}, 200

        except Exception as e:
            logger.error(
                "Failed to deactivate account for user %s: %s", user_id, str(e)
            )
            # 重新抛出异常，让 Flask-RESTful 的异常处理机制处理
            raise
