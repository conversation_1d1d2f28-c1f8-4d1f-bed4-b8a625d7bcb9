#!/usr/bin/env python3
"""
重构 segments 数据结构的迁移脚本

将复杂的 segments/aligned_segments 双字段设计重构为单一 segments 字段设计
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text
import json


# 简化的应用创建，避免加载完整的应用程序
def create_minimal_app():
    app = Flask(__name__)

    # 从环境变量加载数据库配置
    import os

    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError("DATABASE_URL environment variable is required")

    app.config["SQLALCHEMY_DATABASE_URI"] = database_url
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False

    db = SQLAlchemy(app)
    return app, db


def add_original_segments_field(db):
    """添加 original_segments 字段"""
    print("1. 添加 original_segments 字段...")

    try:
        # 检查字段是否已存在
        result = db.session.execute(
            text(
                """
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'task_result' 
                AND COLUMN_NAME = 'original_segments'
            """
            )
        )

        if result.fetchone():
            print("  - original_segments 字段已存在，跳过...")
            return True

        # 添加字段
        db.session.execute(
            text(
                """
                ALTER TABLE task_result 
                ADD COLUMN original_segments JSON NULL 
                COMMENT '原始转录结果(用于调试)'
            """
            )
        )

        db.session.commit()
        print("  - 成功添加 original_segments 字段")
        return True

    except Exception as e:
        print(f"  - 添加 original_segments 字段失败: {e}")
        db.session.rollback()
        return False


def migrate_data(db):
    """迁移现有数据到新结构"""
    print("2. 迁移现有数据...")

    try:
        # 直接使用 SQL 查询，避免依赖模型
        result = db.session.execute(
            text(
                "SELECT id, file_id, segments, aligned_segments FROM task_result WHERE segments IS NOT NULL"
            )
        )
        results = result.fetchall()

        print(f"  - 找到 {len(results)} 条需要迁移的记录")

        migrated_count = 0
        for result in results:
            try:
                # 保存原始的 segments 到 original_segments
                if result.segments and not result.original_segments:
                    result.original_segments = result.segments

                    # 如果有 aligned_segments，将其作为最终的 segments
                    if result.aligned_segments:
                        result.segments = result.aligned_segments
                        print(
                            f"    - 文件 {result.file_id}: 使用 aligned_segments 作为最终结果"
                        )
                    else:
                        print(f"    - 文件 {result.file_id}: 保持原有 segments")

                    migrated_count += 1

            except Exception as e:
                print(f"    - 迁移文件 {result.file_id} 失败: {e}")
                continue

        db.session.commit()
        print(f"  - 成功迁移 {migrated_count} 条记录")
        return True

    except Exception as e:
        print(f"  - 数据迁移失败: {e}")
        db.session.rollback()
        return False


def remove_aligned_segments_field():
    """删除 aligned_segments 字段"""
    print("3. 删除 aligned_segments 字段...")

    try:
        # 检查字段是否存在
        result = db.session.execute(
            text(
                """
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'task_result' 
                AND COLUMN_NAME = 'aligned_segments'
            """
            )
        )

        if not result.fetchone():
            print("  - aligned_segments 字段不存在，跳过...")
            return True

        # 删除字段
        db.session.execute(
            text(
                """
                ALTER TABLE task_result 
                DROP COLUMN aligned_segments
            """
            )
        )

        db.session.commit()
        print("  - 成功删除 aligned_segments 字段")
        return True

    except Exception as e:
        print(f"  - 删除 aligned_segments 字段失败: {e}")
        db.session.rollback()
        return False


def verify_migration():
    """验证迁移结果"""
    print("4. 验证迁移结果...")

    try:
        # 检查字段结构
        result = db.session.execute(
            text(
                """
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'task_result' 
                AND COLUMN_NAME IN ('segments', 'original_segments', 'diarization_segments')
                ORDER BY COLUMN_NAME
            """
            )
        )

        fields = result.fetchall()
        print("  - 字段结构:")
        for field in fields:
            print(f"    - {field[0]}: {field[1]} ({field[2]}) - {field[3]}")

        # 检查数据完整性
        total_count = TaskResult.query.count()
        segments_count = TaskResult.query.filter(
            TaskResult.segments.isnot(None)
        ).count()
        original_segments_count = TaskResult.query.filter(
            TaskResult.original_segments.isnot(None)
        ).count()

        print(f"  - 数据统计:")
        print(f"    - 总记录数: {total_count}")
        print(f"    - 有 segments 的记录: {segments_count}")
        print(f"    - 有 original_segments 的记录: {original_segments_count}")

        # 检查是否还有 aligned_segments 字段
        result = db.session.execute(
            text(
                """
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'task_result' 
                AND COLUMN_NAME = 'aligned_segments'
            """
            )
        )

        if result.fetchone():
            print("  - ❌ aligned_segments 字段仍然存在")
            return False
        else:
            print("  - ✅ aligned_segments 字段已成功删除")

        print("  - ✅ 迁移验证通过")
        return True

    except Exception as e:
        print(f"  - 验证失败: {e}")
        return False


def rollback_migration():
    """回滚迁移（紧急情况使用）"""
    print("执行回滚操作...")

    try:
        # 重新添加 aligned_segments 字段
        db.session.execute(
            text(
                """
                ALTER TABLE task_result 
                ADD COLUMN aligned_segments JSON NULL 
                COMMENT '对齐后的分段(包含说话人信息)'
            """
            )
        )

        # 从 segments 恢复 aligned_segments（如果有说话人信息）
        results = TaskResult.query.filter(TaskResult.segments.isnot(None)).all()

        for result in results:
            if result.segments and any("speaker" in seg for seg in result.segments):
                result.aligned_segments = result.segments
                # 从 original_segments 恢复原始 segments
                if result.original_segments:
                    result.segments = result.original_segments

        # 删除 original_segments 字段
        db.session.execute(
            text(
                """
                ALTER TABLE task_result 
                DROP COLUMN original_segments
            """
            )
        )

        db.session.commit()
        print("✅ 回滚完成")
        return True

    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        db.session.rollback()
        return False


def main():
    """主函数"""
    app = create_app()

    if len(sys.argv) > 1 and sys.argv[1] == "--rollback":
        print("⚠️  执行回滚操作...")
        with app.app_context():
            success = rollback_migration()
            if success:
                print("✅ 回滚成功")
            else:
                print("❌ 回滚失败")
                sys.exit(1)
        return

    print("🚀 开始重构 segments 数据结构...")
    print("=" * 60)

    with app.app_context():
        # 执行迁移步骤
        steps = [
            add_original_segments_field,
            migrate_data,
            remove_aligned_segments_field,
            verify_migration,
        ]

        for step in steps:
            if not step():
                print("❌ 迁移失败，请检查错误信息")
                sys.exit(1)

    print("=" * 60)
    print("✅ 重构完成！")
    print("\n下一步:")
    print("1. 更新代码以使用新的数据结构")
    print("2. 重启应用服务")
    print("3. 测试功能是否正常")
    print("\n如需回滚，请运行: python cli/refactor_segments_migration.py --rollback")


if __name__ == "__main__":
    main()
