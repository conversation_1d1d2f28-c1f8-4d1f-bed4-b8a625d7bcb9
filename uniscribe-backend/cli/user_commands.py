"""
用户管理相关的 CLI 命令
"""

import logging
import click
from flask.cli import with_appcontext

from models import db, db_transaction
from models.user import User

logger = logging.getLogger(__name__)


@click.command("swap-user-data")
@click.option("--user-id-a", type=int, required=True, help="用户A的ID")
@click.option("--user-id-b", type=int, required=True, help="用户B的ID")
@click.option("--dry-run", is_flag=True, help="预览模式，不实际执行修改")
@click.option("--force", is_flag=True, help="跳过确认提示")
@with_appcontext
def swap_user_data(user_id_a, user_id_b, dry_run, force):
    """
    交换两个用户的登录身份数据（保持ID和stripe_customer_id不变）

    该命令会交换以下字段：
    - supabase_id (登录凭证)
    - email (邮箱)
    - full_name (全名)
    - first_name (名)
    - last_name (姓)
    - avatar_url (头像)
    - provider (登录提供商)

    保持不变的字段：
    - id (主键)
    - stripe_customer_id (Stripe客户ID)
    - 所有关联表的数据

    使用场景：
    同一个人有两个账户（如Google登录的付费账户和企业邮箱的免费账户），
    希望能用企业邮箱登录后看到付费账户的数据。

    示例:
    flask admin swap-user-data --user-id-a=123 --user-id-b=456 --dry-run
    flask admin swap-user-data --user-id-a=123 --user-id-b=456
    """
    try:
        logger.info(
            "开始用户数据交换: %s <-> %s (dry_run=%s)",
            user_id_a,
            user_id_b,
            dry_run,
        )

        # 1. 验证用户存在
        user_a = User.get_by_id(user_id_a)
        if not user_a:
            click.echo(f"❌ 用户A (ID: {user_id_a}) 不存在")
            return

        user_b = User.get_by_id(user_id_b)
        if not user_b:
            click.echo(f"❌ 用户B (ID: {user_id_b}) 不存在")
            return

        # 2. 显示用户信息
        click.echo("\n📋 用户信息:")
        click.echo(f"用户A (ID: {user_a.id}):")
        click.echo(f"  📧 邮箱: {user_a.email}")
        click.echo(f"  👤 姓名: {user_a.full_name}")
        click.echo(f"  🔑 Supabase ID: {user_a.supabase_id}")
        click.echo(f"  🏢 登录方式: {user_a.provider}")
        click.echo(f"  💳 Stripe Customer: {user_a.stripe_customer_id or '无'}")
        click.echo(f"  💰 计划: {user_a.primary_plan}")

        click.echo(f"\n用户B (ID: {user_b.id}):")
        click.echo(f"  📧 邮箱: {user_b.email}")
        click.echo(f"  👤 姓名: {user_b.full_name}")
        click.echo(f"  🔑 Supabase ID: {user_b.supabase_id}")
        click.echo(f"  🏢 登录方式: {user_b.provider}")
        click.echo(f"  💳 Stripe Customer: {user_b.stripe_customer_id or '无'}")
        click.echo(f"  💰 计划: {user_b.primary_plan}")

        # 3. 显示交换后的效果
        click.echo("\n🔄 交换后效果预览:")
        click.echo(f"用户A (ID: {user_a.id}) 将拥有:")
        click.echo(f"  📧 邮箱: {user_b.email}")
        click.echo(f"  👤 姓名: {user_b.full_name}")
        click.echo(f"  🔑 Supabase ID: {user_b.supabase_id}")
        click.echo(f"  🏢 登录方式: {user_b.provider}")
        click.echo(f"  💳 Stripe Customer: {user_a.stripe_customer_id or '无'} (不变)")
        click.echo(f"  💰 计划: {user_a.primary_plan} (不变)")

        click.echo(f"\n用户B (ID: {user_b.id}) 将拥有:")
        click.echo(f"  📧 邮箱: {user_a.email}")
        click.echo(f"  👤 姓名: {user_a.full_name}")
        click.echo(f"  🔑 Supabase ID: {user_a.supabase_id}")
        click.echo(f"  🏢 登录方式: {user_a.provider}")
        click.echo(f"  💳 Stripe Customer: {user_b.stripe_customer_id or '无'} (不变)")
        click.echo(f"  💰 计划: {user_b.primary_plan} (不变)")

        # 4. 检查潜在冲突
        conflicts = []

        # 检查 supabase_id 冲突
        if user_a.supabase_id == user_b.supabase_id:
            conflicts.append("两个用户的 supabase_id 相同，无法交换")

        # 检查是否有其他用户使用相同的 supabase_id
        other_users_a = User.query.filter(
            User.supabase_id == user_b.supabase_id,
            User.id != user_a.id,
            User.id != user_b.id,
        ).first()
        if other_users_a:
            conflicts.append(f"已有其他用户使用 supabase_id: {user_b.supabase_id}")

        other_users_b = User.query.filter(
            User.supabase_id == user_a.supabase_id,
            User.id != user_a.id,
            User.id != user_b.id,
        ).first()
        if other_users_b:
            conflicts.append(f"已有其他用户使用 supabase_id: {user_a.supabase_id}")

        if conflicts:
            click.echo("\n⚠️  发现冲突:")
            for conflict in conflicts:
                click.echo(f"  - {conflict}")
            return

        if dry_run:
            click.echo("\n🔍 预览模式 - 不会实际修改数据")
            return

        # 5. 确认操作
        if not force:
            click.echo("\n⚠️  警告: 此操作将交换两个用户的登录身份数据!")
            click.echo("请确保您了解此操作的影响。")
            if not click.confirm("确定要继续吗?"):
                click.echo("操作已取消")
                return

        # 6. 执行交换
        _perform_user_data_swap(user_a, user_b)

        click.echo("\n✅ 用户数据交换完成!")
        click.echo(f"用户 {user_id_a} 现在可以用 {user_b.email} 的登录方式访问")
        click.echo(f"用户 {user_id_b} 现在可以用 {user_a.email} 的登录方式访问")

    except Exception as e:
        logger.error("用户数据交换失败: %s", str(e), exc_info=True)
        click.echo(f"❌ 操作失败: {str(e)}")


@db_transaction()
def _perform_user_data_swap(user_a, user_b):
    """执行用户数据交换的核心逻辑"""
    import uuid

    # 需要交换的字段
    fields_to_swap = [
        "supabase_id",
        "email",
        "full_name",
        "first_name",
        "last_name",
        "avatar_url",
        "provider",
    ]

    # 临时存储用户A和B的数据
    temp_data_a = {}
    temp_data_b = {}
    for field in fields_to_swap:
        temp_data_a[field] = getattr(user_a, field)
        temp_data_b[field] = getattr(user_b, field)

    # 第一步：将两个用户的supabase_id都设置为临时值，避免唯一约束冲突
    temp_supabase_id_a = f"temp_a_{uuid.uuid4()}"
    temp_supabase_id_b = f"temp_b_{uuid.uuid4()}"

    user_a.supabase_id = temp_supabase_id_a
    user_b.supabase_id = temp_supabase_id_b
    db.session.flush()

    # 第二步：交换除supabase_id外的所有字段
    for field in fields_to_swap:
        if field != "supabase_id":
            # 交换字段值
            setattr(user_a, field, temp_data_b[field])
            setattr(user_b, field, temp_data_a[field])

    db.session.flush()

    # 第三步：最后交换supabase_id
    user_a.supabase_id = temp_data_b["supabase_id"]
    user_b.supabase_id = temp_data_a["supabase_id"]

    # 提交所有更改
    db.session.flush()

    logger.info("用户数据交换完成: %s <-> %s", user_a.id, user_b.id)


# 注册命令到 admin_cli
from . import admin_cli

admin_cli.add_command(swap_user_data)
