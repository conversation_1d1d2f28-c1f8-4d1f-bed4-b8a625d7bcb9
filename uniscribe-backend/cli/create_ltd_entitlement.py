"""
CLI script to create LTD entitlements for specified users.

This script allows administrators to create LTD (Lifetime Deal) entitlements
for users by specifying user ID and tier. It creates both Purchase and
Entitlement records in the database.

Usage:
    flask admin create-ltd-entitlement --user-id=123456 --tier=1
    flask admin create-ltd-entitlement --user-id=123456 --tier=2 --dry-run
"""

import logging
import time
from datetime import datetime

import click
from flask.cli import with_appcontext

from models import db, db_transaction
from models.user import User
from models.plan import Plan, PlanType
from models.purchase import Purchase
from models.entitlement import Entitlement, EntitlementSource
from services.entitlement_service import EntitlementService
from libs.id_generator import id_generator

logger = logging.getLogger(__name__)


@click.command("create-ltd-entitlement")
@click.option(
    "--user-id", type=int, required=True, help="User ID to create LTD entitlement for"
)
@click.option(
    "--tier",
    type=click.Choice(["1", "2"]),
    required=True,
    help="LTD tier: 1 (1200 credits/month) or 2 (6000 credits/month)",
)
@click.option(
    "--dry-run", is_flag=True, help="Show what would happen without making changes"
)
@with_appcontext
def create_ltd_entitlement(user_id, tier, dry_run):
    """
    Create LTD entitlement for a specified user and tier.

    This command creates both Purchase and Entitlement records for LTD plans.
    It's intended for administrative use to grant or handle private LTD purchases.

    Examples:
        flask admin create-ltd-entitlement --user-id=123456 --tier=1
        flask admin create-ltd-entitlement --user-id=123456 --tier=2 --dry-run
    """
    try:
        # 1. Validate user exists
        user = User.get_by_id(user_id)
        if not user:
            logger.error(f"User {user_id} not found")
            click.echo(f"❌ Error: User {user_id} not found", err=True)
            return

        logger.info(
            f"Found user: ID={user.id}, Email={user.email}, Name={user.full_name}"
        )
        click.echo(f"📋 User Info:")
        click.echo(f"   ID: {user.id}")
        click.echo(f"   Email: {user.email}")
        click.echo(f"   Name: {user.full_name}")
        click.echo(f"   Current Plan: {user.primary_plan}")

        # 2. Find LTD plan for the specified tier
        tier_mapping = {"1": "Tier 1", "2": "Tier 2"}

        plan = Plan.query.filter_by(
            tier=tier_mapping[tier], plan_type=PlanType.LTD, is_active=True
        ).first()

        if not plan:
            logger.error(f"No active LTD plan found for tier {tier}")
            click.echo(f"❌ Error: No active LTD plan found for tier {tier}", err=True)
            return

        logger.info(
            f"Found LTD plan: ID={plan.id}, Name={plan.name}, Credits={plan.credit_amount}"
        )
        click.echo(f"📦 LTD Plan Info:")
        click.echo(f"   ID: {plan.id}")
        click.echo(f"   Name: {plan.name}")
        click.echo(f"   Tier: {plan.tier}")
        click.echo(f"   Credits: {plan.credit_amount} per month")
        click.echo(f"   Description: {plan.description}")

        # 3. Check for existing LTD entitlements
        existing_ltd_entitlements = Entitlement.query.filter(
            Entitlement.user_id == user_id,
            Entitlement.source_type == EntitlementSource.LTD,
            Entitlement.valid_until > datetime.now(),
        ).all()

        if existing_ltd_entitlements:
            click.echo(
                f"⚠️  Warning: User already has {len(existing_ltd_entitlements)} active LTD entitlement(s):"
            )
            for ent in existing_ltd_entitlements:
                purchase = Purchase.get_by_id(ent.source_id)
                if purchase:
                    existing_plan = Plan.get_by_id(purchase.plan_id)
                    if existing_plan:
                        click.echo(
                            f"   - {existing_plan.tier} ({existing_plan.credit_amount} credits/month)"
                        )

        # 4. Generate stripe_payment_id
        timestamp = int(time.time())
        stripe_payment_id = f"admin_ltd_gift_{timestamp}_{user_id}"

        click.echo(f"💳 Purchase Info:")
        click.echo(f"   Stripe Payment ID: {stripe_payment_id}")
        click.echo(f"   Plan ID: {plan.id}")
        click.echo(f"   Quantity: 1")

        # 5. Show summary and ask for confirmation
        click.echo(f"\n📋 Summary:")
        click.echo(f"   User: {user.email} (ID: {user_id})")
        click.echo(f"   LTD Tier: {tier} ({plan.credit_amount} credits/month)")
        click.echo(f"   Plan: {plan.name}")
        click.echo(f"   Is AppSumo: False")

        if dry_run:
            click.echo(f"\n🔍 DRY RUN - No changes will be made")
            click.echo(
                f"✅ Would create Purchase record with stripe_payment_id: {stripe_payment_id}"
            )
            click.echo(
                f"✅ Would create LTD Entitlement with {plan.credit_amount} credits"
            )
            return

        # 6. Ask for confirmation
        click.echo(f"\n⚠️  This will create a new LTD entitlement for the user.")
        if not click.confirm("Do you want to proceed?", default=False):
            click.echo("❌ Operation cancelled")
            return

        # 7. Create records in database transaction
        _create_ltd_records(user_id, plan, stripe_payment_id)

        click.echo(f"✅ Successfully created LTD entitlement for user {user.email}")
        logger.info(
            f"Successfully created LTD entitlement for user {user_id}, tier {tier}"
        )

    except Exception as e:
        logger.error(f"Failed to create LTD entitlement: {str(e)}")
        click.echo(f"❌ Error: {str(e)}", err=True)
        raise click.ClickException(str(e))


@db_transaction()
def _create_ltd_records(user_id, plan, stripe_payment_id):
    """
    Create Purchase and Entitlement records in a database transaction.

    Args:
        user_id: User ID
        plan: Plan object
        stripe_payment_id: Generated stripe payment ID
    """
    # 1. Create Purchase record
    purchase = Purchase(
        id=id_generator.get_id(),
        user_id=user_id,
        plan_id=plan.id,
        quantity=1,
        stripe_payment_id=stripe_payment_id,
    )
    db.session.add(purchase)
    db.session.flush()  # Ensure Purchase ID is generated

    logger.info(
        f"Created Purchase record: ID={purchase.id}, stripe_payment_id={stripe_payment_id}"
    )

    # 2. Create Entitlement using EntitlementService
    entitlement = EntitlementService.create_from_ltd_purchase(purchase)
    db.session.add(entitlement)
    db.session.flush()  # Ensure Entitlement ID is generated

    logger.info(
        f"Created Entitlement record: ID={entitlement.id}, credits={entitlement.total_credits}, valid_until={entitlement.valid_until}"
    )

    # 3. Log the successful creation
    click.echo(f"✅ Purchase created: ID={purchase.id}")
    click.echo(f"✅ Entitlement created: ID={entitlement.id}")
    click.echo(f"   Credits: {entitlement.total_credits}")
    click.echo(f"   Valid until: {entitlement.valid_until}")
    click.echo(f"   Is recurring: {entitlement.is_recurring}")
