import logging
import click
from flask.cli import with_appcontext
from sqlalchemy import text

from models import db
from models.plan import Plan, PlanType

logger = logging.getLogger(__name__)


@click.command("add-ltd-plan-type")
@click.option("--confirm", is_flag=True, help="确认执行迁移")
@with_appcontext
def add_ltd_plan_type(confirm):
    """添加 LTD 计划类型并创建 AppSumo LTD 计划"""
    if not confirm:
        click.echo("请使用 --confirm 参数确认执行此迁移")
        return

    try:
        # 1. 修改 plan_type 枚举类型
        db.session.execute(
            text(
                """
        ALTER TABLE plan
        MODIFY COLUMN plan_type ENUM('subscription', 'one_time', 'free', 'ltd')
        COMMENT '套餐类型: subscription/one_time/free/ltd';
        """
            )
        )

        # 2. 修改 entitlement_source 枚举类型
        db.session.execute(
            text(
                """
        ALTER TABLE entitlements
        MODIFY COLUMN source_type ENUM('subscription', 'one_time', 'free_plan', 'bonus', 'referral', 'ltd')
        COMMENT '来源类型';
        """
            )
        )

        # 3. 创建 AppSumo LTD 计划
        tier1_plan = Plan.query.filter_by(name="AppSumo LTD Tier 1").first()
        if not tier1_plan:
            tier1_plan = Plan(
                id=9,
                name="appsumo_tier_1",
                tier="Tier 1",
                plan_type=PlanType.LTD,
                credit_amount=1200,  # 每月 1200 分钟
                interval="month",
                currency="USD",
                stripe_price_id=f"fake_stripe_price_id_tier_1",
                validity_days=0,  # 不使用有效期天数
                max_quantity=1,
                description="AppSumo Lifetime Deal - Tier 1 (1200 minutes/month)",
                amount=0,  # 不使用价格
            )
            db.session.add(tier1_plan)
            click.echo(f"创建了 AppSumo LTD Tier 1 计划，ID: {tier1_plan.id}")

        tier2_plan = Plan.query.filter_by(name="AppSumo LTD Tier 2").first()
        if not tier2_plan:
            tier2_plan = Plan(
                id=10,
                name="appsumo_tier_2",
                tier="Tier 2",
                plan_type=PlanType.LTD,
                credit_amount=6000,  # 每月 6000 分钟
                interval="month",
                currency="USD",
                stripe_price_id="fake_stripe_price_id_tier_2",
                validity_days=0,  # 不使用有效期天数
                max_quantity=1,
                description="AppSumo Lifetime Deal - Tier 2 (6000 minutes/month)",
                amount=0,  # 不使用价格
            )
            db.session.add(tier2_plan)
            click.echo(f"创建了 AppSumo LTD Tier 2 计划，ID: {tier2_plan.id}")
        db.session.commit()

        # 6. 输出配置信息
        click.echo("\n请将以下配置添加到 config.py 中的 APPSUMO_PLAN_MAPPING：")
        click.echo(
            f'    "tier1": {tier1_plan.id},  # AppSumo tier1 对应 ID 为 {tier1_plan.id} 的 LTD 计划'
        )
        click.echo(
            f'    "tier2": {tier2_plan.id},  # AppSumo tier2 对应 ID 为 {tier2_plan.id} 的 LTD 计划'
        )

    except Exception as e:
        db.session.rollback()
        logger.exception(f"迁移失败: {str(e)}")
        click.echo(f"迁移失败: {str(e)}", err=True)
        raise
