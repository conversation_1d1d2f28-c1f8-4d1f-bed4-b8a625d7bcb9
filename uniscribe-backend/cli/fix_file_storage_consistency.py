#!/usr/bin/env python3
"""
修正 file_storage 数据一致性脚本

以 transcription_file 为准，修正 file_storage 的 reference_count 和 state
"""

import click
import logging
from datetime import datetime
from collections import defaultdict

from app import app
from models import db_transaction, db
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from constants.file_storage import FileState

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def analyze_inconsistencies():
    """分析数据不一致情况"""
    logger.info("开始分析数据不一致情况...")

    # 统计信息
    stats = {
        "total_file_storage": 0,
        "total_transcription_files": 0,
        "inconsistent_count": 0,
        "missing_file_storage": 0,
        "orphaned_file_storage": 0,
        "wrong_reference_count": 0,
        "wrong_state": 0,
    }

    # 1. 统计总数
    stats["total_file_storage"] = FileStorage.query.count()
    stats["total_transcription_files"] = TranscriptionFile.query.filter(
        TranscriptionFile.fingerprint.isnot(None),
        TranscriptionFile.uploaded_time.isnot(None),
    ).count()

    logger.info(f"总 FileStorage 记录: {stats['total_file_storage']}")
    logger.info(
        f"总 TranscriptionFile 记录 (上传成功): {stats['total_transcription_files']}"
    )

    # 2. 检查 TranscriptionFile 缺失对应的 FileStorage
    transcription_files = TranscriptionFile.query.filter(
        TranscriptionFile.fingerprint.isnot(None),
        TranscriptionFile.uploaded_time.isnot(None),
    ).all()

    user_fingerprint_refs = defaultdict(lambda: {"active": 0, "deleted": 0})

    for tf in transcription_files:
        key = (tf.user_id, tf.fingerprint)
        if tf.is_deleted:
            user_fingerprint_refs[key]["deleted"] += 1
        else:
            user_fingerprint_refs[key]["active"] += 1

    logger.info(
        f"发现 {len(user_fingerprint_refs)} 个唯一的 (user_id, fingerprint) 组合"
    )

    # 3. 检查每个组合的 FileStorage 状态
    for (user_id, fingerprint), refs in user_fingerprint_refs.items():
        fs = FileStorage.query.filter_by(
            user_id=user_id, fingerprint=fingerprint
        ).first()

        expected_active_refs = refs["active"]

        # 确定期望的状态
        if expected_active_refs > 0:
            expected_state = FileState.ACTIVE.value
        else:
            # 如果没有活跃引用，检查当前状态
            # 如果已经是 DELETED 状态，说明文件已被物理删除，保持不变
            if fs and fs.state == FileState.DELETED.value:
                expected_state = FileState.DELETED.value  # 保持已删除状态
            else:
                expected_state = FileState.PENDING_DELETION.value

        if not fs:
            if expected_active_refs > 0:
                stats["missing_file_storage"] += 1
                logger.warning(
                    f"缺失 FileStorage (有活跃引用): user_id={user_id}, fingerprint={fingerprint}, active_refs={expected_active_refs}"
                )
            else:
                # 这些是已删除文件的记录，不需要创建 FileStorage
                logger.debug(
                    f"跳过已删除文件: user_id={user_id}, fingerprint={fingerprint}, deleted_refs={refs['deleted']}"
                )
        else:
            # 检查引用计数
            if fs.reference_count != expected_active_refs:
                stats["wrong_reference_count"] += 1
                logger.warning(
                    f"引用计数错误: user_id={user_id}, fingerprint={fingerprint}, stored={fs.reference_count}, actual={expected_active_refs}"
                )

            # 检查状态
            if fs.state != expected_state:
                stats["wrong_state"] += 1
                logger.warning(
                    f"状态错误: user_id={user_id}, fingerprint={fingerprint}, stored_state={fs.state}, expected_state={expected_state}"
                )

            if fs.reference_count != expected_active_refs or fs.state != expected_state:
                stats["inconsistent_count"] += 1

    # 4. 检查孤立的 FileStorage (没有对应的 TranscriptionFile)
    all_file_storage = FileStorage.query.all()
    for fs in all_file_storage:
        if fs.fingerprint:  # 跳过没有 fingerprint 的记录
            tf_count = (
                TranscriptionFile.query.filter_by(
                    user_id=fs.user_id, fingerprint=fs.fingerprint
                )
                .filter(TranscriptionFile.uploaded_time.isnot(None))
                .count()
            )
            if tf_count == 0:
                stats["orphaned_file_storage"] += 1
                logger.warning(
                    f"孤立的 FileStorage: id={fs.id}, user_id={fs.user_id}, fingerprint={fs.fingerprint}"
                )

    logger.info("=== 分析结果 ===")
    for key, value in stats.items():
        logger.info(f"{key}: {value}")

    return stats


def fix_inconsistencies(dry_run=True, batch_size=1000):
    """修正数据不一致"""
    logger.info(f"开始修正数据不一致 (dry_run={dry_run}, batch_size={batch_size})...")

    stats = {
        "processed": 0,
        "created": 0,
        "updated": 0,
        "deleted": 0,
        "errors": 0,
        "batches_completed": 0,
    }

    # 1. 分批查询 TranscriptionFile，避免内存溢出
    logger.info("开始分批查询 TranscriptionFile...")

    total_count = TranscriptionFile.query.filter(
        TranscriptionFile.fingerprint.isnot(None),
        TranscriptionFile.uploaded_time.isnot(None),
    ).count()
    logger.info(f"总共需要处理 {total_count} 个 TranscriptionFile 记录")

    user_fingerprint_refs = defaultdict(
        lambda: {"active": 0, "deleted": 0, "files": []}
    )

    # 分批查询 TranscriptionFile
    for offset in range(0, total_count, batch_size):
        batch_files = (
            TranscriptionFile.query.filter(
                TranscriptionFile.fingerprint.isnot(None),
                TranscriptionFile.uploaded_time.isnot(None),
            )
            .offset(offset)
            .limit(batch_size)
            .all()
        )

        logger.info(
            f"处理 TranscriptionFile 批次: {offset + 1}-{min(offset + batch_size, total_count)}/{total_count}"
        )

        for tf in batch_files:
            key = (tf.user_id, tf.fingerprint)
            user_fingerprint_refs[key]["files"].append(tf)
            if tf.is_deleted:
                user_fingerprint_refs[key]["deleted"] += 1
            else:
                user_fingerprint_refs[key]["active"] += 1

    logger.info(
        f"汇总完成，需要处理 {len(user_fingerprint_refs)} 个唯一的 (user_id, fingerprint) 组合"
    )

    # 2. 分批处理 (user_id, fingerprint) 组合
    combinations = list(user_fingerprint_refs.items())
    total_combinations = len(combinations)

    logger.info(f"开始分批处理 {total_combinations} 个 (user_id, fingerprint) 组合")

    for batch_start in range(0, total_combinations, batch_size):
        batch_end = min(batch_start + batch_size, total_combinations)
        batch_combinations = combinations[batch_start:batch_end]

        logger.info(f"处理组合批次: {batch_start + 1}-{batch_end}/{total_combinations}")

        # 在每个批次内处理
        batch_errors = 0
        for (user_id, fingerprint), refs in batch_combinations:
            try:
                stats["processed"] += 1

                expected_active_refs = refs["active"]

                # 确定期望的状态
                if expected_active_refs > 0:
                    expected_state = FileState.ACTIVE.value
                else:
                    # 如果没有活跃引用，检查当前状态
                    # 如果已经是 DELETED 状态，说明文件已被物理删除，保持不变
                    fs_current = FileStorage.query.filter_by(
                        user_id=user_id, fingerprint=fingerprint
                    ).first()

                    if fs_current and fs_current.state == FileState.DELETED.value:
                        expected_state = FileState.DELETED.value  # 保持已删除状态
                    else:
                        expected_state = FileState.PENDING_DELETION.value

                # 获取一个示例文件来获取文件信息
                sample_file = refs["files"][0]

                fs = FileStorage.query.filter_by(
                    user_id=user_id, fingerprint=fingerprint
                ).first()

                if not fs:
                    # 创建缺失的 FileStorage 记录
                    if expected_active_refs > 0:  # 只为有活跃引用的创建记录
                        logger.info(
                            f"创建 FileStorage: user_id={user_id}, fingerprint={fingerprint}, refs={expected_active_refs}"
                        )
                        if not dry_run:
                            with db_transaction():
                                fs = FileStorage(
                                    user_id=user_id,
                                    fingerprint=fingerprint,
                                    file_type=sample_file.file_type or "unknown",
                                    file_key=sample_file.file_key
                                    or f"{user_id}-{fingerprint}",
                                    file_size=sample_file.file_size or 0,
                                    reference_count=expected_active_refs,
                                    state=expected_state,
                                    created_time=datetime.now(),
                                    updated_time=datetime.now(),
                                    last_access_time=datetime.now(),
                                )
                                db.session.add(fs)
                        stats["created"] += 1
                else:
                    # 更新现有的 FileStorage 记录
                    needs_update = False
                    changes = []

                    if fs.reference_count != expected_active_refs:
                        changes.append(
                            f"reference_count: {fs.reference_count} -> {expected_active_refs}"
                        )
                        needs_update = True

                    if fs.state != expected_state:
                        changes.append(f"state: {fs.state} -> {expected_state}")
                        needs_update = True

                    if needs_update:
                        logger.info(
                            f"更新 FileStorage id={fs.id}: {', '.join(changes)}"
                        )
                        if not dry_run:
                            with db_transaction():
                                fs.reference_count = expected_active_refs
                                fs.state = expected_state
                                fs.updated_time = datetime.now()
                        stats["updated"] += 1

                if stats["processed"] % 100 == 0:
                    logger.info(f"已处理 {stats['processed']} 个组合...")

            except Exception as e:
                logger.error(
                    f"处理 user_id={user_id}, fingerprint={fingerprint} 时出错: {str(e)}"
                )
                stats["errors"] += 1
                batch_errors += 1

        stats["batches_completed"] += 1
        logger.info(f"批次 {stats['batches_completed']} 完成，错误数: {batch_errors}")

    # 2. 处理孤立的 FileStorage 记录
    logger.info("处理孤立的 FileStorage 记录...")
    all_file_storage = FileStorage.query.all()

    for fs in all_file_storage:
        if fs.fingerprint:  # 跳过没有 fingerprint 的记录
            tf_count = (
                TranscriptionFile.query.filter_by(
                    user_id=fs.user_id, fingerprint=fs.fingerprint
                )
                .filter(TranscriptionFile.uploaded_time.isnot(None))
                .count()
            )

            if tf_count == 0:
                logger.info(
                    f"删除孤立的 FileStorage: id={fs.id}, user_id={fs.user_id}, fingerprint={fs.fingerprint}"
                )
                if not dry_run:
                    with db_transaction():
                        db.session.delete(fs)
                stats["deleted"] += 1

    logger.info("=== 修正结果 ===")
    for key, value in stats.items():
        logger.info(f"{key}: {value}")

    return stats


def backup_file_storage_table():
    """备份 file_storage 表数据"""
    logger.info("创建 file_storage 表备份...")

    backup_table_name = (
        f"file_storage_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )

    try:
        # 创建备份表
        db.session.execute(
            f"""
            CREATE TABLE {backup_table_name} AS
            SELECT * FROM file_storage
        """
        )
        db.session.commit()

        backup_count = db.session.execute(
            f"SELECT COUNT(*) FROM {backup_table_name}"
        ).scalar()
        logger.info(f"备份完成: {backup_table_name}, 记录数: {backup_count}")
        return backup_table_name

    except Exception as e:
        logger.error(f"创建备份失败: {str(e)}")
        raise


@click.command()
@click.option("--analyze", is_flag=True, default=False, help="只分析数据不一致情况")
@click.option("--fix", is_flag=True, default=False, help="执行修正操作（绕过分析）")
@click.option(
    "--dry-run", is_flag=True, default=False, help="预览修正操作（不实际修改）"
)
@click.option("--backup", is_flag=True, default=False, help="修正前创建备份")
@click.option("--batch-size", default=1000, help="批处理大小")
@click.option("--no-confirm", is_flag=True, default=False, help="跳过确认提示")
def main(analyze, fix, dry_run, backup, batch_size, no_confirm):
    """修正 file_storage 数据一致性

    使用示例:

    # 只分析数据不一致情况
    python -m cli.fix_file_storage_consistency --analyze

    # 预览修正操作（不实际修改）
    python -m cli.fix_file_storage_consistency --dry-run

    # 执行修正操作（带备份）
    python -m cli.fix_file_storage_consistency --fix --backup
    """

    # 参数验证
    if sum([analyze, fix, dry_run]) == 0:
        logger.error("必须指定操作模式: --analyze, --fix, 或 --dry-run")
        return

    if sum([analyze, fix, dry_run]) > 1:
        logger.error("只能指定一个操作模式")
        return

    with app.app_context():
        logger.info("=" * 60)
        logger.info("file_storage 数据一致性修正工具")
        logger.info("=" * 60)

        if analyze:
            logger.info("模式: 分析模式")
        elif dry_run:
            logger.info("模式: 预览模式")
        elif fix:
            logger.info("模式: 修正模式")

        logger.info(f"批处理大小: {batch_size}")

        # 只有在实际修正模式下才考虑备份
        if backup and fix:
            if not no_confirm and not click.confirm("是否创建 file_storage 表备份？"):
                logger.warning("跳过备份，继续执行...")
            else:
                backup_table_name = backup_file_storage_table()
                logger.info(f"备份表名: {backup_table_name}")

        try:
            if analyze:
                # 只分析模式
                logger.info("\n" + "=" * 40)
                logger.info("分析数据不一致情况")
                logger.info("=" * 40)
                analyze_stats = analyze_inconsistencies()

                logger.info("\n" + "=" * 60)
                logger.info("分析完成！")
                logger.info("要执行修正，请使用 --fix 或 --dry-run 参数")
                logger.info("=" * 60)

            elif dry_run or fix:
                # 修正模式（预览或实际执行）
                logger.info("\n" + "=" * 40)
                logger.info("执行数据修正")
                logger.info("=" * 40)

                if fix:
                    # 实际修正前的最终确认
                    if not no_confirm:
                        logger.warning("即将执行实际的数据修正操作")
                        if not click.confirm("确认执行修正操作？"):
                            logger.info("操作已取消")
                            return

                fix_stats = fix_inconsistencies(dry_run=dry_run, batch_size=batch_size)

                if dry_run:
                    logger.info("\n" + "=" * 60)
                    logger.info("这是预览模式，没有实际修改数据。")
                    logger.info("要执行实际修正，请使用 --fix 参数")
                    logger.info("建议先使用 --backup 参数创建备份")
                    logger.info("=" * 60)
                else:
                    logger.info("\n" + "=" * 60)
                    logger.info("数据修正完成！")
                    logger.info("建议运行 --analyze 验证修正结果")
                    logger.info("=" * 60)

        except Exception as e:
            logger.error(f"执行过程中出错: {str(e)}")
            raise
        finally:
            # 清理数据库会话
            db.session.remove()


if __name__ == "__main__":
    main()
