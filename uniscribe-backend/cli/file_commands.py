"""
文件管理相关的 CLI 命令
"""

import logging
import click
from flask import current_app
from flask.cli import with_appcontext

from models.transcription_file import TranscriptionFile
from controllers.transcription import verify_file_integrity
from constants.transcription import TranscriptionFileStatus

logger = logging.getLogger(__name__)


@click.command("check-file-upload")
@click.option("--file-id", type=int, required=True, help="要检查的文件ID")
@click.option("--verbose", is_flag=True, help="显示详细信息")
@with_appcontext
def check_file_upload(file_id, verbose):
    """
    检查指定文件ID是否完成了上传

    该命令会：
    1. 查找指定的转录文件
    2. 检查文件状态
    3. 验证文件在存储中的完整性
    4. 显示检查结果

    示例:
    flask admin check-file-upload --file-id=12345
    flask admin check-file-upload --file-id=12345 --verbose
    """
    try:
        logger.info("开始检查文件上传状态: file_id=%s", file_id)

        # 1. 查找文件（包括已删除的文件）
        transcription_file = TranscriptionFile.query.filter_by(id=file_id).first()
        if not transcription_file:
            click.echo(f"❌ 文件不存在 (ID: {file_id})")
            return

        # 2. 显示文件基本信息
        click.echo(f"\n📁 文件信息 (ID: {file_id}):")
        click.echo(f"  📄 文件名: {transcription_file.filename}")
        click.echo(f"  📊 文件大小: {transcription_file.file_size:,} bytes")
        click.echo(f"  🔗 文件类型: {transcription_file.file_type}")
        click.echo(f"  🔑 文件键: {transcription_file.file_key}")
        click.echo(f"  👤 用户ID: {transcription_file.user_id}")

        # 显示删除状态
        if transcription_file.is_deleted:
            click.echo(f"  🗑️  删除状态: 已删除")
        else:
            click.echo(f"  ✅ 删除状态: 正常")

        # 3. 检查文件状态
        status_name = _get_status_name(transcription_file.status)
        status_emoji = _get_status_emoji(transcription_file.status)
        click.echo(
            f"  {status_emoji} 状态: {status_name} ({transcription_file.status})"
        )

        if verbose:
            click.echo(f"  🕒 创建时间: {transcription_file.created_time}")
            click.echo(f"  🔄 更新时间: {transcription_file.updated_time}")
            if transcription_file.uploaded_time:
                click.echo(f"  ⬆️  上传时间: {transcription_file.uploaded_time}")
            click.echo(f"  🔍 指纹: {transcription_file.fingerprint}")
            click.echo(f"  ⏱️  时长: {transcription_file.duration} 秒")
            if transcription_file.language:
                click.echo(f"  🌐 语言: {transcription_file.language}")

        # 4. 检查删除状态
        if transcription_file.is_deleted:
            click.echo(f"\n🗑️  文件已被删除")
            if verbose:
                click.echo(
                    "💡 提示: 已删除的文件可能仍存在于存储中，但不会被正常业务逻辑访问"
                )

        # 5. 显示文件状态信息
        if transcription_file.status == TranscriptionFileStatus.uploading.id:
            click.echo(f"\n⚠️  文件状态为上传中")
        elif transcription_file.status == TranscriptionFileStatus.uploaded.id:
            click.echo(f"\n✅ 文件已完成上传")
        else:
            click.echo(f"\n📋 文件状态: {status_name}")
            if transcription_file.status == TranscriptionFileStatus.failed.id:
                click.echo("❌ 文件处理失败状态")

        # 6. 对所有文件进行存储存在性检查（调试模式）
        if transcription_file.file_key:
            click.echo(f"\n🔍 开始检查文件存在性...")
            try:
                storage = current_app.storage
                is_valid = verify_file_integrity(
                    storage, transcription_file, transcription_file.file_key
                )

                if is_valid:
                    click.echo("✅ 文件在存储中存在")
                    if (
                        transcription_file.status
                        == TranscriptionFileStatus.uploading.id
                    ):
                        click.echo(
                            "💡 建议: 文件已上传完成，但状态未更新。可能需要手动更新状态。"
                        )
                else:
                    click.echo("❌ 文件在存储中不存在")

            except Exception as e:
                click.echo(f"❌ 文件存在性检查出错: {str(e)}")
                if verbose:
                    logger.exception("文件存在性检查异常")
        else:
            click.echo(f"\n⚠️  文件没有 file_key，无法检查存储存在性")

        # 7. 总结
        click.echo(f"\n📊 检查结果总结:")
        if transcription_file.is_deleted:
            click.echo("🗑️  文件已被删除")
        elif transcription_file.status == TranscriptionFileStatus.uploaded.id:
            click.echo("✅ 文件上传已完成")
        elif transcription_file.status == TranscriptionFileStatus.uploading.id:
            click.echo("⚠️  文件仍在上传中或状态未更新")
        else:
            click.echo(f"ℹ️  文件当前状态: {status_name}")

        if transcription_file.file_key:
            click.echo("🔍 已执行存储存在性检查")
        else:
            click.echo("⚠️  无法检查存储存在性（缺少 file_key）")

    except Exception as e:
        logger.error("检查文件上传状态失败: %s", str(e), exc_info=True)
        click.echo(f"❌ 操作失败: {str(e)}")


def _get_status_name(status_id):
    """获取状态名称"""
    status_map = {
        TranscriptionFileStatus.uploading.id: "上传中",
        TranscriptionFileStatus.uploaded.id: "已上传",
        TranscriptionFileStatus.preprocessing.id: "媒体预处理中",
        TranscriptionFileStatus.preprocessing_failed.id: "媒体预处理失败",
        TranscriptionFileStatus.processing.id: "处理中",
        TranscriptionFileStatus.partially_completed.id: "部分完成",
        TranscriptionFileStatus.completed.id: "已完成",
        TranscriptionFileStatus.failed.id: "失败",
        TranscriptionFileStatus.completed_with_errors.id: "完成但有错误",
    }
    return status_map.get(status_id, f"未知状态({status_id})")


def _get_status_emoji(status_id):
    """获取状态对应的emoji"""
    emoji_map = {
        TranscriptionFileStatus.uploading.id: "⬆️",
        TranscriptionFileStatus.uploaded.id: "✅",
        TranscriptionFileStatus.preprocessing.id: "🎬",  # 媒体预处理中
        TranscriptionFileStatus.preprocessing_failed.id: "🚫",  # 媒体预处理失败
        TranscriptionFileStatus.processing.id: "⚙️",
        TranscriptionFileStatus.partially_completed.id: "🔄",
        TranscriptionFileStatus.completed.id: "🎉",
        TranscriptionFileStatus.failed.id: "❌",
        TranscriptionFileStatus.completed_with_errors.id: "⚠️",
    }
    return emoji_map.get(status_id, "❓")


# 注册命令到 admin_cli
from . import admin_cli

admin_cli.add_command(check_file_upload)
