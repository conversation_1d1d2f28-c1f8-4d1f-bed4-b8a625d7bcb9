#!/usr/bin/env python3
"""
说话人对齐任务管理器

用法:
python cli/alignment_manager.py status                    # 查看队列状态
python cli/alignment_manager.py retrigger --file-id 123   # 重新触发单个文件
python cli/alignment_manager.py batch --file-ids 123,124,125  # 批量重新触发
python cli/alignment_manager.py list --no-alignment       # 列出没有对齐结果的文件
python cli/alignment_manager.py clear-queue               # 清空队列
"""

import sys
import os
import argparse
from typing import List

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models.task_result import TaskResult
from services.alignment_queue_service import AlignmentQueueService
from sqlalchemy.orm.attributes import flag_modified
from models import db


def show_queue_status():
    """显示队列状态"""
    print("📊 对齐队列状态:")

    try:
        # 这里可以添加队列状态检查的逻辑
        # 由于AlignmentQueueService没有提供队列状态方法，我们先显示基本信息
        print("   队列服务: 运行中")
        print("   注意: 请确保对齐worker正在运行")
        print("   启动命令: python workers/alignment_worker.py")
    except Exception as e:
        print(f"   ❌ 无法获取队列状态: {e}")


def list_files_without_alignment():
    """列出没有对齐结果的文件"""
    with app.app_context():
        # 查找有转录和说话人识别结果但没有对齐结果的文件
        results = TaskResult.query.filter(
            TaskResult.segments.isnot(None),
            TaskResult.diarization_segments.isnot(None),
            TaskResult.is_aligned == False,
        ).all()

        if not results:
            print("✅ 所有文件都已完成对齐")
            return []

        print(f"📋 找到 {len(results)} 个需要对齐的文件:")
        file_ids = []

        for result in results:
            file_ids.append(result.file_id)
            segments_count = len(result.segments) if result.segments else 0
            dia_segments_count = (
                len(result.diarization_segments.get("segments", []))
                if result.diarization_segments
                else 0
            )

            # 检测类型
            from workers.alignment_worker import AlignmentWorker

            is_word_level = (
                AlignmentWorker._is_word_level_segments(result.segments)
                if result.segments
                else False
            )
            segment_type = "词级别" if is_word_level else "句子级别"

            print(
                f"   文件 {result.file_id}: {segments_count} 转录段, {dia_segments_count} 说话人段 ({segment_type})"
            )

        return file_ids


def retrigger_single_file(file_id: int, force: bool = False):
    """重新触发单个文件的对齐任务"""
    from cli.retrigger_alignment import retrigger_alignment

    return retrigger_alignment(file_id, force)


def retrigger_batch_files(file_ids: List[int], force: bool = False):
    """批量重新触发对齐任务"""
    print(f"🚀 开始批量处理 {len(file_ids)} 个文件...")

    success_count = 0
    failed_count = 0

    for file_id in file_ids:
        print(f"\n处理文件 {file_id}:")
        try:
            if retrigger_single_file(file_id, force):
                success_count += 1
                print(f"   ✅ 文件 {file_id} 处理成功")
            else:
                failed_count += 1
                print(f"   ❌ 文件 {file_id} 处理失败")
        except Exception as e:
            failed_count += 1
            print(f"   ❌ 文件 {file_id} 处理异常: {e}")

    print(f"\n📊 批量处理完成:")
    print(f"   成功: {success_count}")
    print(f"   失败: {failed_count}")
    print(f"   总计: {len(file_ids)}")


def clear_queue():
    """清空队列（谨慎使用）"""
    print("⚠️  此操作将清空对齐队列中的所有任务")
    confirm = input("确认要继续吗? (yes/no): ")

    if confirm.lower() != "yes":
        print("操作已取消")
        return

    try:
        # 这里需要实现清空队列的逻辑
        # 由于AlignmentQueueService没有提供清空方法，我们先提示
        print("❌ 清空队列功能尚未实现")
        print("如需清空队列，请手动操作Redis或重启Redis服务")
    except Exception as e:
        print(f"❌ 清空队列失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="说话人对齐任务管理器")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # status 命令
    subparsers.add_parser("status", help="查看队列状态")

    # retrigger 命令
    retrigger_parser = subparsers.add_parser("retrigger", help="重新触发单个文件")
    retrigger_parser.add_argument("--file-id", type=int, required=True, help="文件ID")
    retrigger_parser.add_argument("--force", action="store_true", help="强制重新对齐")

    # batch 命令
    batch_parser = subparsers.add_parser("batch", help="批量重新触发")
    batch_parser.add_argument(
        "--file-ids", type=str, required=True, help="文件ID列表，用逗号分隔"
    )
    batch_parser.add_argument("--force", action="store_true", help="强制重新对齐")

    # list 命令
    list_parser = subparsers.add_parser("list", help="列出文件")
    list_parser.add_argument(
        "--no-alignment", action="store_true", help="列出没有对齐结果的文件"
    )
    list_parser.add_argument(
        "--auto-retrigger", action="store_true", help="自动重新触发找到的文件"
    )

    # clear-queue 命令
    subparsers.add_parser("clear-queue", help="清空队列")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    if args.command == "status":
        show_queue_status()

    elif args.command == "retrigger":
        retrigger_single_file(args.file_id, args.force)

    elif args.command == "batch":
        file_ids = [int(x.strip()) for x in args.file_ids.split(",")]
        retrigger_batch_files(file_ids, args.force)

    elif args.command == "list":
        if args.no_alignment:
            file_ids = list_files_without_alignment()
            if args.auto_retrigger and file_ids:
                print(f"\n🚀 自动重新触发 {len(file_ids)} 个文件...")
                retrigger_batch_files(file_ids, force=False)

    elif args.command == "clear-queue":
        clear_queue()


if __name__ == "__main__":
    main()
