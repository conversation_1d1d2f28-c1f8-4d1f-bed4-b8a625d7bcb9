#!/usr/bin/env python3
"""
重新触发说话人对齐任务

用法:
python cli/retrigger_alignment.py --file-id 123
python cli/retrigger_alignment.py --file-id 123 --force  # 强制重新对齐，即使已有结果
"""

import sys
import os
import argparse

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models.task_result import TaskResult
from services.alignment_queue_service import AlignmentQueueService
from sqlalchemy.orm.attributes import flag_modified
from models import db


def retrigger_alignment(file_id: int, force: bool = False):
    """重新触发对齐任务

    Args:
        file_id: 文件ID
        force: 是否强制重新对齐（即使已有结果）
    """
    with app.app_context():
        # 查找任务结果，强制刷新以获取最新数据
        task_result = TaskResult.get_by_file_id(file_id, refresh=True)
        if not task_result:
            print(f"❌ 未找到文件 {file_id} 的任务结果")
            return False

        # 检查必要数据是否存在
        if not task_result.segments:
            print(f"❌ 文件 {file_id} 没有转录结果，无法进行对齐")
            return False

        if not task_result.diarization_segments:
            print(f"❌ 文件 {file_id} 没有说话人识别结果，无法进行对齐")
            return False

        # 检查是否已有对齐结果
        if task_result.is_aligned and not force:
            print(f"⚠️  文件 {file_id} 已有对齐结果")
            print("如果要强制重新对齐，请使用 --force 参数")
            return False

        # 清除现有对齐结果（如果强制重新对齐）
        if force and task_result.is_aligned:
            print(f"🔄 重置文件 {file_id} 的对齐状态")
            # 从 original_segments 恢复原始的 segments
            if task_result.original_segments:
                task_result.segments = task_result.original_segments
                task_result.original_segments = None
                flag_modified(task_result, "segments")
                flag_modified(task_result, "original_segments")
            task_result.is_aligned = False
            flag_modified(task_result, "is_aligned")
            db.session.commit()

        # 显示任务信息
        print(f"📊 文件 {file_id} 信息:")
        print(f"   转录段数: {len(task_result.segments)}")
        print(
            f"   说话人识别段数: {len(task_result.diarization_segments.get('segments', []))}"
        )

        # 检测是否为词级别
        from workers.alignment_worker import AlignmentWorker

        is_word_level = AlignmentWorker._is_word_level_segments(task_result.segments)
        print(f"   检测类型: {'词级别' if is_word_level else '句子级别'}")

        # 将任务加入对齐队列
        task_data = {
            "file_id": file_id,
            "transcription_task_id": f"retrigger_{file_id}",
            "diarization_task_id": f"retrigger_{file_id}",
        }

        message_id = AlignmentQueueService.enqueue_alignment_task(task_data)
        print(f"✅ 已将文件 {file_id} 的对齐任务加入队列")
        print(f"   消息ID: {message_id}")
        print(f"   请确保对齐worker正在运行: python workers/alignment_worker.py")

        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="重新触发说话人对齐任务")
    parser.add_argument("--file-id", type=int, required=True, help="文件ID")
    parser.add_argument(
        "--force", action="store_true", help="强制重新对齐，即使已有结果"
    )

    args = parser.parse_args()

    print(f"🚀 开始重新触发文件 {args.file_id} 的对齐任务...")

    success = retrigger_alignment(args.file_id, args.force)

    if success:
        print("✅ 对齐任务已成功加入队列")
    else:
        print("❌ 对齐任务触发失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
