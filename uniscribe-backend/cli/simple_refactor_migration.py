#!/usr/bin/env python3
"""
简化的 segments 数据结构重构迁移脚本
"""

import sys
import os
import json

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text

# 加载环境变量
try:
    from dotenv import load_dotenv

    load_dotenv()
except ImportError:
    # 如果没有 python-dotenv，尝试手动加载 .env 文件
    env_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))), ".env"
    )
    if os.path.exists(env_path):
        with open(env_path, "r") as f:
            for line in f:
                if "=" in line and not line.strip().startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value


def create_minimal_app():
    """创建最小化的 Flask 应用"""
    app = Flask(__name__)

    # 从环境变量加载数据库配置
    database_url = os.getenv("SQLALCHEMY_DATABASE_URI") or os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError(
            "SQLALCHEMY_DATABASE_URI or DATABASE_URL environment variable is required"
        )

    # 如果是 Docker 环境的配置，替换为本地连接
    if "mysql_db:3306" in database_url:
        database_url = database_url.replace("mysql_db:3306", "localhost:3307")
        print(
            f"  - 使用本地数据库连接: {database_url.replace('shiyin_developer', '***')}"
        )

    app.config["SQLALCHEMY_DATABASE_URI"] = database_url
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False

    db = SQLAlchemy(app)
    return app, db


def main():
    """主函数"""
    print("🚀 开始重构 segments 数据结构...")
    print("=" * 60)

    app, db = create_minimal_app()

    with app.app_context():
        try:
            # 1. 添加 original_segments 字段
            print("1. 添加 original_segments 字段...")

            # 检查字段是否已存在
            result = db.session.execute(
                text(
                    """
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'task_result' 
                    AND COLUMN_NAME = 'original_segments'
                """
                )
            )

            if result.fetchone():
                print("  - original_segments 字段已存在，跳过...")
            else:
                # 添加字段
                db.session.execute(
                    text(
                        """
                        ALTER TABLE task_result 
                        ADD COLUMN original_segments JSON NULL 
                        COMMENT '原始转录结果(用于调试)'
                    """
                    )
                )
                db.session.commit()
                print("  - 成功添加 original_segments 字段")

            # 2. 迁移现有数据
            print("2. 迁移现有数据...")

            # 获取所有有数据的记录
            result = db.session.execute(
                text(
                    "SELECT id, file_id, segments, aligned_segments FROM task_result WHERE segments IS NOT NULL"
                )
            )
            results = result.fetchall()

            print(f"  - 找到 {len(results)} 条需要迁移的记录")

            migrated_count = 0
            for row in results:
                try:
                    record_id, file_id, segments_json, aligned_segments_json = row

                    if segments_json:
                        # 如果有 aligned_segments，将其作为最终的 segments，原始 segments 保存到 original_segments
                        if aligned_segments_json:
                            db.session.execute(
                                text(
                                    "UPDATE task_result SET original_segments = :original, segments = :final WHERE id = :id"
                                ),
                                {
                                    "original": segments_json,
                                    "final": aligned_segments_json,
                                    "id": record_id,
                                },
                            )
                            print(
                                f"    - 文件 {file_id}: 使用 aligned_segments 作为最终结果"
                            )
                        else:
                            # 没有 aligned_segments，直接保存原始 segments 到 original_segments
                            db.session.execute(
                                text(
                                    "UPDATE task_result SET original_segments = :original WHERE id = :id"
                                ),
                                {"original": segments_json, "id": record_id},
                            )
                            print(f"    - 文件 {file_id}: 保持原有 segments")

                        migrated_count += 1

                except Exception as e:
                    print(f"    - 迁移文件 {file_id} 失败: {e}")
                    continue

            db.session.commit()
            print(f"  - 成功迁移 {migrated_count} 条记录")

            # 3. 删除 aligned_segments 字段
            print("3. 删除 aligned_segments 字段...")

            # 检查字段是否存在
            result = db.session.execute(
                text(
                    """
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'task_result' 
                    AND COLUMN_NAME = 'aligned_segments'
                """
                )
            )

            if not result.fetchone():
                print("  - aligned_segments 字段不存在，跳过...")
            else:
                # 删除字段
                db.session.execute(
                    text(
                        """
                        ALTER TABLE task_result 
                        DROP COLUMN aligned_segments
                    """
                    )
                )
                db.session.commit()
                print("  - 成功删除 aligned_segments 字段")

            # 4. 验证迁移结果
            print("4. 验证迁移结果...")

            # 检查字段结构
            result = db.session.execute(
                text(
                    """
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'task_result' 
                    AND COLUMN_NAME IN ('segments', 'original_segments', 'diarization_segments')
                    ORDER BY COLUMN_NAME
                """
                )
            )

            fields = result.fetchall()
            print("  - 字段结构:")
            for field in fields:
                print(f"    - {field[0]}: {field[1]} ({field[2]}) - {field[3]}")

            # 检查数据完整性
            total_result = db.session.execute(text("SELECT COUNT(*) FROM task_result"))
            total_count = total_result.fetchone()[0]

            segments_result = db.session.execute(
                text("SELECT COUNT(*) FROM task_result WHERE segments IS NOT NULL")
            )
            segments_count = segments_result.fetchone()[0]

            original_segments_result = db.session.execute(
                text(
                    "SELECT COUNT(*) FROM task_result WHERE original_segments IS NOT NULL"
                )
            )
            original_segments_count = original_segments_result.fetchone()[0]

            print(f"  - 数据统计:")
            print(f"    - 总记录数: {total_count}")
            print(f"    - 有 segments 的记录: {segments_count}")
            print(f"    - 有 original_segments 的记录: {original_segments_count}")

            # 检查是否还有 aligned_segments 字段
            result = db.session.execute(
                text(
                    """
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'task_result' 
                    AND COLUMN_NAME = 'aligned_segments'
                """
                )
            )

            if result.fetchone():
                print("  - ❌ aligned_segments 字段仍然存在")
                return False
            else:
                print("  - ✅ aligned_segments 字段已成功删除")

            print("  - ✅ 迁移验证通过")

        except Exception as e:
            print(f"❌ 迁移失败: {e}")
            db.session.rollback()
            return False

    print("=" * 60)
    print("✅ 重构完成！")
    print("\n下一步:")
    print("1. 重启应用服务")
    print("2. 测试功能是否正常")
    return True


if __name__ == "__main__":
    if main():
        sys.exit(0)
    else:
        sys.exit(1)
