"""
测试生产环境文件下载表现的 CLI 命令
"""

import logging
import click
import time
import requests
from flask import current_app
from flask.cli import with_appcontext

from models.transcription_file import TranscriptionFile
from constants.transcription import TranscriptionFileStatus
from . import admin_cli

logger = logging.getLogger(__name__)


@click.command("test-download-performance")
@click.option("--user-id", type=int, required=True, help="用户ID")
@click.option("--file-id", type=int, help="指定文件ID进行测试")
@click.option("--limit", type=int, default=5, help="测试文件数量限制（默认5个）")
@click.option("--timeout", type=int, default=30, help="下载超时时间（秒，默认30）")
@click.option("--verbose", is_flag=True, help="显示详细信息")
@with_appcontext
def test_download_performance(user_id, file_id, limit, timeout, verbose):
    """
    测试生产环境的文件下载表现
    
    该命令会：
    1. 获取用户的文件列表（或指定文件）
    2. 生成预签名下载URL
    3. 测试下载速度和表现
    4. 将文件内容写入 /dev/null 避免占用磁盘空间
    
    示例:
    flask admin test-download-performance --user-id=12345
    flask admin test-download-performance --user-id=12345 --file-id=67890
    flask admin test-download-performance --user-id=12345 --limit=10 --verbose
    """
    try:
        logger.info("开始测试下载表现: user_id=%s", user_id)
        
        # 获取要测试的文件列表
        if file_id:
            # 测试指定文件
            transcription_file = TranscriptionFile.get_by_id(file_id)
            if not transcription_file:
                click.echo(f"❌ 文件不存在 (ID: {file_id})")
                return
            
            if transcription_file.user_id != user_id:
                click.echo(f"❌ 文件不属于指定用户 (文件ID: {file_id}, 用户ID: {user_id})")
                return
                
            test_files = [transcription_file]
            click.echo(f"🎯 测试指定文件: {transcription_file.filename}")
        else:
            # 获取用户的文件列表
            test_files = TranscriptionFile.query.filter_by(
                user_id=user_id, 
                is_deleted=False
            ).filter(
                TranscriptionFile.status.in_([
                    TranscriptionFileStatus.uploaded.id,
                    TranscriptionFileStatus.completed.id,
                    TranscriptionFileStatus.processing.id,
                    TranscriptionFileStatus.partially_completed.id,
                    TranscriptionFileStatus.completed_with_errors.id,
                ])
            ).order_by(TranscriptionFile.id.desc()).limit(limit).all()
            
            if not test_files:
                click.echo(f"❌ 用户 {user_id} 没有可测试的文件")
                return
                
            click.echo(f"📋 找到 {len(test_files)} 个文件进行测试")
        
        # 初始化存储服务
        storage = current_app.storage
        
        # 测试结果统计
        total_files = len(test_files)
        successful_downloads = 0
        failed_downloads = 0
        total_download_time = 0
        total_file_size = 0
        
        click.echo(f"\n🚀 开始下载测试...")
        click.echo(f"⏱️  超时设置: {timeout} 秒")
        click.echo(f"📁 输出目标: /dev/null")
        click.echo("-" * 60)
        
        for i, tf in enumerate(test_files, 1):
            click.echo(f"\n[{i}/{total_files}] 测试文件: {tf.filename}")
            click.echo(f"  📊 文件大小: {tf.file_size:,} bytes ({tf.file_size / 1024 / 1024:.2f} MB)")
            click.echo(f"  🔑 文件键: {tf.file_key}")
            
            if not tf.file_key:
                click.echo("  ⚠️  跳过: 文件没有 file_key")
                failed_downloads += 1
                continue
            
            try:
                # 生成预签名URL
                presigned_url = storage.generate_presigned_url_for_read(
                    tf.file_key, 
                    3600  # 1小时有效期
                )
                
                if verbose:
                    click.echo(f"  🔗 预签名URL: {presigned_url[:100]}...")
                
                # 开始下载测试
                start_time = time.time()
                
                # 使用 requests 下载文件到 /dev/null
                with requests.get(presigned_url, stream=True, timeout=timeout) as response:
                    response.raise_for_status()
                    
                    # 检查响应头
                    content_length = response.headers.get('content-length')
                    if content_length:
                        expected_size = int(content_length)
                        if verbose:
                            click.echo(f"  📏 响应头文件大小: {expected_size:,} bytes")
                    
                    # 将内容写入 /dev/null
                    downloaded_size = 0
                    with open('/dev/null', 'wb') as null_file:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                null_file.write(chunk)
                                downloaded_size += len(chunk)
                
                end_time = time.time()
                download_time = end_time - start_time
                
                # 计算下载速度
                if download_time > 0:
                    speed_mbps = (downloaded_size / 1024 / 1024) / download_time
                else:
                    speed_mbps = 0
                
                # 显示结果
                click.echo(f"  ✅ 下载成功")
                click.echo(f"  📥 下载大小: {downloaded_size:,} bytes ({downloaded_size / 1024 / 1024:.2f} MB)")
                click.echo(f"  ⏱️  下载时间: {download_time:.2f} 秒")
                click.echo(f"  🚀 下载速度: {speed_mbps:.2f} MB/s")
                
                # 验证文件大小
                if abs(downloaded_size - tf.file_size) > 1024:  # 允许1KB的误差
                    click.echo(f"  ⚠️  警告: 下载大小与数据库记录不匹配")
                    click.echo(f"      数据库: {tf.file_size:,} bytes")
                    click.echo(f"      实际下载: {downloaded_size:,} bytes")
                
                successful_downloads += 1
                total_download_time += download_time
                total_file_size += downloaded_size
                
            except requests.exceptions.Timeout:
                click.echo(f"  ❌ 下载超时 (>{timeout}秒)")
                failed_downloads += 1
            except requests.exceptions.RequestException as e:
                click.echo(f"  ❌ 下载失败: {str(e)}")
                failed_downloads += 1
            except Exception as e:
                click.echo(f"  ❌ 未知错误: {str(e)}")
                failed_downloads += 1
                if verbose:
                    logger.exception("下载测试异常")
        
        # 显示总结
        click.echo("\n" + "=" * 60)
        click.echo("📊 测试结果总结")
        click.echo("=" * 60)
        click.echo(f"📁 总文件数: {total_files}")
        click.echo(f"✅ 成功下载: {successful_downloads}")
        click.echo(f"❌ 失败下载: {failed_downloads}")
        click.echo(f"📈 成功率: {(successful_downloads / total_files * 100):.1f}%")
        
        if successful_downloads > 0:
            avg_download_time = total_download_time / successful_downloads
            total_size_mb = total_file_size / 1024 / 1024
            avg_speed = total_size_mb / total_download_time if total_download_time > 0 else 0
            
            click.echo(f"📥 总下载量: {total_size_mb:.2f} MB")
            click.echo(f"⏱️  总下载时间: {total_download_time:.2f} 秒")
            click.echo(f"📊 平均下载时间: {avg_download_time:.2f} 秒/文件")
            click.echo(f"🚀 平均下载速度: {avg_speed:.2f} MB/s")
        
        if failed_downloads > 0:
            click.echo(f"\n⚠️  有 {failed_downloads} 个文件下载失败，请检查网络连接和文件状态")
        
        click.echo(f"\n💡 提示: 所有下载内容已写入 /dev/null，不占用磁盘空间")
        
    except Exception as e:
        logger.error("测试下载表现失败: %s", str(e), exc_info=True)
        click.echo(f"❌ 操作失败: {str(e)}")


# 注册命令到 admin_cli
admin_cli.add_command(test_download_performance)
