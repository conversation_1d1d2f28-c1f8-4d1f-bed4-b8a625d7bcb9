"""
一次性脚本：修复存量数据中同时拥有一次性购买权益和免费权益的用户

问题描述：
在之前的实现中，create_from_purchase 方法没有终止免费权益，
导致一些用户同时拥有一次性购买权益和免费权益。

解决方案：
将这些用户的免费权益设置为不再自动续期（is_recurring=0），
让其自然失效，而不是立即终止。

SQL查询逻辑：
select * from entitlements e1
left join entitlements e2 on e1.user_id=e2.user_id
and e1.source_type='one_time' and e2.source_type='free_plan'
where e2.valid_until >now() and e1.valid_until >now() and e2.is_recurring=1

Usage:
    flask oneoff fix-free-entitlements --help
    flask oneoff fix-free-entitlements --dry-run
    flask oneoff fix-free-entitlements --execute
    flask oneoff fix-free-entitlements --execute --user-id 123456
    flask oneoff fix-free-entitlements --stats
"""

import logging
import click
from datetime import datetime
from flask.cli import with_appcontext

from models import db
from models.entitlement import Entitlement, EntitlementSource
from models import db_transaction

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def find_conflicting_entitlements(user_id=None):
    """
    查找同时拥有有效一次性购买权益和免费权益的用户

    Args:
        user_id: 可选，只查找特定用户的冲突权益

    Returns:
        list: 需要修复的免费权益记录列表
    """
    current_time = datetime.now()

    # 构建查询：查找同时拥有一次性购买权益和免费权益的用户
    # 使用子查询来找到拥有有效一次性购买权益的用户
    users_with_one_time_query = db.session.query(Entitlement.user_id).filter(
        Entitlement.source_type == EntitlementSource.ONE_TIME,
        Entitlement.valid_until > current_time,
    )

    # 如果指定了用户ID，添加过滤条件
    if user_id:
        users_with_one_time_query = users_with_one_time_query.filter(
            Entitlement.user_id == user_id
        )

    users_with_one_time = users_with_one_time_query.distinct().subquery()

    # 查找这些用户中同时拥有有效免费权益且仍在自动续期的记录
    conflicting_free_entitlements_query = db.session.query(Entitlement).filter(
        Entitlement.source_type == EntitlementSource.FREE_PLAN,
        Entitlement.valid_until > current_time,
        Entitlement.is_recurring == True,
        Entitlement.user_id.in_(users_with_one_time),
    )

    # 如果指定了用户ID，添加过滤条件
    if user_id:
        conflicting_free_entitlements_query = (
            conflicting_free_entitlements_query.filter(Entitlement.user_id == user_id)
        )

    return conflicting_free_entitlements_query.all()


def show_statistics():
    """
    显示详细的数据统计信息
    """
    current_time = datetime.now()

    click.echo("📊 Detailed Statistics")
    click.echo("=" * 40)

    # 总体统计
    total_users = db.session.query(Entitlement.user_id).distinct().count()
    click.echo(f"👥 Total users with entitlements: {total_users}")

    # 一次性购买权益统计
    one_time_count = (
        db.session.query(Entitlement)
        .filter(
            Entitlement.source_type == EntitlementSource.ONE_TIME,
            Entitlement.valid_until > current_time,
        )
        .count()
    )

    one_time_users = (
        db.session.query(Entitlement.user_id)
        .filter(
            Entitlement.source_type == EntitlementSource.ONE_TIME,
            Entitlement.valid_until > current_time,
        )
        .distinct()
        .count()
    )

    click.echo(
        f"💰 Active one-time entitlements: {one_time_count} (affecting {one_time_users} users)"
    )

    # 免费权益统计
    free_count = (
        db.session.query(Entitlement)
        .filter(
            Entitlement.source_type == EntitlementSource.FREE_PLAN,
            Entitlement.valid_until > current_time,
        )
        .count()
    )

    free_recurring_count = (
        db.session.query(Entitlement)
        .filter(
            Entitlement.source_type == EntitlementSource.FREE_PLAN,
            Entitlement.valid_until > current_time,
            Entitlement.is_recurring == True,
        )
        .count()
    )

    click.echo(
        f"🆓 Active free entitlements: {free_count} (recurring: {free_recurring_count})"
    )

    # 冲突统计
    conflicting_count = len(find_conflicting_entitlements())
    click.echo(f"⚠️  Conflicting free entitlements: {conflicting_count}")

    click.echo()


def preview_changes(dry_run=True, user_id=None):
    """
    预览需要修改的数据

    Args:
        dry_run: 是否为预览模式
        user_id: 可选，只查看特定用户的数据
    """
    conflicting_entitlements = find_conflicting_entitlements(user_id=user_id)

    if not conflicting_entitlements:
        click.echo("✅ No conflicting entitlements found. All data is clean.")
        return 0

    click.echo(
        f"🔍 Found {len(conflicting_entitlements)} free entitlements that need to be fixed:"
    )
    click.echo()

    # 按用户分组显示
    user_groups = {}
    for entitlement in conflicting_entitlements:
        user_id = entitlement.user_id
        if user_id not in user_groups:
            user_groups[user_id] = []
        user_groups[user_id].append(entitlement)

    for user_id, entitlements in user_groups.items():
        click.echo(f"👤 User ID: {user_id}")

        # 显示该用户的一次性购买权益
        current_time = datetime.now()
        one_time_entitlements = (
            db.session.query(Entitlement)
            .filter(
                Entitlement.user_id == user_id,
                Entitlement.source_type == EntitlementSource.ONE_TIME,
                Entitlement.valid_until > current_time,
            )
            .all()
        )

        for ot_ent in one_time_entitlements:
            click.echo(
                f"   💰 One-time entitlement: ID={ot_ent.id}, "
                f"credits={ot_ent.total_credits}, "
                f"valid_until={ot_ent.valid_until}"
            )

        # 显示需要修复的免费权益
        for free_ent in entitlements:
            click.echo(
                f"   🆓 Free entitlement to fix: ID={free_ent.id}, "
                f"credits={free_ent.total_credits}, "
                f"valid_until={free_ent.valid_until}, "
                f"is_recurring={free_ent.is_recurring}"
            )
        click.echo()

    if dry_run:
        click.echo("🔍 DRY RUN - No changes will be made")
        click.echo(
            f"📊 Summary: {len(conflicting_entitlements)} free entitlements would be set to non-recurring"
        )

    return len(conflicting_entitlements)


@db_transaction()
def fix_conflicting_entitlements(user_id=None):
    """
    修复冲突的权益：将免费权益设置为不再自动续期

    Args:
        user_id: 可选，只修复特定用户的权益

    Returns:
        int: 修复的权益数量
    """
    conflicting_entitlements = find_conflicting_entitlements(user_id=user_id)

    if not conflicting_entitlements:
        return 0

    current_time = datetime.now()
    fixed_count = 0

    for entitlement in conflicting_entitlements:
        # 设置为不再自动续期
        entitlement.is_recurring = False
        entitlement.updated_time = current_time

        logger.info(
            f"Fixed free entitlement: ID={entitlement.id}, "
            f"user_id={entitlement.user_id}, "
            f"set is_recurring=False"
        )
        fixed_count += 1

    logger.info(f"Fixed {fixed_count} conflicting free entitlements")
    return fixed_count


@click.command("fix-free-entitlements")
@click.option(
    "--dry-run",
    is_flag=True,
    default=True,
    help="Preview changes without making them (default: True)",
)
@click.option(
    "--execute",
    is_flag=True,
    default=False,
    help="Actually execute the changes (overrides --dry-run)",
)
@click.option(
    "--user-id",
    type=int,
    default=None,
    help="Fix entitlements for a specific user ID only",
)
@click.option(
    "--stats",
    is_flag=True,
    default=False,
    help="Show detailed statistics about the data",
)
@with_appcontext
def fix_free_entitlements(dry_run, execute, user_id, stats):
    """
    修复存量数据中同时拥有一次性购买权益和免费权益的用户

    默认为预览模式，使用 --execute 参数来实际执行修改
    """
    try:
        # 如果指定了 --execute，则覆盖 dry_run
        if execute:
            dry_run = False

        click.echo("🔧 Fix Free Entitlements with One-time Purchases")
        click.echo("=" * 60)

        if dry_run:
            click.echo("🔍 Running in PREVIEW mode...")
        else:
            click.echo("⚠️  Running in EXECUTE mode...")

        if user_id:
            click.echo(f"🎯 Targeting specific user: {user_id}")

        if stats:
            show_statistics()

        click.echo()

        # 预览需要修改的数据
        count = preview_changes(dry_run=dry_run, user_id=user_id)

        if count == 0:
            return

        if dry_run:
            click.echo()
            click.echo("💡 To actually execute the changes, run:")
            cmd = "   flask oneoff fix-free-entitlements --execute"
            if user_id:
                cmd += f" --user-id {user_id}"
            click.echo(cmd)
            return

        # 确认执行
        click.echo()
        click.echo("⚠️  This will modify the database!")
        if not click.confirm("Do you want to proceed with the changes?", default=False):
            click.echo("❌ Operation cancelled")
            return

        # 执行修复
        click.echo()
        click.echo("🔧 Executing fixes...")
        fixed_count = fix_conflicting_entitlements(user_id=user_id)

        click.echo(f"✅ Successfully fixed {fixed_count} free entitlements")
        click.echo(
            "   These entitlements will no longer auto-renew and will expire naturally."
        )

        logger.info(f"Script completed successfully. Fixed {fixed_count} entitlements.")

    except Exception as e:
        logger.error(f"Script failed: {str(e)}")
        click.echo(f"❌ Error: {str(e)}", err=True)
        raise click.ClickException(str(e))


if __name__ == "__main__":
    fix_free_entitlements()
