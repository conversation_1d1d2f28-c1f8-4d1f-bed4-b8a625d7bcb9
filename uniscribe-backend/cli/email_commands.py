"""
邮件发送测试相关的CLI命令
"""

import click
from flask.cli import with_appcontext
import logging

from libs.send_email import (
    send_appsumo_onboarding_email,
    send_appsumo_follow_up_reminder_email,
    send_appsumo_last_touch_email,
    send_reset_usage_email,
)
from services.appsumo_service import AppSumoService
from models.user import User
from . import admin_cli

logger = logging.getLogger(__name__)


@admin_cli.group("email")
def email_cli():
    """邮件发送测试命令组"""
    pass


@email_cli.command("test-appsumo-onboarding")
# @click.option("--email", required=True, help="收件人邮箱地址")
# @click.option("--name", required=True, help="收件人名字")
@click.option("--user_id", required=True, help="userid")
@with_appcontext
def test_appsumo_onboarding(user_id):
    # logger.info(f"发送AppSumo欢迎邮件到: {email}, 名字: {name}")
    try:
        # send_appsumo_onboarding_email(email, name)
        AppSumoService.send_onboarding_email(user_id)
        click.echo(f"成功发送AppSumo欢迎邮件到: {user_id}")
    except Exception as e:
        logger.exception(f"发送AppSumo欢迎邮件失败: {str(e)}")
        click.echo(f"发送失败: {str(e)}", err=True)


@email_cli.command("test-appsumo-follow-up-reminder")
@click.option("--email", required=True, help="收件人邮箱地址")
@click.option("--name", required=True, help="收件人名字")
@with_appcontext
def test_appsumo_follow_up_reminder(email, name):
    """测试发送AppSumo跟进邮件

    参数:
        email: 收件人邮箱地址
        name: 收件人名字
    """
    logger.info(f"发送AppSumo跟进邮件到: {email}, 名字: {name}")
    try:
        send_appsumo_follow_up_reminder_email(email, name)
        click.echo(f"成功发送AppSumo跟进邮件到: {email}")
    except Exception as e:
        logger.exception(f"发送AppSumo跟进邮件失败: {str(e)}")
        click.echo(f"发送失败: {str(e)}", err=True)


@email_cli.command("test-appsumo-last-touch")
@click.option("--email", required=True, help="收件人邮箱地址")
@click.option("--name", required=True, help="收件人名字")
@with_appcontext
def test_appsumo_last_touch(email, name):
    """测试发送AppSumo反馈请求邮件

    参数:
        email: 收件人邮箱地址
        name: 收件人名字
    """
    logger.info(f"发送AppSumo反馈请求邮件到: {email}, 名字: {name}")
    try:
        send_appsumo_last_touch_email(email, name)
        click.echo(f"成功发送AppSumo反馈请求邮件到: {email}")
    except Exception as e:
        logger.exception(f"发送AppSumo反馈请求邮件失败: {str(e)}")
        click.echo(f"发送失败: {str(e)}", err=True)
