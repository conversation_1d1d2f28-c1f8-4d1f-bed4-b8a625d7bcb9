#!/usr/bin/env python3
"""
修复存量数据中处于 uploading 状态的文件对应的 file_storage 记录

问题描述：
在修复前，创建 transcription_file 时就会创建 file_storage 记录，
但如果用户中断上传，文件会一直处于 uploading 状态，
导致未完成上传的文件也被计入存储用量。

修复逻辑：
1. 查找所有 status=uploading 的 transcription_file
2. 对于每个文件的 fingerprint，查询相关的 file_storage 记录
3. 查询该 fingerprint 对应的所有 transcription_file 记录
4. 如果只有一个文件且 status=uploading，则删除对应的 file_storage 记录
5. 如果有其他非 uploading 状态的文件，则保留 file_storage 记录但减少引用计数

使用方法：
python -m cli.fix_uploading_file_storage --dry-run  # 预览将要执行的操作
python -m cli.fix_uploading_file_storage            # 实际执行修复
"""

import logging
from collections import defaultdict
from datetime import datetime, timedelta

import click

from app import app
from models import db_transaction, db
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from constants.transcription import TranscriptionFileStatus
from constants.file_storage import FileState

logger = logging.getLogger(__name__)


@click.command()
@click.option("--dry-run", is_flag=True, help="预览将要执行的操作，不实际修改数据")
@click.option("--days", type=int, default=7, help="只处理指定天数内创建的文件，默认7天")
@click.option("--user-id", type=int, help="只处理指定用户的文件")
@click.option("--verbose", is_flag=True, help="显示详细信息")
def fix_uploading_file_storage(dry_run, days, user_id, verbose):
    """修复处于 uploading 状态的文件对应的 file_storage 记录"""

    if dry_run:
        click.echo("🔍 DRY RUN 模式 - 只预览操作，不会实际修改数据")
    else:
        click.echo("⚠️  实际执行模式 - 将会修改数据库")
        if not click.confirm("确定要继续吗？"):
            click.echo("操作已取消")
            return

    if user_id:
        click.echo(f"📅 处理用户 {user_id} 在 {days} 天内创建的文件")
    else:
        click.echo(f"📅 处理所有用户在 {days} 天内创建的文件")

    # 设置时间范围
    min_created_time = datetime.now() - timedelta(days=days)

    # 构建查询条件
    query = TranscriptionFile.query.filter(
        TranscriptionFile.status == TranscriptionFileStatus.uploading.id,
        TranscriptionFile.created_time >= min_created_time,
        TranscriptionFile.is_deleted.is_(False),
        TranscriptionFile.fingerprint != "",  # 排除空 fingerprint
        TranscriptionFile.fingerprint.isnot(None),
    )

    # 如果指定了用户ID，添加用户过滤条件
    if user_id:
        query = query.filter(TranscriptionFile.user_id == user_id)

    uploading_files = query.all()

    if not uploading_files:
        click.echo("✅ 没有找到需要处理的 uploading 状态文件")
        return

    click.echo(f"📋 找到 {len(uploading_files)} 个 uploading 状态的文件")

    # 按 fingerprint 分组
    fingerprint_groups = defaultdict(list)
    for file in uploading_files:
        fingerprint_groups[file.fingerprint].append(file)

    click.echo(f"🔗 涉及 {len(fingerprint_groups)} 个不同的 fingerprint")

    stats = {
        "processed_fingerprints": 0,
        "deleted_storage_records": 0,
        "updated_storage_records": 0,
        "skipped_storage_records": 0,
        "errors": 0,
    }

    for fingerprint, uploading_files_for_fp in fingerprint_groups.items():
        try:
            result = process_fingerprint(
                fingerprint, uploading_files_for_fp, dry_run, verbose
            )
            stats["processed_fingerprints"] += 1

            if result["action"] == "delete":
                stats["deleted_storage_records"] += 1
            elif result["action"] == "update":
                stats["updated_storage_records"] += 1
            elif result["action"] == "skip":
                stats["skipped_storage_records"] += 1

        except Exception as e:
            logger.exception(f"处理 fingerprint {fingerprint} 时出错: {str(e)}")
            stats["errors"] += 1
            click.echo(f"❌ 处理 fingerprint {fingerprint} 时出错: {str(e)}")

    # 输出统计信息
    click.echo("\n📊 处理统计:")
    click.echo(f"  处理的 fingerprint 数量: {stats['processed_fingerprints']}")
    click.echo(f"  删除的存储记录: {stats['deleted_storage_records']}")
    click.echo(f"  更新的存储记录: {stats['updated_storage_records']}")
    click.echo(f"  跳过的存储记录: {stats['skipped_storage_records']}")
    click.echo(f"  错误数量: {stats['errors']}")

    if dry_run:
        click.echo(
            "\n💡 这是预览模式，没有实际修改数据。要执行修复，请移除 --dry-run 参数"
        )


def process_fingerprint(fingerprint, uploading_files, dry_run, verbose=False):
    """处理单个 fingerprint 对应的文件和存储记录"""

    # 查找该 fingerprint 对应的 file_storage 记录
    file_storage = FileStorage.get_by_user_id_and_fingerprint_any_state(
        uploading_files[0].user_id, fingerprint
    )

    if not file_storage:
        click.echo(f"⚠️  fingerprint {fingerprint} 没有对应的 file_storage 记录，跳过")
        return {"action": "skip", "reason": "no_storage_record"}

    # 查找该 fingerprint 对应的所有 transcription_file 记录
    all_files_for_fp = TranscriptionFile.query.filter(
        TranscriptionFile.user_id == uploading_files[0].user_id,
        TranscriptionFile.fingerprint == fingerprint,
        TranscriptionFile.is_deleted.is_(False),
    ).all()

    # 分析文件状态
    uploading_count = 0
    other_status_count = 0

    for file in all_files_for_fp:
        if file.status == TranscriptionFileStatus.uploading.id:
            uploading_count += 1
        else:
            other_status_count += 1

    click.echo(f"🔍 fingerprint {fingerprint}:")
    click.echo(f"    uploading 文件数: {uploading_count}")
    click.echo(f"    其他状态文件数: {other_status_count}")
    click.echo(f"    当前存储记录引用计数: {file_storage.reference_count}")
    click.echo(f"    当前存储记录状态: {file_storage.state}")

    if verbose:
        click.echo(f"    文件详情:")
        for file in all_files_for_fp:
            status_name = TranscriptionFileStatus.by_id(file.status).name
            click.echo(
                f"      - ID: {file.id}, 状态: {status_name}, 创建时间: {file.created_time}"
            )
        click.echo(f"    存储记录详情:")
        click.echo(
            f"      - ID: {file_storage.id}, 文件大小: {file_storage.file_size} bytes"
        )

    # 决定操作类型
    if other_status_count == 0 and uploading_count > 0:
        # 只有 uploading 状态的文件，删除存储记录
        action = "delete"
        click.echo(f"    🗑️  操作: 删除存储记录 (只有 uploading 状态文件)")

        if not dry_run:
            with db_transaction():
                db.session.delete(file_storage)
                click.echo(f"    ✅ 已删除存储记录")

    elif other_status_count > 0 and uploading_count > 0:
        # 有其他状态的文件，减少引用计数
        action = "update"
        new_reference_count = file_storage.reference_count - uploading_count
        click.echo(
            f"    📝 操作: 减少引用计数 {uploading_count} (从 {file_storage.reference_count} 到 {new_reference_count})"
        )

        if not dry_run:
            with db_transaction():
                file_storage.reference_count = new_reference_count
                if new_reference_count <= 0:
                    file_storage.state = FileState.PENDING_DELETION.value
                    click.echo(f"    🔄 引用计数为0，标记为待删除状态")
                click.echo(f"    ✅ 已更新存储记录")

    else:
        # 异常情况，跳过
        action = "skip"
        click.echo(
            f"    ⚠️  跳过: 异常情况 (uploading: {uploading_count}, other: {other_status_count})"
        )

    return {"action": action}


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    with app.app_context():
        fix_uploading_file_storage()
