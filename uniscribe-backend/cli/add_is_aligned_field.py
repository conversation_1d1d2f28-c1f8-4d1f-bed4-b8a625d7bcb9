#!/usr/bin/env python3
"""
添加 is_aligned 字段的迁移脚本

用明确的布尔字段替代隐式的 original_segments 状态判断
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text
import socket

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # 如果没有 python-dotenv，尝试手动加载 .env 文件
    env_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))), ".env"
    )
    if os.path.exists(env_path):
        with open(env_path, "r") as f:
            for line in f:
                if "=" in line and not line.strip().startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value


def create_minimal_app():
    """创建最小化的 Flask 应用"""
    app = Flask(__name__)
    
    # 从环境变量加载数据库配置
    database_url = os.getenv("SQLALCHEMY_DATABASE_URI") or os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError(
            "SQLALCHEMY_DATABASE_URI or DATABASE_URL environment variable is required"
        )
    
    # 如果在本地运行且是 Docker 环境的配置，替换为本地连接
    # 如果在 Docker 容器内运行，保持原有配置
    try:
        # 尝试解析 mysql_db 主机名，如果失败说明在本地环境
        socket.gethostbyname('mysql_db')
        # 在 Docker 环境中，保持原有配置
        pass
    except socket.gaierror:
        # 在本地环境中，替换为本地连接
        if "mysql_db:3306" in database_url:
            database_url = database_url.replace("mysql_db:3306", "localhost:3307")
    
    app.config["SQLALCHEMY_DATABASE_URI"] = database_url
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
    
    db = SQLAlchemy(app)
    return app, db


def add_is_aligned_field(db):
    """添加 is_aligned 字段"""
    print("1. 添加 is_aligned 字段...")
    
    try:
        # 检查字段是否已存在
        result = db.session.execute(
            text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'task_result' 
                AND COLUMN_NAME = 'is_aligned'
            """)
        )
        
        if result.fetchone():
            print("  - is_aligned 字段已存在，跳过...")
            return True
        
        # 添加字段
        db.session.execute(
            text("""
                ALTER TABLE task_result 
                ADD COLUMN is_aligned BOOLEAN NOT NULL DEFAULT FALSE 
                COMMENT '是否已完成对齐处理'
            """)
        )
        
        db.session.commit()
        print("  - 成功添加 is_aligned 字段")
        return True
        
    except Exception as e:
        print(f"  - 添加 is_aligned 字段失败: {e}")
        db.session.rollback()
        return False


def migrate_existing_data(db):
    """迁移现有数据，根据 original_segments 是否存在来设置 is_aligned"""
    print("2. 迁移现有数据...")
    
    try:
        # 获取所有记录的统计
        total_result = db.session.execute(text("SELECT COUNT(*) FROM task_result"))
        total_count = total_result.fetchone()[0]
        
        original_segments_result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE original_segments IS NOT NULL")
        )
        original_segments_count = original_segments_result.fetchone()[0]
        
        print(f"  - 总记录数: {total_count}")
        print(f"  - 有 original_segments 的记录: {original_segments_count}")
        
        # 将有 original_segments 的记录标记为已对齐
        if original_segments_count > 0:
            db.session.execute(
                text("""
                    UPDATE task_result 
                    SET is_aligned = TRUE 
                    WHERE original_segments IS NOT NULL
                """)
            )
            
            db.session.commit()
            print(f"  - 成功将 {original_segments_count} 条记录标记为已对齐")
        
        # 验证迁移结果
        aligned_result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE is_aligned = TRUE")
        )
        aligned_count = aligned_result.fetchone()[0]
        
        not_aligned_result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE is_aligned = FALSE")
        )
        not_aligned_count = not_aligned_result.fetchone()[0]
        
        print(f"  - 迁移后统计:")
        print(f"    - 已对齐: {aligned_count} 条")
        print(f"    - 未对齐: {not_aligned_count} 条")
        
        return True
        
    except Exception as e:
        print(f"  - 数据迁移失败: {e}")
        db.session.rollback()
        return False


def verify_migration(db):
    """验证迁移结果"""
    print("3. 验证迁移结果...")
    
    try:
        # 检查字段结构
        result = db.session.execute(
            text("""
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'task_result' 
                AND COLUMN_NAME = 'is_aligned'
            """)
        )
        
        field_info = result.fetchone()
        if field_info:
            print(f"  - 字段信息: {field_info[0]} {field_info[1]} {field_info[2]} DEFAULT {field_info[3]} - {field_info[4]}")
        else:
            print("  - ❌ is_aligned 字段不存在")
            return False
        
        # 检查数据一致性：有 original_segments 的记录应该都标记为已对齐
        result = db.session.execute(
            text("""
                SELECT COUNT(*) FROM task_result 
                WHERE original_segments IS NOT NULL AND is_aligned = FALSE
            """)
        )
        inconsistent_count = result.fetchone()[0]
        
        if inconsistent_count > 0:
            print(f"  - ❌ 发现 {inconsistent_count} 条不一致的记录")
            return False
        
        # 检查数据一致性：没有 original_segments 的记录应该都标记为未对齐
        result = db.session.execute(
            text("""
                SELECT COUNT(*) FROM task_result 
                WHERE original_segments IS NULL AND is_aligned = TRUE
            """)
        )
        inconsistent_count = result.fetchone()[0]
        
        if inconsistent_count > 0:
            print(f"  - ❌ 发现 {inconsistent_count} 条不一致的记录")
            return False
        
        print("  - ✅ 数据一致性检查通过")
        print("  - ✅ 迁移验证通过")
        return True
        
    except Exception as e:
        print(f"  - 验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始添加 is_aligned 字段...")
    print("=" * 60)
    
    app, db = create_minimal_app()
    
    with app.app_context():
        # 执行迁移步骤
        steps = [
            add_is_aligned_field,
            migrate_existing_data,
            verify_migration,
        ]
        
        for step in steps:
            if not step(db):
                print("❌ 迁移失败，请检查错误信息")
                return False
    
    print("=" * 60)
    print("✅ is_aligned 字段添加完成！")
    print("\n下一步:")
    print("1. 更新代码以使用新的 is_aligned 字段")
    print("2. 重启应用服务")
    print("3. 测试功能是否正常")
    return True


if __name__ == "__main__":
    if main():
        sys.exit(0)
    else:
        sys.exit(1)
