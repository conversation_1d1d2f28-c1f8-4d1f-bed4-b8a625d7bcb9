#!/usr/bin/env python3
"""
简化的重构后功能测试脚本
"""

import sys
import os
import json

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text

# 加载环境变量
try:
    from dotenv import load_dotenv

    load_dotenv()
except ImportError:
    # 如果没有 python-dotenv，尝试手动加载 .env 文件
    env_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))), ".env"
    )
    if os.path.exists(env_path):
        with open(env_path, "r") as f:
            for line in f:
                if "=" in line and not line.strip().startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value


def create_minimal_app():
    """创建最小化的 Flask 应用"""
    app = Flask(__name__)

    # 从环境变量加载数据库配置
    database_url = os.getenv("SQLALCHEMY_DATABASE_URI") or os.getenv("DATABASE_URL")
    if not database_url:
        raise ValueError(
            "SQLALCHEMY_DATABASE_URI or DATABASE_URL environment variable is required"
        )

    # 如果在本地运行且是 Docker 环境的配置，替换为本地连接
    # 如果在 Docker 容器内运行，保持原有配置
    import socket

    try:
        # 尝试解析 mysql_db 主机名，如果失败说明在本地环境
        socket.gethostbyname("mysql_db")
        # 在 Docker 环境中，保持原有配置
        pass
    except socket.gaierror:
        # 在本地环境中，替换为本地连接
        if "mysql_db:3306" in database_url:
            database_url = database_url.replace("mysql_db:3306", "localhost:3307")

    app.config["SQLALCHEMY_DATABASE_URI"] = database_url
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False

    db = SQLAlchemy(app)
    return app, db


def test_database_schema(db):
    """测试数据库表结构"""
    print("1. 测试数据库表结构...")

    try:
        # 检查字段是否存在
        result = db.session.execute(
            text(
                """
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'task_result' 
                AND COLUMN_NAME IN ('segments', 'original_segments', 'diarization_segments', 'aligned_segments')
                ORDER BY COLUMN_NAME
            """
            )
        )

        fields = {row[0]: row for row in result.fetchall()}

        # 检查必要字段
        assert "segments" in fields, "segments 字段不存在"
        assert "original_segments" in fields, "original_segments 字段不存在"
        assert "diarization_segments" in fields, "diarization_segments 字段不存在"

        # 检查 aligned_segments 字段是否已删除
        assert "aligned_segments" not in fields, "aligned_segments 字段仍然存在"

        print("  ✅ 数据库表结构检查通过")
        print("  - segments: 存在")
        print("  - original_segments: 存在")
        print("  - diarization_segments: 存在")
        print("  - aligned_segments: 已删除")
        return True

    except Exception as e:
        print(f"  ❌ 数据库表结构检查失败: {e}")
        return False


def test_data_integrity(db):
    """测试数据完整性"""
    print("2. 测试数据完整性...")

    try:
        # 检查现有数据的完整性
        total_result = db.session.execute(text("SELECT COUNT(*) FROM task_result"))
        total_count = total_result.fetchone()[0]

        segments_result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE segments IS NOT NULL")
        )
        segments_count = segments_result.fetchone()[0]

        original_segments_result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE original_segments IS NOT NULL")
        )
        original_segments_count = original_segments_result.fetchone()[0]

        print(f"  - 总记录数: {total_count}")
        print(f"  - 有 segments 的记录: {segments_count}")
        print(f"  - 有 original_segments 的记录: {original_segments_count}")

        # 检查是否有记录的 segments 为空但 original_segments 不为空（这种情况不应该存在）
        invalid_result = db.session.execute(
            text(
                "SELECT COUNT(*) FROM task_result WHERE segments IS NULL AND original_segments IS NOT NULL"
            )
        )
        invalid_records = invalid_result.fetchone()[0]

        assert (
            invalid_records == 0
        ), f"发现 {invalid_records} 条无效记录（segments 为空但 original_segments 不为空）"

        print("  ✅ 数据完整性检查通过")
        return True

    except Exception as e:
        print(f"  ❌ 数据完整性检查失败: {e}")
        return False


def test_speaker_info_detection(db):
    """测试说话人信息检测逻辑"""
    print("3. 测试说话人信息检测逻辑...")

    try:
        # 查找有说话人信息的记录
        result = db.session.execute(
            text(
                "SELECT id, file_id, segments FROM task_result WHERE segments IS NOT NULL LIMIT 10"
            )
        )

        has_speaker_count = 0
        no_speaker_count = 0

        for row in result.fetchall():
            record_id, file_id, segments_json = row
            if segments_json:
                segments = json.loads(segments_json)
                contains_speaker_info = any(
                    "speaker" in seg for seg in segments if isinstance(seg, dict)
                )

                if contains_speaker_info:
                    has_speaker_count += 1
                    print(f"  - 文件 {file_id}: 有说话人信息")
                else:
                    no_speaker_count += 1
                    print(f"  - 文件 {file_id}: 无说话人信息")

        print(f"  - 检查了 {has_speaker_count + no_speaker_count} 条记录")
        print(f"  - 有说话人信息: {has_speaker_count} 条")
        print(f"  - 无说话人信息: {no_speaker_count} 条")
        print("  ✅ 说话人信息检测逻辑检查通过")
        return True

    except Exception as e:
        print(f"  ❌ 说话人信息检测逻辑检查失败: {e}")
        return False


def test_migration_correctness(db):
    """测试迁移正确性"""
    print("4. 测试迁移正确性...")

    try:
        # 检查有 original_segments 的记录，确保迁移正确
        result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE original_segments IS NOT NULL")
        )
        original_count = result.fetchone()[0]

        # 检查有 segments 的记录
        result = db.session.execute(
            text("SELECT COUNT(*) FROM task_result WHERE segments IS NOT NULL")
        )
        segments_count = result.fetchone()[0]

        # 所有有 segments 的记录都应该有 original_segments
        assert (
            original_count == segments_count
        ), f"迁移不完整：有 {segments_count} 条 segments 记录，但只有 {original_count} 条 original_segments 记录"

        print(f"  - 所有 {segments_count} 条记录都正确迁移")
        print("  ✅ 迁移正确性检查通过")
        return True

    except Exception as e:
        print(f"  ❌ 迁移正确性检查失败: {e}")
        return False


def main():
    """主函数"""
    print("🧪 开始测试重构后的 segments 数据结构...")
    print("=" * 60)

    app, db = create_minimal_app()

    with app.app_context():
        # 执行测试
        tests = [
            test_database_schema,
            test_data_integrity,
            test_speaker_info_detection,
            test_migration_correctness,
        ]

        passed = 0
        failed = 0

        for test in tests:
            if test(db):
                passed += 1
            else:
                failed += 1

    print("=" * 60)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")

    if failed == 0:
        print("✅ 所有测试通过！重构成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查问题")
        return 1


if __name__ == "__main__":
    sys.exit(main())
