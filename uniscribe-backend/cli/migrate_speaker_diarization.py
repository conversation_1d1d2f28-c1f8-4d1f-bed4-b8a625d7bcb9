#!/usr/bin/env python3
"""
数据库迁移脚本：添加说话人识别相关字段

使用方法：
python cli/migrate_speaker_diarization.py
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app
from models import db
from sqlalchemy import text


def migrate_transcription_file_table():
    """为 transcription_file 表添加 enable_speaker_diarization 字段"""
    print("Migrating transcription_file table...")

    with app.app_context():
        try:
            # 检查字段是否已存在
            result = db.session.execute(
                text(
                    """
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'transcription_file' 
                AND COLUMN_NAME = 'enable_speaker_diarization'
            """
                )
            )

            if result.fetchone():
                print(
                    "  - enable_speaker_diarization field already exists, skipping..."
                )
                return

            # 添加字段
            db.session.execute(
                text(
                    """
                ALTER TABLE transcription_file 
                ADD COLUMN enable_speaker_diarization BOOLEAN NOT NULL DEFAULT FALSE 
                COMMENT '是否开启说话人识别'
            """
                )
            )

            db.session.commit()
            print("  - Added enable_speaker_diarization field successfully")

        except Exception as e:
            db.session.rollback()
            print(f"  - Error migrating transcription_file table: {e}")
            raise


def migrate_task_result_table():
    """为 task_result 表添加说话人识别相关字段"""
    print("Migrating task_result table...")

    with app.app_context():
        try:
            # 检查字段是否已存在
            fields_to_add = [
                ("diarization_segments", "JSON", "说话人识别分段"),
                ("aligned_segments", "JSON", "对齐后的分段(包含说话人信息)"),
            ]

            for field_name, field_type, comment in fields_to_add:
                result = db.session.execute(
                    text(
                        f"""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'task_result' 
                    AND COLUMN_NAME = '{field_name}'
                """
                    )
                )

                if result.fetchone():
                    print(f"  - {field_name} field already exists, skipping...")
                    continue

                # 添加字段
                db.session.execute(
                    text(
                        f"""
                    ALTER TABLE task_result 
                    ADD COLUMN {field_name} {field_type} NULL 
                    COMMENT '{comment}'
                """
                    )
                )

                print(f"  - Added {field_name} field successfully")

            db.session.commit()

        except Exception as e:
            db.session.rollback()
            print(f"  - Error migrating task_result table: {e}")
            raise


def verify_migration():
    """验证迁移是否成功"""
    print("Verifying migration...")

    with app.app_context():
        try:
            # 验证 transcription_file 表
            result = db.session.execute(
                text(
                    """
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'transcription_file' 
                AND COLUMN_NAME = 'enable_speaker_diarization'
            """
                )
            )

            row = result.fetchone()
            if row:
                print(f"  - transcription_file.enable_speaker_diarization: {row}")
            else:
                print("  - ERROR: enable_speaker_diarization field not found!")
                return False

            # 验证 task_result 表
            fields_to_check = ["diarization_segments", "aligned_segments"]

            for field_name in fields_to_check:
                result = db.session.execute(
                    text(
                        f"""
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'task_result' 
                    AND COLUMN_NAME = '{field_name}'
                """
                    )
                )

                row = result.fetchone()
                if row:
                    print(f"  - task_result.{field_name}: {row}")
                else:
                    print(f"  - ERROR: {field_name} field not found!")
                    return False

            print("Migration verification completed successfully!")
            return True

        except Exception as e:
            print(f"  - Error verifying migration: {e}")
            return False


def main():
    """主函数"""
    print("Starting speaker diarization database migration...")
    print("=" * 50)

    try:
        # 执行迁移
        migrate_transcription_file_table()
        migrate_task_result_table()

        # 验证迁移
        if verify_migration():
            print("=" * 50)
            print("Migration completed successfully!")
        else:
            print("=" * 50)
            print("Migration verification failed!")
            sys.exit(1)

    except Exception as e:
        print(f"Migration failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
