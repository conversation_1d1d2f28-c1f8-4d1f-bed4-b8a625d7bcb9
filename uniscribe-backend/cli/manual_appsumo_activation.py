#!/usr/bin/env python3
"""
手动激活 AppSumo license 脚本

用于在没有 OAuth token 的情况下手动激活 AppSumo license
只需要 license_key 和 user_id
"""

import sys
import os
import logging
from datetime import datetime

import click
from flask.cli import with_appcontext

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import db
from models.appsumo_license import AppSumoLicense
from models.user import User
from services.appsumo_service import AppSumoService
from exceptions.appsumo import (
    AppSumoMultipleLicensesError,
    AppSumoLicenseActivationError,
)

# 设置 logger
logger = logging.getLogger(__name__)


@click.group(name="manual-appsumo")
def manual_appsumo():
    """手动 AppSumo license 激活工具"""


@manual_appsumo.command("activate")
@click.option("--license-key", required=True, help="AppSumo License Key")
@click.option("--user-id", type=int, required=True, help="用户 ID")
@click.option("--tier", type=int, default=1, help="产品等级，默认为 1")
@click.option("--force", is_flag=True, help="强制激活，跳过重复激活检查")
@click.option("--dry-run", is_flag=True, help="预览模式，不实际执行操作")
@with_appcontext
def activate_license(license_key, user_id, tier, force, dry_run):
    """手动激活 AppSumo license"""

    logger.info(
        "开始手动激活 AppSumo license: key=%s, user_id=%s, tier=%s",
        license_key,
        user_id,
        tier,
    )

    try:
        # 1. 验证用户是否存在
        user = User.get_by_id(user_id)
        if not user:
            click.echo(f"❌ 错误: 用户 ID {user_id} 不存在", err=True)
            return

        click.echo(f"✅ 找到用户: {user.email} (ID: {user_id})")

        # 2. 查找 license 记录
        license_record = AppSumoLicense.get_by_license_key(license_key)
        if not license_record:
            click.echo(f"❌ 错误: License Key {license_key} 不存在", err=True)
            return

        click.echo(f"✅ 找到 license 记录: {license_key}")
        click.echo(f"   - 当前状态: {license_record.status}")
        click.echo(f"   - 当前用户: {license_record.user_id}")
        click.echo(f"   - 等级: {license_record.tier}")

        # 3. 检查是否已经激活
        if license_record.user_id and not force:
            if license_record.user_id == user_id:
                click.echo(f"⚠️  警告: License 已经被用户 {user_id} 激活")
            else:
                click.echo(
                    f"❌ 错误: License 已经被其他用户 {license_record.user_id} 激活"
                )
                click.echo("   使用 --force 参数可以强制重新激活")
                return

        # 4. 检查用户是否已经激活了其他 AppSumo license
        if not force:
            existing_licenses = AppSumoLicense.get_by_user_id(user_id)
            if existing_licenses:
                active_licenses = [
                    lic for lic in existing_licenses if lic.status == "active"
                ]
                if active_licenses:
                    click.echo(
                        f"❌ 错误: 用户 {user_id} 已经激活了其他 AppSumo license:"
                    )
                    for lic in active_licenses:
                        click.echo(f"   - {lic.license_key} (tier {lic.tier})")
                    click.echo("   使用 --force 参数可以强制激活")
                    return

        # 5. 显示即将执行的操作
        click.echo("\n📋 即将执行的操作:")
        click.echo(f"   - 设置 license_record.user_id = {user_id}")
        click.echo("   - 设置 license_record.status = 'active'")
        click.echo(f"   - 设置 license_record.activation_time = {datetime.now()}")
        click.echo(f"   - 更新用户权益 (tier {tier})")

        if dry_run:
            click.echo("\n🔍 预览模式，不会实际执行操作")
            return

        # 6. 请求确认
        if not force:
            if not click.confirm("\n确定要执行激活操作吗?"):
                click.echo("操作已取消")
                return

        # 7. 执行激活操作
        click.echo("\n🚀 开始执行激活操作...")

        # 更新 license 记录
        license_record.user_id = user_id
        license_record.status = "active"
        license_record.activation_time = datetime.now()

        # 更新用户权益
        AppSumoService.update_user_entitlements(user_id, license_key, tier)

        # 提交数据库事务
        db.session.commit()

        click.echo("✅ AppSumo license 激活成功!")
        click.echo(f"   - License Key: {license_key}")
        click.echo(f"   - 用户: {user.email} (ID: {user_id})")
        click.echo(f"   - 等级: tier {tier}")
        click.echo(f"   - 激活时间: {license_record.activation_time}")

        logger.info(
            "手动激活 AppSumo license 成功: key=%s, user_id=%s, tier=%s",
            license_key,
            user_id,
            tier,
        )

    except AppSumoMultipleLicensesError:
        click.echo(f"❌ 错误: 用户 {user_id} 已经激活了其他 AppSumo license", err=True)
        click.echo("   使用 --force 参数可以强制激活")
    except AppSumoLicenseActivationError as e:
        click.echo(f"❌ 激活错误: {str(e)}", err=True)
    except Exception as e:
        db.session.rollback()
        click.echo(f"❌ 激活失败: {str(e)}", err=True)
        logger.exception(
            "手动激活 AppSumo license 失败: key=%s, user_id=%s", license_key, user_id
        )


@manual_appsumo.command("check")
@click.option("--license-key", required=True, help="AppSumo License Key")
@with_appcontext
def check_license(license_key):
    """检查 AppSumo license 状态"""

    try:
        license_record = AppSumoLicense.get_by_license_key(license_key)
        if not license_record:
            click.echo(f"❌ License Key {license_key} 不存在")
            return

        click.echo("📋 License 信息:")
        click.echo(f"   - License Key: {license_record.license_key}")
        click.echo(f"   - 状态: {license_record.status}")
        click.echo(f"   - 等级: {license_record.tier}")
        click.echo(f"   - 用户 ID: {license_record.user_id}")
        click.echo(f"   - 激活时间: {license_record.activation_time}")
        click.echo(f"   - 创建时间: {license_record.created_time}")
        click.echo(f"   - 最后事件: {license_record.event}")

        if license_record.user_id:
            user = User.get_by_id(license_record.user_id)
            if user:
                click.echo(f"   - 用户邮箱: {user.email}")

    except Exception as e:
        click.echo(f"❌ 查询失败: {str(e)}", err=True)


@manual_appsumo.command("list-user-licenses")
@click.option("--user-id", type=int, required=True, help="用户 ID")
@with_appcontext
def list_user_licenses(user_id):
    """列出用户的所有 AppSumo license"""

    try:
        user = User.get_by_id(user_id)
        if not user:
            click.echo(f"❌ 用户 ID {user_id} 不存在")
            return

        click.echo(f"👤 用户: {user.email} (ID: {user_id})")

        licenses = AppSumoLicense.get_by_user_id(user_id)
        if not licenses:
            click.echo("   没有找到 AppSumo license")
            return

        click.echo(f"   共找到 {len(licenses)} 个 license:")
        for i, license_record in enumerate(licenses, 1):
            click.echo(f"   {i}. {license_record.license_key}")
            click.echo(f"      - 状态: {license_record.status}")
            click.echo(f"      - 等级: {license_record.tier}")
            click.echo(f"      - 激活时间: {license_record.activation_time}")

    except Exception as e:
        click.echo(f"❌ 查询失败: {str(e)}", err=True)


if __name__ == "__main__":
    manual_appsumo()
