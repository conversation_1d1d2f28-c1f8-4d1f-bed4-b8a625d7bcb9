"""
任务管理相关的CLI命令
"""

import click
from flask.cli import with_appcontext
import logging

from models.transcription_file import TranscriptionFile
from controllers.task import create_transcription_task

from . import admin_cli

logger = logging.getLogger(__name__)


@admin_cli.command("trigger-transcription")
@click.argument("transcription_file_id", type=int)
@with_appcontext
def trigger_transcription(transcription_file_id):
    """手动触发转录任务

    参数:
        transcription_file_id: 转录文件ID
    """
    # 获取转录文件
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        logger.error(f"转录文件不存在: {transcription_file_id}")
        return

    user_id = transcription_file.user_id
    logger.info(f"找到转录文件 ID: {transcription_file_id}, 用户 ID: {user_id}")

    try:
        # 调用创建转录任务函数
        task = create_transcription_task(user_id, transcription_file_id)
        logger.info(f"成功创建转录任务: {task.id} 状态: {task.status}")
        
        # 输出任务详情
        logger.info(f"任务详情:")
        logger.info(f"  文件ID: {task.file_id}")
        logger.info(f"  任务类型: {task.task_type}")
        logger.info(f"  请求的服务提供商: {task.requested_service_provider}")
        if task.error_message:
            logger.warning(f"  错误信息: {task.error_message}")
            
    except Exception as e:
        logger.exception(f"创建转录任务失败: {str(e)}")
