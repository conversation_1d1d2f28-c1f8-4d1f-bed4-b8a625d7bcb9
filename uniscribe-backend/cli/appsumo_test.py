import json
import hmac
import hashlib
import time
import uuid
from datetime import datetime

import click
import requests
from flask.cli import with_appcontext

from config import CONFIG


@click.group(name="appsumo-test")
def appsumo_test():
    """AppSumo 测试工具"""
    pass


@appsumo_test.command("send-event")
@click.option(
    "--event",
    type=click.Choice(["purchase", "activate", "upgrade", "downgrade", "deactivate"]),
    required=True,
    help="事件类型",
)
@click.option("--license-key", default=None, help="License Key，如果不提供则自动生成")
@click.option(
    "--prev-license-key",
    default=None,
    help="之前的 License Key（仅用于 upgrade/downgrade 事件）",
)
@click.option("--tier", type=int, default=1, help="产品等级，1 或 2")
@click.option("--test", is_flag=True, help="是否为测试事件")
@click.option("--url", default=None, help="Webhook URL，默认使用配置中的 URL")
@click.option(
    "--api-key", default=None, help="AppSumo API 密钥，默认使用配置中的 API 密钥"
)
@with_appcontext
def send_event(event, license_key, prev_license_key, tier, test, url, api_key):
    """发送模拟的 AppSumo webhook 事件"""
    # 生成 license key 如果没有提供
    if not license_key:
        license_key = str(uuid.uuid4())
        click.echo(f"生成的 License Key: {license_key}")

    # 检查 upgrade/downgrade 事件是否提供了 prev_license_key
    if event in ["upgrade", "downgrade"] and not prev_license_key:
        prev_license_key = str(uuid.uuid4())
        click.echo(f"生成的前一个 License Key: {prev_license_key}")

    # 构建事件数据
    event_timestamp = int(time.time() * 1000)  # 毫秒时间戳
    created_at = int(time.time())  # 秒级时间戳

    # 根据事件类型设置 license_status
    if event in ("purchase", "activate", "upgrade", "downgrade"):
        license_status = "inactive"
    elif event == "deactivate":
        license_status = "active"  # 停用事件中，当前状态是 active
    else:
        license_status = "inactive"  # 默认值

    # 构建事件数据
    event_data = {
        "license_key": license_key,
        "event": event,
        "license_status": license_status,
        "event_timestamp": event_timestamp,
        "created_at": created_at,
        "test": test,
    }

    # 添加 tier 字段（除了 purchase 事件外都需要）
    if event != "purchase":
        event_data["tier"] = tier

    # 添加 extra 字段和 reason
    if event == "activate":
        event_data["extra"] = {"reason": "Purchased by the customer"}
    elif event == "upgrade":
        event_data["extra"] = {"reason": "Upgraded by the customer"}
    elif event == "downgrade":
        event_data["extra"] = {"reason": "Downgraded by the customer"}
    elif event == "deactivate":
        event_data["extra"] = {"reason": "Refunded by the user"}

    # 添加 prev_license_key（如果适用）
    if event in ["upgrade", "downgrade"] and prev_license_key:
        event_data["prev_license_key"] = prev_license_key

    # 生成签名
    if not api_key:
        api_key = CONFIG.APPSUMO.get("api_key")
        if not api_key:
            click.echo(
                "错误: 未配置 APPSUMO_API_KEY，请使用 --api-key 选项指定", err=True
            )
            return

    click.echo(f"使用 API 密钥: {api_key}")

    # 获取 webhook URL
    webhook_url = url or CONFIG.APPSUMO.get("webhook_url")
    if not webhook_url:
        click.echo(
            "错误: 未提供 webhook URL，请使用 --url 选项或在配置中设置 APPSUMO.webhook_url",
            err=True,
        )
        return

    # 生成时间戳和签名
    timestamp = str(int(time.time() * 1000))
    payload = json.dumps(event_data)
    message = f"{timestamp}{payload}"

    # 打印调试信息
    click.echo(f"\n签名计算信息:")
    click.echo(f"API Key: {api_key}")
    click.echo(f"Timestamp: {timestamp}")
    click.echo(f"Message: {timestamp}{payload[:30]}...")

    signature = hmac.new(
        key=api_key.encode(), msg=message.encode(), digestmod=hashlib.sha256
    ).hexdigest()

    click.echo(f"Computed signature: {signature}")

    # 发送请求
    headers = {
        "Content-Type": "application/json; charset=UTF-8",
        "X-AppSumo-Timestamp": timestamp,
        "X-AppSumo-Signature": signature,
        "Date": datetime.now().strftime(
            "%a, %d %b %Y %H:%M:%S GMT"
        ),  # RFC 7231 格式的日期
    }

    # 打印完整请求信息（类似 AppSumo 的请求格式）
    request_info = {
        "header": json.dumps(
            {
                "content-length": "",
                "content-type": "application/json; charset=UTF-8",
                "date": headers["Date"],
                "x-appsumo-signature": signature,
                "x-appsumo-timestamp": timestamp,
            }
        ),
        "body": payload,
        "method": "POST",
        "url": webhook_url,
        "event": event,
        "timestamp": int(time.time()),
    }

    click.echo(f"发送 {event} 事件到 {webhook_url}")
    click.echo(f"事件数据: {json.dumps(event_data, indent=2)}")
    click.echo(f"完整请求信息: {json.dumps(request_info, indent=2)}")

    try:
        response = requests.post(
            webhook_url, headers=headers, data=payload, timeout=10
        )  # 添加超时参数
        click.echo(f"响应状态码: {response.status_code}")
        click.echo(f"响应内容: {response.text}")
    except Exception as e:
        click.echo(f"请求失败: {str(e)}", err=True)


@appsumo_test.command("simulate-flow")
@click.option("--tier", type=int, default=1, help="产品等级，1 或 2")
@click.option("--test", is_flag=True, help="是否为测试事件")
@click.option("--url", default=None, help="Webhook URL，默认使用配置中的 URL")
@click.option(
    "--api-key", default=None, help="AppSumo API 密钥，默认使用配置中的 API 密钥"
)
@with_appcontext
def simulate_flow(tier, test, url, api_key):
    """模拟完整的 AppSumo 流程：purchase -> activate -> upgrade/downgrade -> deactivate"""
    # 生成 license keys
    license_key = str(uuid.uuid4())
    license_key2 = str(uuid.uuid4())

    click.echo("=== 开始模拟 AppSumo 流程 ===")
    click.echo(f"License Key: {license_key}")

    # 1. 发送 purchase 事件
    click.echo("\n1. 发送 purchase 事件")
    ctx = click.Context(send_event)
    ctx.invoke(
        send_event,
        event="purchase",
        license_key=license_key,
        prev_license_key=None,
        tier=tier,
        test=test,
        url=url,
        api_key=api_key,
    )

    # 等待 1 秒
    time.sleep(1)

    # 2. 发送 activate 事件
    click.echo("\n2. 发送 activate 事件")
    ctx.invoke(
        send_event,
        event="activate",
        license_key=license_key,
        prev_license_key=None,
        tier=tier,
        test=test,
        url=url,
        api_key=api_key,
    )

    # 等待 1 秒
    time.sleep(1)

    # 3. 发送 upgrade/downgrade 事件（如果 tier=1 则升级，否则降级）
    new_tier = 2 if tier == 1 else 1
    event_type = "upgrade" if new_tier > tier else "downgrade"

    click.echo(f"\n3. 发送 {event_type} 事件 (tier {tier} -> {new_tier})")
    ctx.invoke(
        send_event,
        event=event_type,
        license_key=license_key2,
        prev_license_key=license_key,
        tier=new_tier,
        test=test,
        url=url,
        api_key=api_key,
    )

    # 等待 1 秒
    time.sleep(1)

    # 4. 发送 deactivate 事件
    click.echo("\n4. 发送 deactivate 事件")
    ctx.invoke(
        send_event,
        event="deactivate",
        license_key=license_key2,
        prev_license_key=None,
        tier=new_tier,
        test=test,
        url=url,
        api_key=api_key,
    )

    click.echo("\n=== AppSumo 流程模拟完成 ===")


if __name__ == "__main__":
    appsumo_test()
