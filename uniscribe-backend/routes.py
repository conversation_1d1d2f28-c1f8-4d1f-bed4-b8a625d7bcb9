from resources.checkout import StripeCheckoutResource
from resources.customer_portal import CustomerPortalResource
from resources.entitlement import EntitlementResource
from resources.example import ExampleResource
from resources.export import ExportResource, ExportShareResource, BatchExportResource
from resources.outline_export import OutlineExportResource
from resources.plan import PlanConfigResource, SharerPlanConfigResource
from resources.queue import QueueStatsResource
from resources.task import (
    TaskNextResource,
    TaskResultResource,
    TaskStatusResource,
    CreateTranscriptionTaskResource,
    CreateTextTasksResource,
    TaskStatusQueryResource,
)
from resources.task_retry import TaskRetryResource
from resources.transcription import (
    TranscriptionsByPageResource,
    TranscriptionsResource,
    TranscriptionResource,
    TranscriptionStatusResource,
    DeleteTranscriptionResource,
    DeleteOriginalFileResource,
    BatchDeleteTranscriptionResource,
    TranscriptionSearchResource,
    AnonymousLatestTranscriptionResource,
    MigrateAnonymousTranscriptionResource,
    UnlockTranscriptionResource,
    TranscriptionSegmentResource,
    TranscriptionSegmentsBatchResource,
    TranscriptionSpeakersBatchResource,
)
from resources.upload import GenerateSignedUrlResource, CompleteUploadResource
from resources.multipart_upload import (
    CreateMultipartUploadResource,
    ListMultipartPartsResource,
    SignMultipartPartResource,
    CompleteMultipartUploadResource,
    AbortMultipartUploadResource,
)
from resources.usage import UsageResource
from resources.user import EmailCheckResource, UserResource
from resources.user_limits import UserLimitsResource
from resources.account import DeactivateAccountResource
from resources.webhook import StripeWebhookResource
from resources.appsumo import (
    AppSumoWebhookResource,
    AppSumoActivateResource,
)
from resources.share import ShareResource
from resources.youtube import (
    YoutubeInfoResource,
    CreateYoutubeTranscriptionTaskResource,
    YoutubeSubtitleListResource,
    YoutubeSubtitleDownloadResource,
)
from resources.redirect import RedirectResource
from resources.folder import (
    FoldersResource,
    FolderResource,
    FolderTranscriptionsResource,
    BatchTranscriptionFolderResource,
)
from resources.openapi import (
    OpenAPITranscriptionResource,
    OpenAPITranscriptionListResource,
    OpenAPITranscriptionStatusResource,
    OpenAPIYoutubeTranscriptionResource,
)
from resources.api_key import (
    APIKeysResource,
    APIKeyResource,
    APIKeyResetResource,
)

# 导入模型以确保 SQLAlchemy 能够发现它们
from models.folder import Folder  # noqa: F401
from models.api_key import APIKey, WebhookLog  # noqa: F401
from models.user_activity import UserActivity  # noqa: F401


def register_routes(api):
    api.add_resource(ExampleResource, "/example")
    # 生成预签名 url
    api.add_resource(GenerateSignedUrlResource, "/upload/generate-signed-url")
    # 上传完成
    api.add_resource(CompleteUploadResource, "/upload/complete")

    # 分块上传相关端点
    api.add_resource(CreateMultipartUploadResource, "/upload/multipart/create")
    api.add_resource(
        ListMultipartPartsResource, "/upload/multipart/<string:upload_id>/parts"
    )
    api.add_resource(
        SignMultipartPartResource,
        "/upload/multipart/<string:upload_id>/part/<string:part_number>/sign",
    )
    api.add_resource(
        CompleteMultipartUploadResource, "/upload/multipart/<string:upload_id>/complete"
    )
    api.add_resource(
        AbortMultipartUploadResource, "/upload/multipart/<string:upload_id>/abort"
    )
    # 获取所有转录文件
    api.add_resource(TranscriptionsResource, "/transcriptions")
    api.add_resource(TranscriptionsByPageResource, "/transcriptions/page")
    # 获取单个转录文件
    api.add_resource(
        TranscriptionResource,
        "/transcriptions/<int:transcription_file_id>",
        endpoint="transcription_file",
    )
    # 获取单个转录文件状态
    api.add_resource(
        TranscriptionStatusResource,
        "/transcriptions/<int:transcription_file_id>/status",
        endpoint="transcription_status",
    )
    # go service 获取下一个任务
    api.add_resource(
        TaskNextResource,
        "/tasks/next",
        endpoint="task_next",
    )
    # go service 提交任务结果
    api.add_resource(
        TaskResultResource, "/tasks/result", endpoint="task_result", methods=["POST"]
    )
    # 创建转录任务
    api.add_resource(CreateTranscriptionTaskResource, "/tasks/transcription")
    api.add_resource(
        CreateYoutubeTranscriptionTaskResource, "/tasks/youtube-transcription"
    )

    # go service 创建文本处理任务
    api.add_resource(CreateTextTasksResource, "/tasks/text")

    # go service 更新任务状态
    api.add_resource(TaskStatusResource, "/tasks/status")

    # 任务重试接口
    api.add_resource(TaskRetryResource, "/tasks/retry")

    # 任务状态查询接口
    api.add_resource(TaskStatusQueryResource, "/tasks/<int:task_id>/status")

    # 队列监控接口（包含统计和健康检查）
    api.add_resource(QueueStatsResource, "/queue/stats")
    # 导出文本
    api.add_resource(ExportResource, "/export")

    # 批量导出文本
    api.add_resource(BatchExportResource, "/export/batch")

    # 导出outline
    api.add_resource(OutlineExportResource, "/export/outline")

    # 支付
    api.add_resource(StripeCheckoutResource, "/checkout/stripe")
    api.add_resource(CustomerPortalResource, "/customer-portal")

    # webhook
    api.add_resource(StripeWebhookResource, "/webhook/stripe")
    api.add_resource(AppSumoWebhookResource, "/webhook/appsumo")

    # AppSumo OAuth
    api.add_resource(AppSumoActivateResource, "/auth/appsumo/activate")

    # 用户
    api.add_resource(UserResource, "/user")
    api.add_resource(EmailCheckResource, "/user/email-check")
    api.add_resource(DeactivateAccountResource, "/user/deactivate")

    # plan 配置
    api.add_resource(PlanConfigResource, "/plan-config")

    # usage
    api.add_resource(UsageResource, "/usage")

    # user limits
    api.add_resource(UserLimitsResource, "/user/limits")

    # 分享页面
    api.add_resource(
        ShareResource,
        "/transcriptions/<int:transcription_file_id>/share",  # POST endpoint
        "/shares/<string:share_code>",  # GET endpoint
    )
    api.add_resource(ExportShareResource, "/shares/export")
    api.add_resource(SharerPlanConfigResource, "/sharer/plan-config")

    # youtube
    api.add_resource(YoutubeInfoResource, "/tools/youtube-info")
    api.add_resource(YoutubeSubtitleListResource, "/tools/youtube-subtitle-list")
    api.add_resource(
        YoutubeSubtitleDownloadResource, "/tools/youtube-subtitle-download"
    )

    # 删除转录文件
    api.add_resource(
        DeleteTranscriptionResource,
        "/transcriptions/<int:transcription_file_id>",
        endpoint="delete_transcription",
        methods=["DELETE"],
    )

    # 删除原文件但保留转录记录
    api.add_resource(
        DeleteOriginalFileResource,
        "/transcriptions/<int:transcription_file_id>/delete-original-file",
        endpoint="delete_original_file",
        methods=["DELETE"],
    )

    # 批量删除转录文件
    api.add_resource(
        BatchDeleteTranscriptionResource,
        "/transcriptions/batch-delete",
        endpoint="batch_delete_transcription",
        methods=["DELETE"],
    )

    # 搜索转录文件
    api.add_resource(
        TranscriptionSearchResource,
        "/transcriptions/search",
        endpoint="search_transcription",
    )

    # 获取匿名用户最新文件
    api.add_resource(
        AnonymousLatestTranscriptionResource,
        "/transcriptions/anonymous/latest",
        endpoint="anonymous_latest_transcription",
    )
    # 迁移匿名用户的文件到新的登录用户
    api.add_resource(
        MigrateAnonymousTranscriptionResource,
        "/transcriptions/anonymous/migrate",
        endpoint="migrate_anonymous_transcription",
    )

    # 解锁转录文件（付费）
    api.add_resource(
        UnlockTranscriptionResource,
        "/transcriptions/<int:transcription_file_id>/unlock",
        endpoint="unlock_transcription",
    )

    # 权益相关接口
    api.add_resource(EntitlementResource, "/entitlements")

    # 更新单个segment的文本内容
    api.add_resource(
        TranscriptionSegmentResource,
        "/transcriptions/<int:transcription_file_id>/segments/<int:segment_id>",
        endpoint="transcription_segment",
    )

    # 批量更新segments的文本内容
    api.add_resource(
        TranscriptionSegmentsBatchResource,
        "/transcriptions/<int:transcription_file_id>/segments/batch",
        endpoint="transcription_segments_batch",
    )

    # 批量更新segments的说话人信息
    api.add_resource(
        TranscriptionSpeakersBatchResource,
        "/transcriptions/<int:transcription_file_id>/speakers/batch",
        endpoint="transcription_speakers_batch",
    )

    # 重定向服务
    api.add_resource(
        RedirectResource,
        "/go/<string:target>",
        "/go/<string:target>/<string:campaign>",
        endpoint="redirect",
    )

    # 文件夹管理
    api.add_resource(FoldersResource, "/folders")
    api.add_resource(
        FolderResource,
        "/folders/<int:folder_id>",
        endpoint="folder",
    )
    api.add_resource(
        FolderTranscriptionsResource,
        "/folders/<int:folder_id>/transcriptions",
        endpoint="folder_transcriptions",
    )
    # 批量移动转录记录到文件夹
    api.add_resource(
        BatchTranscriptionFolderResource,
        "/transcriptions/batch-folder",
        endpoint="batch_transcription_folder",
        methods=["PATCH"],
    )

    # OpenAPI v1 endpoints
    api.add_resource(
        OpenAPITranscriptionListResource,
        "/api/v1/transcriptions",
        endpoint="openapi_transcriptions",
    )
    api.add_resource(
        OpenAPITranscriptionResource,
        "/api/v1/transcriptions/<int:transcription_id>",
        endpoint="openapi_transcription",
    )
    api.add_resource(
        OpenAPITranscriptionStatusResource,
        "/api/v1/transcriptions/<int:transcription_id>/status",
        endpoint="openapi_transcription_status",
    )
    api.add_resource(
        OpenAPIYoutubeTranscriptionResource,
        "/api/v1/transcriptions/youtube",
        endpoint="openapi_youtube_transcription",
    )

    # API Key management endpoints
    api.add_resource(
        APIKeysResource,
        "/api-keys",
        endpoint="api_keys",
    )
    api.add_resource(
        APIKeyResource,
        "/api-keys/<string:api_key_id>",
        endpoint="api_key",
    )
    api.add_resource(
        APIKeyResetResource,
        "/api-keys/<string:api_key_id>/reset",
        endpoint="api_key_reset",
    )
