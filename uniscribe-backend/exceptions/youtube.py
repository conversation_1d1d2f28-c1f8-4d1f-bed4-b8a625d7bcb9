from .error_codes import ErrorCode
from .base import BaseAPIException


class YoutubeURLInvalidError(BaseAPIException):
    code = 400
    error_code = ErrorCode.YOUTUBE_URL_INVALID


class YoutubeSubtitleNotFoundError(BaseAPIException):
    code = 404
    error_code = ErrorCode.YOUTUBE_SUBTITLE_NOT_FOUND


class YoutubeSubtitleLiveChatOnlyError(BaseAPIException):
    code = 404
    error_code = ErrorCode.YOUTUBE_SUBTITLE_LIVE_CHAT_ONLY


class YoutubeExtractError(BaseAPIException):
    code = 403
    error_code = ErrorCode.YOUTUBE_EXTRACT_ERROR


class YoutubeDownloadError(BaseAPIException):
    """当YouTube视频下载失败时抛出的异常"""

    code = 500
    error_code = ErrorCode.YOUTUBE_DOWNLOAD_ERROR


class YoutubeUploadError(BaseAPIException):
    """当上传到存储服务失败时抛出的异常"""

    code = 500
    error_code = ErrorCode.YOUTUBE_UPLOAD_ERROR


class YoutubeTranscriptionError(BaseAPIException):
    """当转录过程失败时抛出的异常"""

    code = 500
    error_code = ErrorCode.YOUTUBE_TRANSCRIPTION_ERROR
