from flask import jsonify
from werkzeug.exceptions import HTTPException


class BaseAPIException(HTTPException):
    """Base exception for all API errors"""

    code = 400
    error_code = None

    def __init__(self, message=None, details=None):
        if message:
            self.description = message
        else:
            self.description = (
                self.error_code.name if self.error_code else "Unknown Error"
            )
        self.details = details
        super().__init__(description=self.description)

    def get_response(self, environ=None):
        response = jsonify(
            {
                "code": self.error_code.value if self.error_code else None,
                "message": self.description,
                "details": self.details,
            }
        )
        response.status_code = self.code
        return response
