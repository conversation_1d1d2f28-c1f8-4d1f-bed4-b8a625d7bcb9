from .base import BaseAPIException
from .error_codes import ErrorCode


class FileIntegrityError(BaseAPIException):
    """Exception for file integrity verification failures"""

    code = 404
    error_code = ErrorCode.FILE_INTEGRITY_ERROR


class FileNotFoundError(FileIntegrityError):
    """Exception for file not found in storage"""

    code = 404
    error_code = ErrorCode.FILE_NOT_FOUND


class FileSizeMismatchError(FileIntegrityError):
    """Exception for file size mismatch"""

    code = 404
    error_code = ErrorCode.FILE_SIZE_MISMATCH


class ChecksumMismatchError(FileIntegrityError):
    """Exception for MD5 checksum mismatch"""

    code = 404
    error_code = ErrorCode.CHECKSUM_MISMATCH


class FileAlreadyExistsError(BaseAPIException):
    """Exception for duplicate file"""

    code = 403
    error_code = ErrorCode.FILE_ALREADY_EXISTS


class FreeUserFileCountLimitExceededError(BaseAPIException):
    """Exception for free user file count limit exceeded"""

    code = 403
    error_code = ErrorCode.FREE_USER_FILE_COUNT_LIMIT_EXCEEDED
