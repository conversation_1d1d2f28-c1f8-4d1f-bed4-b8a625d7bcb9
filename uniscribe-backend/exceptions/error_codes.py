from enum import Enum, unique


@unique  # 确保枚举值不重复
class ErrorCode(Enum):
    # 通用错误 (10000-19999)
    UNKNOWN = 10000
    INVALID_PARAMS = 10001

    # 用户相关 (20000-20999)
    USER_NOT_FOUND = 20001
    INVALID_PASSWORD = 20002
    DEACTIVATED_ACCOUNT = 20003
    ENTITLEMENT_CREATION_FAILED = 20004
    USER_ACCOUNT_DEACTIVATION_FAILED = 20005

    # 业务相关 (30000-30999)
    FILE_INTEGRITY_ERROR = 30000
    FILE_NOT_FOUND = 30001
    FILE_SIZE_MISMATCH = 30002
    CHECKSUM_MISMATCH = 30003
    FILE_ALREADY_EXISTS = 30004
    FREE_USER_FILE_COUNT_LIMIT_EXCEEDED = 30005
    INSUFFICIENT_TRANSCRIPTION_QUOTA = 30006

    # 视频相关 (40000-40999)
    YOUTUBE_URL_INVALID = 40001
    YOUTUBE_SUBTITLE_NOT_FOUND = 40002
    YOUTUBE_SUBTITLE_LIVE_CHAT_ONLY = 40003
    YOUTUBE_SUBTITLE_SAVE_FAILED = 40004
    YOUTUBE_EXTRACT_ERROR = 40005
    YOUTUBE_DOWNLOAD_ERROR = 40006
    YOUTUBE_UPLOAD_ERROR = 40007
    YOUTUBE_TRANSCRIPTION_ERROR = 40008

    # OpenAPI 相关 (41000-41999)
    API_ACCESS_DENIED = 41000
    INVALID_API_KEY = 41001
    API_KEY_REQUIRED = 41002
    FILE_SIZE_EXCEEDED = 41003
    INVALID_FILE_FORMAT = 41004
    WEBHOOK_DELIVERY_ERROR = 41005
    API_KEY_NOT_FOUND = 41006
    API_KEY_LIMIT_EXCEEDED = 41007
    API_KEY_NAME_EXISTS = 41008
    
    # 任务相关 (45000-45999)
    TASK_NOT_FOUND = 45001
    TASK_PERMISSION_DENIED = 45002
    TASK_STATUS_NOT_RETRYABLE = 45003
    TASK_RETRY_LIMIT_EXCEEDED = 45004
    TASK_FILE_NOT_FOUND = 45005
    TASK_UNSUPPORTED_TYPE = 45006

    # AppSumo 相关 (50000-50999)
    INVALID_APPSUMO_SIGNATURE = 50001
    INVALID_APPSUMO_PAYLOAD = 50002
    MISSING_APPSUMO_EVENT_TYPE = 50003
    UNKNOWN_APPSUMO_EVENT_TYPE = 50004
    APPSUMO_LICENSE_ACTIVATION_ERROR = 50005
    INVALID_APPSUMO_LICENSE = 50006
    APPSUMO_LICENSE_ALREADY_ACTIVATED = 50007
    APPSUMO_OAUTH_ERROR = 50008
    APPSUMO_MULTIPLE_LICENSES_ERROR = 50009
