from .base import BaseAPIException
from .error_codes import ErrorCode
from .decorators import register_exception


@register_exception
class InvalidAppSumoSignatureError(BaseAPIException):
    """无效的 AppSumo webhook 签名"""

    code = 401
    error_code = ErrorCode.INVALID_APPSUMO_SIGNATURE


@register_exception
class InvalidAppSumoPayloadError(BaseAPIException):
    """无效的 AppSumo webhook 请求体"""

    code = 400
    error_code = ErrorCode.INVALID_APPSUMO_PAYLOAD


@register_exception
class MissingAppSumoEventTypeError(BaseAPIException):
    """缺少 AppSumo 事件类型"""

    code = 400
    error_code = ErrorCode.MISSING_APPSUMO_EVENT_TYPE


@register_exception
class UnknownAppSumoEventTypeError(BaseAPIException):
    """未知的 AppSumo 事件类型"""

    code = 400
    error_code = ErrorCode.UNKNOWN_APPSUMO_EVENT_TYPE


@register_exception
class AppSumoLicenseActivationError(BaseAPIException):
    """AppSumo license 激活错误"""

    code = 400
    error_code = ErrorCode.APPSUMO_LICENSE_ACTIVATION_ERROR


@register_exception
class InvalidAppSumoLicenseError(BaseAPIException):
    """无效的 AppSumo license"""

    code = 400
    error_code = ErrorCode.INVALID_APPSUMO_LICENSE


@register_exception
class AppSumoLicenseAlreadyActivatedError(BaseAPIException):
    """AppSumo license 已被其他用户激活"""

    code = 400
    error_code = ErrorCode.APPSUMO_LICENSE_ALREADY_ACTIVATED


@register_exception
class AppSumoMultipleLicensesError(BaseAPIException):
    """用户尝试激活多个 AppSumo license"""

    code = 400
    error_code = ErrorCode.APPSUMO_MULTIPLE_LICENSES_ERROR
    default_message = "Email already exists in our system. You cannot activate multiple AppSumo licenses with the same email."


@register_exception
class AppSumoOAuthError(BaseAPIException):
    """AppSumo OAuth 错误，包括授权码失效、过期等"""

    code = 400
    error_code = ErrorCode.APPSUMO_OAUTH_ERROR
