from .base import BaseAPIException
from .error_codes import ErrorCode
from .decorators import register_exception


@register_exception
class TaskNotFoundError(BaseAPIException):
    """Task not found error"""
    code = 404
    error_code = ErrorCode.TASK_NOT_FOUND


@register_exception
class TaskPermissionDeniedError(BaseAPIException):
    """Task permission denied error"""
    code = 403
    error_code = ErrorCode.TASK_PERMISSION_DENIED


@register_exception
class TaskStatusNotRetryableError(BaseAPIException):
    """Task status does not allow retry error"""
    code = 400
    error_code = ErrorCode.TASK_STATUS_NOT_RETRYABLE


@register_exception
class TaskRetryLimitExceededError(BaseAPIException):
    """Task retry limit exceeded error"""
    code = 400
    error_code = ErrorCode.TASK_RETRY_LIMIT_EXCEEDED


@register_exception
class TaskFileNotFoundError(BaseAPIException):
    """Task associated file not found error"""
    code = 404
    error_code = ErrorCode.TASK_FILE_NOT_FOUND


@register_exception
class TaskUnsupportedTypeError(BaseAPIException):
    """Task type not supported for retry error"""
    code = 400
    error_code = ErrorCode.TASK_UNSUPPORTED_TYPE
