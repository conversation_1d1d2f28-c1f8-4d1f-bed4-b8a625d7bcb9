from .error_codes import <PERSON>rrorCode
from .base import BaseAPIException
from .decorators import register_exception


@register_exception
class AuthenticationError(BaseAPIException):
    code = 401
    error_code = ErrorCode.INVALID_PASSWORD


@register_exception
class UserNotFoundError(BaseAPIException):
    code = 404
    error_code = ErrorCode.USER_NOT_FOUND


@register_exception
class DeactivatedAccountError(BaseAPIException):
    code = 403
    error_code = ErrorCode.DEACTIVATED_ACCOUNT


@register_exception
class EntitlementCreationFailedError(BaseAPIException):
    code = 403
    error_code = ErrorCode.ENTITLEMENT_CREATION_FAILED


@register_exception
class UserAccountDeactivationFailedError(BaseAPIException):
    code = 403
    error_code = ErrorCode.USER_ACCOUNT_DEACTIVATION_FAILED
