"""
异常类装饰器
"""

import sys
from functools import wraps

from .base import BaseAPIException


def register_exception(cls):
    """
    装饰器：自动将异常类注册到exceptions命名空间
    
    用法：
    @register_exception
    class MyException(BaseAPIException):
        code = 400
        error_code = ErrorCode.MY_ERROR
    """
    # 确保被装饰的类是BaseAPIException的子类
    if not issubclass(cls, BaseAPIException):
        raise TypeError(f"{cls.__name__} must be a subclass of BaseAPIException")
    
    # 将异常类添加到exceptions模块的命名空间
    import exceptions
    setattr(exceptions, cls.__name__, cls)
    
    return cls
