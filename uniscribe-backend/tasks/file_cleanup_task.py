"""
文件清理定时任务

该任务负责：
1. 清理免费用户超过30天未访问的文件
2. 清理引用计数为0的文件
"""

import logging
from datetime import datetime, timedelta


from models.user import User
from models import db_transaction
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from constants.file_storage import FileState
from flask import current_app

logger = logging.getLogger(__name__)


# def cleanup_expired_files():
#     """清理免费用户超过30天未访问的文件"""
#     logger.info("Starting cleanup of expired files...")

#     # 获取存储接口
#     storage = current_app.storage

#     # 设置时间阈值
#     thirty_days_ago = datetime.now() - timedelta(days=30)

#     # 分批处理参数
#     user_batch_size = 1000  # 每批查询的用户数
#     file_batch_size = 500  # 每批处理的文件数
#     total_expired_files = 0
#     total_deleted_files = 0
#     total_errors = 0
#     total_free_users = 0

#     # 分批查询用户
#     user_offset = 0

#     while True:
#         # 分批查询用户
#         batch_users = User.query.limit(user_batch_size).offset(user_offset).all()

#         if not batch_users:
#             break  # 没有更多用户了

#         # 筛选出这一批中的免费用户
#         batch_free_users = [user for user in batch_users if not user.has_paid_plan]
#         batch_free_user_ids = [user.id for user in batch_free_users]
#         total_free_users += len(batch_free_user_ids)

#         logger.info(
#             "Processing batch of %d users, found %d free users",
#             len(batch_users),
#             len(batch_free_user_ids),
#         )

#         # 如果这一批中没有免费用户，继续下一批
#         if not batch_free_user_ids:
#             user_offset += user_batch_size
#             continue

#         # 处理这一批免费用户
#         batch_user_count = len(batch_free_user_ids)
#         batch_user_index = 0

#         # 分批处理这一批免费用户的文件
#         while batch_user_index < batch_user_count:
#             # 取当前分批的用户ID
#             current_batch_user_ids = batch_free_user_ids[
#                 batch_user_index : batch_user_index + file_batch_size
#             ]
#             batch_user_index += file_batch_size

#             with db_transaction():
#                 # 查询这批用户的过期文件
#                 expired_files = FileStorage.query.filter(
#                     FileStorage.user_id.in_(current_batch_user_ids),
#                     FileStorage.last_access_time < thirty_days_ago,
#                     FileStorage.state == FileState.ACTIVE.value,  # 只查询活跃状态的文件
#                 ).all()

#                 batch_expired_files = len(expired_files)
#                 total_expired_files += batch_expired_files
#                 logger.info(
#                     "Found %d expired files for user batch %d-%d",
#                     batch_expired_files,
#                     batch_user_index - file_batch_size + 1,
#                     min(batch_user_index, batch_user_count),
#                 )

#                 # 处理过期文件
#                 batch_deleted = 0
#                 batch_errors = 0
#                 for file in expired_files:
#                     try:
#                         # 从存储中删除文件
#                         try:
#                             storage.permanently_delete_file(file.file_key)
#                             logger.info("Deleted expired file: %s", file.file_key)

#                             # 成功删除后，更新文件状态为已删除
#                             file.state = FileState.DELETED.value
#                             batch_deleted += 1
#                         except Exception as e:
#                             logger.error(
#                                 "Error deleting expired file %s: %s",
#                                 file.file_key,
#                                 str(e),
#                             )
#                             batch_errors += 1

#                         # 标记关联的转录记录为已删除
#                         TranscriptionFile.query.filter(
#                             TranscriptionFile.user_id == file.user_id,
#                             TranscriptionFile.fingerprint == file.fingerprint,
#                             TranscriptionFile.is_deleted == False,
#                         ).update({"is_deleted": True})

#                     except Exception as e:
#                         logger.error(
#                             "Error processing expired file %d: %s", file.id, str(e)
#                         )
#                         batch_errors += 1

#                 # 更新统计信息
#                 total_deleted_files += batch_deleted
#                 total_errors += batch_errors
#                 logger.info(
#                     "Batch completed: %d files deleted, %d errors",
#                     batch_deleted,
#                     batch_errors,
#                 )

#     # 更新用户偏移量，准备处理下一批用户
#     user_offset += user_batch_size

#     # 输出总结果
#     logger.info(
#         "Expired files cleanup completed: %d files found, %d files deleted, %d errors, %d free users processed",
#         total_expired_files,
#         total_deleted_files,
#         total_errors,
#         total_free_users,
#     )


def cleanup_unreferenced_files():
    """清理引用计数为0的文件"""
    logger.info("Starting cleanup of unreferenced files...")

    # 获取存储接口
    storage = current_app.storage

    # 分批查询和处理引用计数为0的文件
    batch_size = 500  # 每批处理的文件数
    total_processed = 0
    total_deleted = 0
    total_errors = 0

    # 获取待删除文件的总数
    pending_deletion_count = FileStorage.query.filter(
        FileStorage.state == FileState.PENDING_DELETION.value
    ).count()
    logger.info("Found %d files pending deletion", pending_deletion_count)

    while True:
        with db_transaction():
            # 查询一批待删除状态的文件，始终从头开始查询
            zero_ref_files = (
                FileStorage.query.filter(
                    FileStorage.state
                    == FileState.PENDING_DELETION.value  # 待删除状态的文件
                )
                .limit(batch_size)
                .all()
            )

            if not zero_ref_files:
                break  # 没有更多文件了，结束循环

            batch_count = len(zero_ref_files)
            logger.info(
                "Processing batch of %d unreferenced files (total processed so far: %d)",
                batch_count,
                total_processed,
            )

            batch_deleted = 0
            batch_errors = 0
            for file in zero_ref_files:
                try:
                    # 从存储中删除文件
                    try:
                        storage.permanently_delete_file(file.file_key)
                        logger.info("Deleted unreferenced file: %s", file.file_key)

                        # 成功删除后，更新文件状态为已删除
                        file.state = FileState.DELETED.value
                        batch_deleted += 1
                    except Exception as e:
                        logger.error(
                            "Error deleting unreferenced file %s: %s",
                            file.file_key,
                            str(e),
                        )
                        batch_errors += 1

                except Exception as e:
                    logger.error(
                        "Error processing unreferenced file %d: %s", file.id, str(e)
                    )
                    batch_errors += 1

            total_processed += batch_count
            total_deleted += batch_deleted
            total_errors += batch_errors

            logger.info(
                "Batch completed: %d files deleted, %d errors, %d/%d total processed",
                batch_deleted,
                batch_errors,
                total_processed,
                pending_deletion_count,
            )

    logger.info(
        "Unreferenced files cleanup completed: %d files processed, %d files deleted, %d errors",
        total_processed,
        total_deleted,
        total_errors,
    )


def run_file_cleanup():
    """运行文件清理任务"""
    logger.info("Starting file cleanup task...")

    try:
        # 清理过期文件
        # cleanup_expired_files()

        # 清理引用计数为0的文件
        cleanup_unreferenced_files()

        logger.info("File cleanup task completed successfully")
    except Exception as e:
        logger.error("Error in file cleanup task: %s", str(e))
        raise  # 重新抛出异常，让调用者知道发生了错误
