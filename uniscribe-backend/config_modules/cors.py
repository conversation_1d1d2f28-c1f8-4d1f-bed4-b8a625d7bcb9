"""CORS 配置模块"""

from flask_cors import CORS
from config import is_development_env


def setup_cors(app):
    """设置 CORS 配置"""
    # 定义需要暴露给前端的响应头
    exposed_headers = [
        "Content-Disposition",
        "X-Export-Total",
        "X-Export-Successful",
        "X-Export-Failed",
        "X-Export-Has-Failures",
        "X-Export-Errors"
    ]

    if is_development_env():
        CORS(
            app,
            supports_credentials=True,
            allow_headers="*",
            origins="*",
            expose_headers=exposed_headers
        )
    else:
        CORS(
            app,
            origins=[
                "https://shiyin-web.vercel.app",
                "https://www.uniscribe.co",
                "https://uniscribe.co",
                r"^https:\/\/uniscribe-.*\.vercel\.app$",
            ],
            supports_credentials=True,
            expose_headers=exposed_headers,
        )
