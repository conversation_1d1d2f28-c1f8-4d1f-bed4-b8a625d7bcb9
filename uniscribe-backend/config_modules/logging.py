"""日志配置模块"""

import logging
import atexit
from libs.logging import setup_logging


def setup_app_logging(config):
    """设置应用日志"""
    # 初始化日志，保存 handler 引用
    axiom_handler = setup_logging(dataset_name=config.AXIOM.get("dataset_name"))
    logger = logging.getLogger(__name__)
    logger.info("Application started")

    # 注册退出处理
    def handle_exit():
        logger.info("Application shutting down, flushing remaining logs...")
        axiom_handler.client.flush()

    atexit.register(handle_exit)
    
    # 禁用 werkzeug 的默认日志
    logging.getLogger("werkzeug").disabled = True
    
    return logger
