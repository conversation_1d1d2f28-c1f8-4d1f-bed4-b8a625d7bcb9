#!/bin/bash

echo "Building Docker image..."

# 方法1: 尝试使用代理构建
echo "=== Attempting build with proxy ==="
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
export all_proxy=socks5://127.0.0.1:7890

if docker build -t flask-boilerplate \
  --network=host \
  --build-arg http_proxy=$http_proxy \
  --build-arg https_proxy=$https_proxy \
  --build-arg all_proxy=$all_proxy \
  .; then
    echo "Build with proxy succeeded!"
    exit 0
fi

# 方法2: 如果代理构建失败，尝试不使用代理
echo "=== Proxy build failed, trying without proxy ==="
unset https_proxy
unset http_proxy
unset all_proxy

if docker build -t flask-boilerplate \
  --build-arg http_proxy="" \
  --build-arg https_proxy="" \
  --build-arg all_proxy="" \
  .; then
    echo "Build without proxy succeeded!"
    exit 0
else
    echo "Both builds failed!"
    exit 1
fi