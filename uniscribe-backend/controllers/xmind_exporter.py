"""
XMind导出器模块
用于将Markdown格式的outline内容转换为XMind思维导图文件
"""

import tempfile
import os
import zipfile
import xml.etree.ElementTree as ET
from typing import List, Dict, Any
from datetime import datetime
import uuid
from markdown_it import MarkdownIt


class MarkdownToXMindConverter:
    """Markdown到XMind转换器"""

    def __init__(self):
        self.topics_hierarchy = []

    def _create_manifest_xml(self) -> str:
        """创建manifest.xml文件"""
        return '''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0" password-hint="">
  <file-entry full-path="content.xml" media-type="text/xml"/>
  <file-entry full-path="META-INF/" media-type=""/>
  <file-entry full-path="meta.xml" media-type="text/xml"/>
</manifest>'''

    def _create_meta_xml(self) -> str:
        """创建meta.xml文件"""
        timestamp = datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%fZ')
        return f'''<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<meta xmlns="urn:xmind:xmap:xmlns:meta:2.0" version="2.0">
  <Author>
    <Name>Uniscribe XMind Exporter</Name>
  </Author>
  <Create>
    <Time>{timestamp}</Time>
  </Create>
</meta>'''

    def _create_revisions_xml(self, sheet_id: str) -> str:
        """创建revisions.xml文件"""
        return f'''<?xml version="1.0" encoding="UTF-8"?>\n<xmap-revisions media-type="application/vnd.xmind.sheet" next-rev-num="1" resource-id="{sheet_id}"/>'''

    def _build_topic_hierarchy_from_tree(self, parent_elem, node):
        """
        递归将树结构转为 XMind topic 节点
        parent_elem: xml.etree.ElementTree.Element
        node: dict，包含 title, type, children
        """
        for child in node.get('children', []):
            topic_elem = ET.SubElement(parent_elem, 'topic', {'id': self._generate_id()})
            title_elem = ET.SubElement(topic_elem, 'title')
            title_elem.text = child['title']
            if child.get('children'):
                children_elem = ET.SubElement(topic_elem, 'children')
                topics_elem = ET.SubElement(children_elem, 'topics', {'type': 'attached'})
                self._build_topic_hierarchy_from_tree(topics_elem, child)

    def _generate_id(self) -> str:
        """生成唯一ID"""
        return str(uuid.uuid4()).replace('-', '')[:26]


def create_xmind_from_markdown(markdown_content: str, use_new_parser: bool = True) -> bytes:
    """
    从Markdown内容创建XMind文件
    Args:
        markdown_content (str): Markdown格式的内容
        use_new_parser (bool): 是否使用新的 markdown-it-py 解析方式（已废弃，仅保留新方法）
    Returns:
        bytes: XMind文件的二进制内容
    """
    converter = MarkdownToXMindConverter()
    tree = parse_markdown_to_tree(markdown_content)
    # 生成唯一id
    sheet_id = converter._generate_id()
    root_topic_id = converter._generate_id()
    import xml.etree.ElementTree as ET
    import tempfile, os, zipfile
    temp_file = tempfile.NamedTemporaryFile(suffix='.xmind', delete=False)
    temp_file_path = temp_file.name
    temp_file.close()
    try:
        with zipfile.ZipFile(temp_file_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # 1. 创建META-INF/manifest.xml
            manifest_xml = converter._create_manifest_xml()
            zip_file.writestr('META-INF/manifest.xml', manifest_xml)
            # 2. 创建content.xml (主要内容)
            root = ET.Element('xmap-content', {
                'xmlns': 'urn:xmind:xmap:xmlns:content:2.0',
                'xmlns:fo': 'http://www.w3.org/1999/XSL/Format',
                'xmlns:svg': 'http://www.w3.org/2000/svg',
                'xmlns:xhtml': 'http://www.w3.org/1999/xhtml',
                'xmlns:xlink': 'http://www.w3.org/1999/xlink',
                'version': '2.0'
            })
            sheet = ET.SubElement(root, 'sheet', {
                'id': sheet_id,
                'theme': 'default',
                'root-topic': root_topic_id
            })
            root_topic = ET.SubElement(sheet, 'topic', {
                'id': root_topic_id,
                'structure-class': 'org.xmind.ui.map.unbalanced'
            })
            title_elem = ET.SubElement(root_topic, 'title')
            title_elem.text = tree['title']
            if tree.get('children'):
                children_elem = ET.SubElement(root_topic, 'children')
                topics_elem = ET.SubElement(children_elem, 'topics', {'type': 'attached'})
                converter._build_topic_hierarchy_from_tree(topics_elem, tree)
            ET.indent(root, space="  ", level=0)
            content_xml = '<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n' + ET.tostring(root, encoding='unicode')
            zip_file.writestr('content.xml', content_xml)
            # 3. 创建meta.xml (元数据)
            meta_xml = converter._create_meta_xml()
            zip_file.writestr('meta.xml', meta_xml)
            # 4. 创建revisions/SHEET_ID/revisions.xml
            revisions_xml = converter._create_revisions_xml(sheet_id)
            revisions_path = f'revisions/{sheet_id}/revisions.xml'
            zip_file.writestr(revisions_path, revisions_xml)
        with open(temp_file_path, 'rb') as f:
            file_content = f.read()
        return file_content
    finally:
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

def parse_markdown_to_tree(markdown_content: str) -> dict:
    """
    使用 markdown-it-py 解析 markdown，生成以 h1 或空字符串为根节点的树结构。
    1. 如果有且仅有一个 h1，用其为 root，所有 heading 层级整体上提一级。
    2. 否则 root.title 为空，heading 层级不变。
    返回格式：
    {
        'title': 'Outline' 或 h1内容,
        'type': 'root',
        'children': [ ... ]
    }
    """
    md = MarkdownIt()
    tokens = md.parse(markdown_content)

    # 先统计 h1 数量和内容
    h1_titles = []
    for i, token in enumerate(tokens):
        if token.type == 'heading_open' and token.tag == 'h1':
            if i + 1 < len(tokens):
                h1_titles.append(tokens[i + 1].content.strip())

    if len(h1_titles) == 1:
        root_title = h1_titles[0]
        offset = 1
    else:
        root_title = ''
        offset = 0

    root = {
        'title': root_title,
        'type': 'root',
        'children': []
    }
    heading_stack = [root]
    for idx, token in enumerate(tokens):
        if token.type == 'heading_open':
            level = int(token.tag[1]) - offset
            # 跳过唯一h1作为根节点的情况
            if offset == 1 and int(token.tag[1]) == 1:
                continue
            if level < 1:
                continue
            next_token = tokens[idx + 1]
            title = next_token.content.strip()
            node = {'title': title, 'type': 'heading', 'children': []}
            # 找到合适的父节点
            while len(heading_stack) > level:
                heading_stack.pop()
            heading_stack[-1]['children'].append(node)
            heading_stack.append(node)
        elif token.type == 'list_item_open':
            for j in range(idx + 1, len(tokens)):
                if tokens[j].type == 'inline':
                    title = tokens[j].content.strip()
                    break
            else:
                title = ''
            node = {'title': title, 'type': 'list_item', 'children': []}
            parent = heading_stack[-1]
            if parent['children'] and parent['children'][-1]['type'] == 'list_item' and not hasattr(parent['children'][-1], '_closed'):
                parent['children'][-1]['children'].append(node)
            else:
                parent['children'].append(node)
    return root
