import os
from abc import ABC, abstractmethod
from io import BytesIO
from urllib.parse import quote
import zipfile
from datetime import datetime
from controllers.xmind_exporter import create_xmind_from_markdown


from flask import send_file, make_response
from flask_restful import abort
from docx import Document
from docx.shared import Pt
import csv
from io import StringIO


from models.task_result import TaskResult
from models.transcription_file import TranscriptionFile
from models.share import Share


def process_insufficient_minutes(transcription_file, segments):
    """
    处理不足分钟数的转录文件，过滤segments并添加升级提示

    Args:
        transcription_file: 转录文件对象
        segments: 原始segments列表

    Returns:
        处理后的segments列表
    """
    if transcription_file.insufficient_minutes <= 0:
        return segments

    # 计算截止时间
    cutoff_time = (
        transcription_file.duration - transcription_file.insufficient_minutes * 60
    )

    # 过滤segments，只保留截止时间内的内容
    filtered_segments = [
        seg for seg in segments if seg.get("end_time", 0) <= cutoff_time
    ]

    # 添加升级提示消息
    notification_msg = {
        "start_time": cutoff_time,
        "end_time": cutoff_time + 1,
        "text": (
            f"You've run out of transcription time. This content has "
            f"{transcription_file.insufficient_minutes} more minutes that need to be unlocked. "
            f"Upgrade to see everything."
        ),
    }
    filtered_segments.append(notification_msg)

    return filtered_segments


def generate_unique_filename(base_filename, file_type, used_filenames):
    """
    生成唯一的文件名，避免ZIP包中的文件名冲突

    Args:
        base_filename: 原始文件名（不含扩展名）
        file_type: 文件类型扩展名
        used_filenames: 已使用的文件名集合

    Returns:
        唯一的文件名
    """
    filename = f"{base_filename}.{file_type}"

    # 如果文件名没有冲突，直接返回
    if filename not in used_filenames:
        used_filenames.add(filename)
        return filename

    # 如果有冲突，添加数字后缀
    counter = 1
    while True:
        # 在文件名和扩展名之间插入数字
        unique_filename = f"{base_filename}_{counter}.{file_type}"
        if unique_filename not in used_filenames:
            used_filenames.add(unique_filename)
            return unique_filename
        counter += 1


class Export(ABC):
    @abstractmethod
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        pass

    def _format_timestamp(self, start_time):
        """格式化时间戳为 mm:ss 格式"""
        minutes = int(start_time) // 60
        seconds = int(start_time) % 60
        return f"{int(minutes):02d}:{int(seconds):02d}"

    def _format_segment_content(
        self, segment, show_speaker_name=True, show_timestamps=True
    ):
        """格式化每个片段的内容"""
        speaker = segment.get("speaker", "Speaker")
        text = segment.get("text", "").strip()

        output = []

        # 构建头部信息（说话人和时间戳）
        header_parts = []
        if show_speaker_name and speaker:
            header_parts.append(speaker)
        if show_timestamps:
            timestamp = self._format_timestamp(segment.get("start_time", 0))
            header_parts.append(timestamp)

        # 如果有头部信息，添加到输出
        if header_parts:
            output.append("  ".join(header_parts))

        if text:
            output.append(text)
            output.append("")  # Empty line between segments

        return output


class PDFExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        # 使用 WeasyPrint 生成 PDF，完美支持多语言
        import logging
        from weasyprint import HTML, CSS
        from weasyprint.text.fonts import FontConfiguration

        logger = logging.getLogger(__name__)
        logger.info("Using WeasyPrint to generate multilingual PDF")

        # Suppress verbose fontTools.subset logging during PDF generation
        fonttools_logger = logging.getLogger("fontTools.subset")
        original_level = fonttools_logger.level
        fonttools_logger.setLevel(logging.WARNING)

        # 生成 HTML 内容
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Transcription Export</title>
        </head>
        <body>
        """

        # 处理转录数据
        for segment in data:
            content_lines = self._format_segment_content(
                segment, show_speaker_name, show_timestamps
            )
            for line in content_lines:
                if line.strip():
                    # HTML 转义特殊字符
                    from html import escape

                    safe_line = escape(line)
                    html_content += f"<p>{safe_line}</p>\n"

        html_content += """
        </body>
        </html>
        """

        # CSS 样式 - 使用多个字体作为回退
        css_content = """
        @page {
            margin: 2cm;
            size: A4;
        }

        body {
            font-family:
                'Noto Sans CJK SC',
                'Noto Sans CJK TC',
                'Noto Sans CJK JP',
                'Noto Sans CJK KR',
                'WenQuanYi Zen Hei',
                'WenQuanYi Micro Hei',
                'Takao PGothic',
                'Nanum Gothic',
                'Noto Sans',
                'DejaVu Sans',
                'Arial Unicode MS',
                Arial,
                sans-serif;
            font-size: 10pt;
            line-height: 1.6;
            color: #333;
        }

        p {
            margin-bottom: 6pt;
            margin-top: 0;
        }

        /* 确保所有 Unicode 字符都能正确显示 */
        * {
            font-variant-ligatures: none;
        }
        """

        try:
            # 创建字体配置
            font_config = FontConfiguration()

            # 生成 PDF
            html_doc = HTML(string=html_content)
            css_doc = CSS(string=css_content, font_config=font_config)

            logger.info("Starting PDF generation...")
            pdf_content = html_doc.write_pdf(
                stylesheets=[css_doc], font_config=font_config
            )
            logger.info(f"PDF generation successful, size: {len(pdf_content)} bytes")
            return pdf_content

        except Exception as e:
            logger.error(f"WeasyPrint PDF generation failed: {str(e)}")
            # If WeasyPrint fails, fallback to simple HTML content
            try:
                simple_html = f"<html><body style='font-family: Arial, sans-serif; font-size: 10pt;'>{html_content}</body></html>"
                html_doc = HTML(string=simple_html)
                pdf_content = html_doc.write_pdf()
                logger.info("Fallback PDF generation successful")
                return pdf_content
            except Exception as fallback_error:
                logger.error(f"Fallback version also failed: {str(fallback_error)}")
                raise Exception(f"PDF generation completely failed: {str(e)}")
        finally:
            # Restore original fontTools logging level
            fonttools_logger.setLevel(original_level)


class DocxExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        doc = Document()
        # 使用默认字体，让系统自动选择合适的字体

        for segment in data:
            content = self._format_segment_content(
                segment, show_speaker_name, show_timestamps
            )
            for line in content:
                if line:  # Add non-empty lines as paragraphs
                    paragraph = doc.add_paragraph(line)
                    # 只设置字体大小，不指定字体名称
                    if paragraph.runs:
                        run = paragraph.runs[0]
                        run.font.size = Pt(10)
                else:  # Add empty lines as paragraph breaks between segments
                    doc.add_paragraph("")

        temp_filename = "temp_output.docx"
        doc.save(temp_filename)

        with open(temp_filename, "rb") as file:
            docx_content = file.read()

        os.remove(temp_filename)
        return docx_content


class TxtExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        output = []
        for segment in data:
            output.extend(
                self._format_segment_content(
                    segment, show_speaker_name, show_timestamps
                )
            )
        return "\n".join(output).encode("utf-8")


class SRTExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        srt_content = ""
        for index, item in enumerate(data, start=1):
            start_time = self._format_time(item["start_time"])
            end_time = self._format_time(item["end_time"])

            # 构建字幕文本
            text = item["text"]
            if show_speaker_name and item.get("speaker"):
                text = f"{item['speaker']}: {text}"

            srt_content += f"{index}\n{start_time} --> {end_time}\n{text}\n\n"
        return srt_content.encode("utf-8")

    def _format_time(self, seconds):
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        milliseconds = int((seconds - int(seconds)) * 1000)
        return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"


class VTTExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        vtt_content = "WEBVTT\n\n"  # WebVTT header
        for item in data:
            start_time = self._format_time(item["start_time"])
            end_time = self._format_time(item["end_time"])
            text = item["text"]

            # 如果有说话人，添加到字幕中
            if show_speaker_name and item.get("speaker"):
                text = f"{item['speaker']}: {text}"

            vtt_content += f"{start_time} --> {end_time}\n{text}\n\n"
        return vtt_content.encode("utf-8")

    def _format_time(self, seconds):
        """格式化时间为 WebVTT 格式 (HH:MM:SS.mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds_remainder = seconds % 60
        milliseconds = int((seconds_remainder - int(seconds_remainder)) * 1000)
        return (
            f"{hours:02d}:{minutes:02d}:{int(seconds_remainder):02d}.{milliseconds:03d}"
        )


class CSVExport(Export):
    def export(self, data, show_speaker_name=True, show_timestamps=True):
        output = StringIO()
        writer = csv.writer(output)

        # Write CSV headers in English
        headers = []
        if show_timestamps:
            headers.extend(["Start Time", "End Time"])
        if show_speaker_name:
            headers.append("Speaker")
        headers.append("Text")
        writer.writerow(headers)

        # Write data rows
        for segment in data:
            row = []
            if show_timestamps:
                row.extend(
                    [
                        self._format_time(segment.get("start_time", 0)),
                        self._format_time(segment.get("end_time", 0)),
                    ]
                )
            if show_speaker_name:
                row.append(segment.get("speaker", "Speaker"))
            row.append(segment.get("text", "").strip())
            writer.writerow(row)

        return output.getvalue().encode("utf-8-sig")  # Use BOM for Excel compatibility

    def _format_time(self, seconds):
        """Format time as HH:MM:SS.mmm"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds_remainder = seconds % 60
        milliseconds = int((seconds_remainder - int(seconds_remainder)) * 1000)
        return (
            f"{hours:02d}:{minutes:02d}:{int(seconds_remainder):02d}.{milliseconds:03d}"
        )


# 根据文件类型选择不同的导出格式
def get_exporter(file_type):
    if file_type == "pdf":
        return PDFExport()
    elif file_type == "docx":
        return DocxExport()
    elif file_type == "txt":
        return TxtExport()
    elif file_type == "srt":
        return SRTExport()
    elif file_type == "vtt":  # 添加 VTT 支持
        return VTTExport()
    elif file_type == "csv":
        return CSVExport()
    else:
        raise NotImplementedError(f"Unsupported file type: {file_type}")


def create_download_response(
    file_content, filename, mime_type="application/octet-stream"
):
    """
    创建文件下载响应

    Args:
        file_content (bytes): 文件内容
        filename (str): 文件名
        mime_type (str, optional): MIME类型. 默认为 "application/octet-stream"

    Returns:
        Response: Flask响应对象
    """
    # 创建一个BytesIO对象
    file_buffer = BytesIO(file_content)
    file_buffer.seek(0)

    filename_encoded = quote(filename)

    # 创建响应
    response = make_response(
        send_file(
            file_buffer,
            mimetype=mime_type,
            as_attachment=True,
            download_name=filename_encoded,
        )
    )

    # 设置不缓存的头部
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    # 设置 Content-Disposition 头（CORS expose_headers 由全局配置处理）
    response.headers["Content-Disposition"] = (
        f"attachment; filename*=UTF-8''{filename_encoded}"
    )

    return response


def export_text(file_type, file_id, show_speaker_name=True, show_timestamps=True):
    exporter = get_exporter(file_type)

    # 将字符串 ID 转换为整数
    try:
        file_id_int = int(file_id)
    except (ValueError, TypeError):
        abort(400, message="Invalid file ID")

    transcription_file = TranscriptionFile.get_by_id(file_id_int)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    task_result = TaskResult.get_by_file_id(file_id_int)
    if not task_result:
        abort(404, message="Task result not found")

    filename = f"{transcription_file.filename}.{file_type}"

    # 处理不足分钟数的情况
    segments = process_insufficient_minutes(transcription_file, task_result.segments)

    file_content = exporter.export(segments, show_speaker_name, show_timestamps)

    return create_download_response(file_content, filename)


def export_outline(format_type, transcription_id, user_id):
    """
    导出单个转录文件的outline内容

    Args:
        format_type (str): 导出格式，支持 'md', 'xmind'
        transcription_id (str): 转录文件ID
        user_id (int): 用户ID，用于权限验证

    Returns:
        Response: Flask响应对象，包含outline文件内容

    Raises:
        400: 无效的文件ID或格式
        403: 权限不足
        404: 文件不存在或没有outline内容
    """
    # 验证格式类型
    supported_formats = ['md', 'xmind']
    if format_type not in supported_formats:
        abort(400, message=f"Unsupported format type: {format_type}")

    # 将字符串 ID 转换为整数
    try:
        transcription_id_int = int(transcription_id)
    except (ValueError, TypeError):
        abort(400, message="Invalid transcription ID")

    # 获取转录文件并验证权限
    transcription_file = TranscriptionFile.get_by_id(transcription_id_int)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    # 验证用户权限
    # 检查文件是否属于当前用户，或者是否被分享
    if transcription_file.user_id != user_id:
        # 如果不属于当前用户，检查是否被分享
        share = Share.get_by_file_id(transcription_id_int)
        if not share:
            abort(403, message="Access denied")

    # 获取任务结果
    task_result = TaskResult.get_by_file_id(transcription_id_int)
    if not task_result:
        abort(404, message="Task result not found")

    # 检查是否有outline内容
    if not task_result.outline or task_result.outline.strip() == "":
        abort(404, message="Outline content not found")

    # 生成文件名：filename_yymmdd_hhmmss_mindmap.format
    now = datetime.now()
    date_str = now.strftime("%y%m%d")
    time_str = now.strftime("%H%M%S")

    # 移除原文件名的扩展名
    base_filename = transcription_file.filename
    if '.' in base_filename:
        base_filename = base_filename.rsplit('.', 1)[0]

    filename = f"{base_filename}_{date_str}_{time_str}_mindmap.{format_type}"

    # 根据格式类型处理内容
    if format_type == 'md':
        file_content = task_result.outline.encode('utf-8')
        mime_type = "text/markdown"
    elif format_type == 'xmind':
        # 导入XMind转换器
        try:
            file_content = create_xmind_from_markdown(task_result.outline)
            mime_type = "application/vnd.xmind.workbook"
        except Exception as e:
            abort(500, message=f"Failed to generate XMind file: {str(e)}")

    return create_download_response(file_content, filename, mime_type)


def export_batch_text(file_type, file_ids, user_id, show_speaker_name=True, show_timestamps=True):
    """批量导出转录文件"""
    exporter = get_exporter(file_type)

    # 初始化结果统计
    export_result = {
        "successful": 0,
        "failed": 0,
        "errors": [],
        "valid_files": []
    }

    # 1. 首先过滤出有效的文件ID
    valid_file_ids = []
    for file_id in file_ids:
        try:
            file_id_int = int(file_id)
            valid_file_ids.append(file_id_int)
        except (ValueError, TypeError):
            export_result["failed"] += 1
            export_result["errors"].append({
                "fileId": str(file_id),
                "error": "Invalid file ID format"
            })
            continue

    if not valid_file_ids:
        abort(400, message="No valid file IDs provided")

    # 2. 批量查询转录文件（只查询属于当前用户的文件）
    transcription_files = TranscriptionFile.query.filter(
        TranscriptionFile.id.in_(valid_file_ids),
        TranscriptionFile.user_id == user_id,
        TranscriptionFile.is_deleted == False
    ).all()

    # 3. 批量查询任务结果
    transcription_file_ids = [tf.id for tf in transcription_files]
    task_results = TaskResult.query.filter(
        TaskResult.file_id.in_(transcription_file_ids)
    ).all()

    # 4. 创建映射字典，便于快速查找
    transcription_file_map = {tf.id: tf for tf in transcription_files}
    task_result_map = {tr.file_id: tr for tr in task_results}

    # 5. 处理每个请求的文件ID，记录成功和失败的情况
    for file_id_int in valid_file_ids:
        try:
            transcription_file = transcription_file_map.get(file_id_int)
            if not transcription_file:
                export_result["failed"] += 1
                export_result["errors"].append({
                    "fileId": str(file_id_int),
                    "error": "Transcription file not found or access denied"
                })
                continue

            task_result = task_result_map.get(file_id_int)
            if not task_result:
                export_result["failed"] += 1
                export_result["errors"].append({
                    "fileId": str(file_id_int),
                    "error": "Task result not found"
                })
                continue

            # 成功找到文件和结果
            export_result["valid_files"].append((transcription_file, task_result))
            export_result["successful"] += 1

        except Exception as e:
            export_result["failed"] += 1
            export_result["errors"].append({
                "fileId": str(file_id_int),
                "error": str(e)
            })

    # 6. 检查是否有可导出的文件
    if not export_result["valid_files"]:
        abort(404, message="No exportable transcription files found")

    valid_files = export_result["valid_files"]

    # 如果只有一个文件，直接返回单个文件（但仍需要添加批量导出的统计响应头）
    if len(valid_files) == 1:
        transcription_file, task_result = valid_files[0]
        filename = f"{transcription_file.filename}.{file_type}"

        # 处理不足分钟数的情况
        segments = process_insufficient_minutes(transcription_file, task_result.segments)

        file_content = exporter.export(segments, show_speaker_name, show_timestamps)
        response = create_download_response(file_content, filename)

        # 添加批量导出统计信息到响应头（即使只有一个文件）
        response.headers["X-Export-Total"] = str(len(file_ids))
        response.headers["X-Export-Successful"] = str(export_result["successful"])
        response.headers["X-Export-Failed"] = str(export_result["failed"])

        # 如果有失败的文件，添加到响应头
        if export_result["failed"] > 0:
            response.headers["X-Export-Has-Failures"] = "true"
            import json
            error_summary = [{"fileId": err["fileId"], "error": err["error"]} for err in export_result["errors"]]
            response.headers["X-Export-Errors"] = json.dumps(error_summary)[:1000]

        return response

    # 多个文件时创建ZIP包
    zip_buffer = BytesIO()
    failed_in_zip = []
    used_filenames = set()  # 跟踪已使用的文件名，避免重复

    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for transcription_file, task_result in valid_files:
            try:
                # 生成唯一的文件名，避免重复
                filename = generate_unique_filename(
                    transcription_file.filename,
                    file_type,
                    used_filenames
                )

                # 处理不足分钟数的情况
                segments = process_insufficient_minutes(transcription_file, task_result.segments)

                # 导出单个文件内容
                file_content = exporter.export(segments, show_speaker_name, show_timestamps)

                # 添加到ZIP文件中
                zip_file.writestr(filename, file_content)

            except Exception as e:
                # 记录ZIP打包过程中的失败
                failed_in_zip.append({
                    "fileId": str(transcription_file.id),
                    "filename": transcription_file.filename,
                    "error": f"Export failed: {str(e)}"
                })
                export_result["failed"] += 1
                export_result["successful"] -= 1  # 之前计算为成功，现在需要调整
                export_result["errors"].append({
                    "fileId": str(transcription_file.id),
                    "error": f"Export failed during ZIP creation: {str(e)}"
                })

    zip_buffer.seek(0)

    # 生成带时间戳和文件数量的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    successful_count = export_result["successful"]
    zip_filename = f"transcriptions_{successful_count}files_{timestamp}.zip"

    # 创建下载响应，并添加导出统计信息到响应头
    response = create_download_response(
        zip_buffer.getvalue(),
        zip_filename,
        "application/zip"
    )

    # 添加导出统计信息到响应头
    response.headers["X-Export-Total"] = str(len(file_ids))
    response.headers["X-Export-Successful"] = str(export_result["successful"])
    response.headers["X-Export-Failed"] = str(export_result["failed"])

    # 如果有失败的文件，添加到响应头（用于前端显示警告）
    if export_result["failed"] > 0:
        response.headers["X-Export-Has-Failures"] = "true"
        # 将错误信息编码为JSON字符串（简化版，只包含关键信息）
        import json
        error_summary = [{"fileId": err["fileId"], "error": err["error"]} for err in export_result["errors"]]
        response.headers["X-Export-Errors"] = json.dumps(error_summary)[:1000]  # 限制长度

    return response
