import json
import logging
import hmac
import hashlib
from flask import request

from exceptions.base import BaseAPIException
from exceptions.common import ValidationError
from exceptions.appsumo import (
    InvalidAppSumoSignatureError,
    InvalidAppSumoPayloadError,
    MissingAppSumoEventTypeError,
    UnknownAppSumoEventTypeError,
)
from services.appsumo_service import AppSumoService
from config import CONFIG

logger = logging.getLogger(__name__)


class AppSumoWebhookController:
    @staticmethod
    def verify_signature(payload, signature):
        """验证 webhook 签名"""
        if not signature:
            logger.warning("Missing X-AppSumo-Signature header")
            return False

        # 获取时间戳
        timestamp = request.headers.get("X-AppSumo-Timestamp")
        if not timestamp:
            logger.warning("Missing X-AppSumo-Timestamp header")
            return False

        # 打印调试信息
        logger.info(
            f"Verifying signature: timestamp={timestamp}, signature={signature}"
        )
        logger.info(f"Payload: {payload}")

        # 构建消息：时间戳 + 请求体
        message = f"{timestamp}{payload}"

        # 使用 API Key 生成签名
        api_key = CONFIG.APPSUMO.get("api_key")
        if not api_key:
            logger.error("Missing APPSUMO_API_KEY configuration")
            return False

        api_key = api_key.encode()
        computed_signature = hmac.new(
            key=api_key, msg=message.encode(), digestmod=hashlib.sha256
        ).hexdigest()

        # 打印计算的签名以进行比较
        logger.info(f"Computed signature: {computed_signature}")

        # 比较生成的签名与收到的签名
        result = hmac.compare_digest(computed_signature, signature)
        logger.info(f"Signature verification result: {result}")
        return result

    @staticmethod
    def handle_webhook():
        """处理 AppSumo webhook"""
        # 获取请求数据
        payload = request.data.decode("utf-8")
        signature = request.headers.get("X-AppSumo-Signature")

        # 验证签名
        if not AppSumoWebhookController.verify_signature(payload, signature):
            logger.warning("Invalid webhook signature")
            raise InvalidAppSumoSignatureError(message="Invalid signature")

        # 解析事件数据
        try:
            event_data = json.loads(payload)
        except json.JSONDecodeError:
            logger.error("Invalid JSON payload")
            raise InvalidAppSumoPayloadError(message="Invalid JSON payload")

        # 获取事件类型
        event = event_data.get("event")
        if not event:
            logger.error("Missing event type")
            raise MissingAppSumoEventTypeError(message="Missing event type")

        # 处理测试事件
        test = event_data.get("test", False)
        if test:
            license_key = event_data.get("license_key")
            logger.info(f"Ignoring test event for license: {license_key}")
            return {
                "success": True,
                "event": event,
                "message": "Test event processed successfully",
            }

        # 根据事件类型处理
        try:
            if event == "purchase":
                AppSumoService.handle_purchase_event(event_data)
            elif event == "activate":
                AppSumoService.handle_activate_event(event_data)
            elif event == "upgrade":
                AppSumoService.handle_upgrade_event(event_data)
            elif event == "downgrade":
                AppSumoService.handle_downgrade_event(event_data)
            elif event == "deactivate":
                AppSumoService.handle_deactivate_event(event_data)
            else:
                logger.warning(f"Unknown event type: {event}")
                raise UnknownAppSumoEventTypeError(
                    message=f"Unknown event type: {event}"
                )

            return {
                "success": True,
                "event": event,
                "message": "Event processed successfully",
            }
        except Exception as e:
            # 如果是已知的 API 异常，直接抛出
            if isinstance(e, BaseAPIException):
                raise

            # 其他异常记录并包装为通用错误
            logger.exception(f"Error processing {event} event: {str(e)}")
            raise ValidationError(message=f"Error processing event: {str(e)}")
