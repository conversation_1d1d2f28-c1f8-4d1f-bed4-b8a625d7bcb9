from datetime import datetime, timedelta
from typing import Optional
from models import db_transaction, db
from models.email_domain import EmailDomain
from services.usercheck_client import UsercheckClient
from config import CONFIG
import logging

logger = logging.getLogger(__name__)


class EmailValidatorService:
    def __init__(self):
        self.usercheck_client = UsercheckClient()
        self.cache_ttl = CONFIG.EMAIL_DOMAIN_CACHE_TTL

    def _extract_domain(self, email: str) -> str:
        return email.split("@")[1].lower()

    def _needs_update(self, domain_info: Optional[EmailDomain]) -> bool:
        if not domain_info:
            return True

        # 如果是公共域名，长期有效，避免重复查询
        if domain_info.is_public_domain:
            return False

        cache_ttl = timedelta(seconds=self.cache_ttl)
        return datetime.now() - domain_info.last_check_time > cache_ttl

    def _is_domain_valid(self, is_disposable: bool, spam: bool) -> bool:
        """
        检查域名是否有效（不是一次性邮箱且不是垃圾邮件域名）

        Args:
            is_disposable: 是否为一次性邮箱
            spam: 是否为垃圾邮件域名

        Returns:
            bool: True表示域名有效，False表示域名无效
        """
        return not is_disposable and not spam

    @db_transaction()
    def validate_email(self, email: str, skip_api_call: bool = False) -> bool:
        """
        验证邮箱是否可用于注册

        Args:
            email: 要验证的邮箱地址
            skip_api_call: 是否跳过第三方API调用，仅使用本地数据库缓存（默认False）

        Returns:
            bool: True 表示邮箱可用于注册，False 表示邮箱不可用（一次性邮箱或垃圾邮件域名）

        Example:
            >>> validator = EmailValidatorService()
            >>> validator.validate_email("<EMAIL>")
            True  # 正常邮箱，可以注册
            >>> validator.validate_email("<EMAIL>")
            False  # 一次性邮箱，不可注册
            >>> validator.validate_email("<EMAIL>")
            False  # 垃圾邮件域名，不可注册
        """
        domain = self._extract_domain(email)

        # 检查白名单
        if domain in CONFIG.WHITELIST_DOMAINS:
            return True

        # 查询本地数据库
        domain_info = EmailDomain.get_by_domain(domain)

        # 如果跳过API调用，直接使用本地缓存数据
        if skip_api_call:
            if domain_info:
                return self._is_domain_valid(
                    domain_info.is_disposable, domain_info.spam
                )
            # 没有本地数据且跳过API调用时，默认允许注册（保守策略）
            return True

        # 判断是否需要更新
        if self._needs_update(domain_info):
            try:
                result = self.usercheck_client.check_domain(domain)

                if domain_info:
                    # 更新现有记录
                    domain_info.is_disposable = result["is_disposable"]
                    domain_info.has_mx = result["has_mx"]
                    domain_info.is_public_domain = result["is_public_domain"]
                    domain_info.relay_domain = result["relay_domain"]
                    domain_info.spam = result["spam"]
                    domain_info.last_check_time = datetime.now()
                else:
                    # 创建新记录
                    domain_info = EmailDomain(
                        domain=domain,
                        is_disposable=result["is_disposable"],
                        has_mx=result["has_mx"],
                        is_public_domain=result["is_public_domain"],
                        relay_domain=result["relay_domain"],
                        spam=result["spam"],
                        last_check_time=datetime.now(),
                    )
                    db.session.add(domain_info)

                logger.info(f"Domain check result for {domain}: {result}")
                return self._is_domain_valid(result["is_disposable"], result["spam"])

            except Exception as e:
                logger.error(f"Error checking domain {domain}: {str(e)}")
                # API 异常时,如果有缓存数据则使用缓存,否则默认允许注册
                if domain_info:
                    return self._is_domain_valid(
                        domain_info.is_disposable, domain_info.spam
                    )
                return True

        return self._is_domain_valid(domain_info.is_disposable, domain_info.spam)


email_validator = EmailValidatorService()
