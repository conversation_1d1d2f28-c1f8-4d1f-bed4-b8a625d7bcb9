import logging


from exceptions.transcription import InsufficientTranscriptionQuotaError
from models.usage import Usage
from models.entitlement import Entitlement

logger = logging.getLogger(__name__)


def get_usage(user_id):
    from services.entitlement_service import EntitlementService

    # 2. 影子模式：获取 entitlement 系统的使用量
    credits = EntitlementService.get_user_credits(user_id)
    total_credits = credits["total_credits"]
    remaining_credits = credits["remaining_credits"]
    consumed_credits = credits["consumed_credits"]

    entitlement = Entitlement.get_next_reset_time(user_id)
    next_reset_time = entitlement.valid_until

    # 兼容老系统
    usage = Usage(
        user_id=user_id,
        transcription_minutes_quota=total_credits,
        transcription_minutes_used=consumed_credits,
        next_reset_time=next_reset_time,
    )
    usage.transcription_remaining_minutes = remaining_credits
    return usage


def check_transcription_quota(user_id, duration):
    from services.entitlement_service import EntitlementService

    # 检查权益配额
    try:
        EntitlementService.check_quota(user_id, duration)
    except InsufficientTranscriptionQuotaError as e:
        logger.info(f"Insufficient entitlement credits for user {user_id}: {str(e)}")
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error when checking entitlement quota for user {user_id}: {str(e)}"
        )
        raise
