import logging
from yt_dlp import YoutubeDL
from yt_dlp.utils import DownloadError, ExtractorError
from flask_restful import abort
import os
import time
import hashlib
import tempfile
import shutil
import webvtt
import requests
from io import StringIO
import csv
import re
from datetime import datetime
import sentry_sdk

from config import CONFIG
from controllers.task import create_transcription_task
from controllers.transcription import create_transcription_file
from models import db_transaction, db
from models.task import Task
from models.youtube_transcript import YoutubeTranscript
from constants.task import TaskStatus
from constants.transcription import TranscriptionFileSourceType, SUPPORTED_AUDIO_FORMATS
from exceptions.youtube import (
    YoutubeExtractError,
    YoutubeDownloadError,
    YoutubeUploadError,
    YoutubeTranscriptionError,
)

logger = logging.getLogger(__name__)


def extract_youtube_info(
    url: str, ydl_opts: dict, error_context: str = "", download: bool = False
):
    """
    统一处理 YouTube 信息提取

    Args:
        url: YouTube URL
        ydl_opts: yt-dlp 选项
        error_context: 错误上下文信息，用于日志

    Returns:
        提取的信息字典

    Raises:
        YoutubeExtractError: YouTube 相关的错误
        abort: HTTP 错误响应
    """
    logger.info(
        f"Extracting YouTube info for URL: {url}, context: {error_context}, download: {download}"
    )

    try:
        if CONFIG.YOUTUBE_PROXY:
            ydl_opts["proxy"] = CONFIG.YOUTUBE_PROXY
            logger.info(f"Using YouTube proxy: {CONFIG.YOUTUBE_PROXY}")

        # 记录关键的 yt-dlp 选项
        log_opts = {
            k: v for k, v in ydl_opts.items() if k not in ["postprocessors", "logger"]
        }
        logger.info(f"YoutubeDL options: {log_opts}")

        with YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=download)
            if info:
                logger.info(
                    f"Successfully extracted video info: {error_context}, video_id: {info.get('id')}, title: {info.get('title')}"
                )
            else:
                logger.warning(f"Extracted info is None for URL: {url}")
            return info

    except (ExtractorError, DownloadError) as e:
        # 处理错误消息，移除 "ERROR: [youtube]" 前缀
        # 保存原始错误消息用于判断
        original_error_message = str(e)

        # 清理错误消息格式，移除 "ERROR: [youtube]" 前缀
        cleaned_error_msg = original_error_message
        if "ERROR: [youtube]" in cleaned_error_msg:
            # 使用 split 分割，取最后一部分
            cleaned_error_msg = cleaned_error_msg.split(":", 2)[-1].strip()

        # 根据原始错误消息判断错误类型，生成用户友好的错误消息
        if "Private video" in original_error_message:
            # 私有视频：只保留核心错误部分
            error_msg = cleaned_error_msg.split(". Use --cookies")[0].strip()
        elif "Sign in to confirm your age" in original_error_message:
            # 年龄限制视频
            error_msg = "This video may be inappropriate for some users. Please try another video."
        elif "This video is unavailable" in original_error_message:
            # 不可用视频
            error_msg = cleaned_error_msg.split(". Use --cookies")[0].strip()
        elif ("available to this channel's members" in original_error_message and ("level:" in original_error_message or "Join this channel" in original_error_message)) or ("Join this channel to get access to members-only content" in original_error_message):
            # 会员专属内容
            # e.g. https://www.youtube.com/watch?v=peqzWu99Cpw
            # e.g. https://www.youtube.com/watch?v=KEpywoRp_Us
            error_msg = "This video is only available to channel members. Please try a different video that is publicly accessible."
        elif "Video unavailable" in original_error_message:
            # 不可用视频的另一种情况
            error_msg = cleaned_error_msg.split(". Use --cookies")[0].strip()
        elif "The uploader has not made this video available in your country" in original_error_message:
            # 地区限制视频，只保留第一行错误信息
            lines = cleaned_error_msg.split("\n")
            error_msg = lines[0].strip() if lines else cleaned_error_msg.strip()
        elif "Incomplete YouTube ID" in original_error_message and "looks truncated" in original_error_message:
            # 截断的YouTube ID错误
            error_msg = cleaned_error_msg.split(". URL")[0].strip()
        elif "SSL: UNEXPECTED_EOF_WHILE_READING" in original_error_message or "EOF occurred in violation of protocol" in original_error_message:
            # SSL网络异常错误（通常由内部代理导致）
            error_msg = "A temporary connection issue occurred on our end while processing your request. Please try again in a moment."
        elif "HTTPSConnectionPool" in original_error_message and "Read timed out" in original_error_message:
            # HTTPS连接超时错误
            error_msg = "A connection timeout occurred on our end while downloading the video. Please try again in a moment."
        elif "Connection aborted" in original_error_message or "Connection reset by peer" in original_error_message:
            # 网络连接中断错误（连接被重置或中断）
            error_msg = "A network connection issue occurred on our end while downloading the video. Please try again in a moment."
        else:
            # 未知错误：使用 Sentry 捕获，提供更多上下文信息
            sentry_sdk.capture_exception(
                e,
                extra={
                    "youtube_url": url,
                    "error_context": error_context,
                    "original_error_message": original_error_message,
                    "ydl_opts": ydl_opts
                }
            )
            error_msg = "Something went wrong. Please try again later. If the issue persists after multiple attempts, please contact <NAME_EMAIL>"

        raise YoutubeExtractError(
            message=error_msg,
        ) from e

    except Exception as e:
        logger.exception(
            f"Unexpected error while extracting video info ({error_context}): URL={url}, error={str(e)}"
        )
        abort(
            500,
            message="Something went wrong. Please try again later. If the issue persists after multiple attempts, please contact <NAME_EMAIL>",
        )


class YoutubeInfoExtractor:
    def __init__(self, url: str):
        self.url = url

    def extract(self):
        """Extract video information from URL"""
        ydl_opts = {
            "quiet": True,
            "no_warnings": True,
            "skip_download": True,
            "verbose": True,
            "logger": logger,
            "extract_flat": "in_playlist",
            "force_generic_extractor": False,
            "youtube_include_dash_manifest": True,
            "youtube_include_hls_manifest": True,
            "allow_unplayable_formats": True,
            "check_formats": False,
            "format": "bestaudio/best",
        }

        info = extract_youtube_info(self.url, ydl_opts, "info extraction")
        filtered_formats = self._filter_formats(info.get("formats", []))
        return self._format_response(filtered_formats, info)

    def _filter_formats(self, formats):
        """过滤和分类格式"""
        result = []
        seen_format_keys = set()

        for f in formats:
            # 跳过没有文件大小的格式
            file_size = f.get("filesize") or f.get("filesize_approx", 0)
            if file_size == 0:
                continue

            # 获取基本格式ID（去除-drc后缀）
            format_id = f.get("format_id", "")
            # 跳过DRC版本
            if "-drc" in format_id:
                continue

            # 创建格式的唯一键 - 使用 format_id 更可靠
            if format_id in seen_format_keys:
                continue
            seen_format_keys.add(format_id)

            # 完整视频格式（音视频）
            if f.get("acodec") != "none" and f.get("vcodec") != "none":
                result.append(f)

            # 纯视频格式 (使用 vbr 而不是 bitrate)
            elif f.get("vcodec") != "none" and f.get("acodec") == "none":
                result.append(f)

            # 音频格式 (使用多个字段尝试获取比特率)
            elif f.get("acodec") != "none" and f.get("vcodec") == "none":
                # 尝试多个字段来获取比特率
                bitrate = f.get("abr") or f.get("tbr") or f.get("bitrate")
                if bitrate and float(bitrate) > 0:  # 确保比特率存在且大于0
                    result.append(f)

        return result

    def _get_format_type(self, f):
        """判断格式类型"""
        if f.get("acodec") != "none" and f.get("vcodec") != "none":
            return "video_with_audio"
        elif f.get("acodec") != "none":
            return "audio"
        else:
            return "video_only"

    def _sort_formats(self, formats):
        """按类型分组并排序格式"""
        format_groups = {
            "video_with_audio": [],
            "video_only": [],
            "audio": [],
        }

        for f in formats:
            format_type = self._get_format_type(f)
            if format_type in format_groups:
                format_groups[format_type].append(f)

        # 视频按分辨率和比特率排序
        for group in ["video_with_audio", "video_only"]:
            format_groups[group].sort(
                key=lambda x: (x.get("height", 0), x.get("vbr", 0)), reverse=True
            )

        # 音频按比特率排序
        format_groups["audio"].sort(key=lambda x: x.get("abr", 0), reverse=True)

        return format_groups

    def _format_response(self, formats, info):
        """格式化响应数据"""
        sorted_formats = self._sort_formats(formats)

        def format_size(f):
            """格式化文件大小"""
            return f.get("filesize") or f.get("filesize_approx", 0)

        def format_bitrate(f):
            """格式化比特率"""
            if f.get("vcodec") != "none":
                return f.get("vbr", 0)  # 视频使用 vbr
            return f.get("abr", 0)  # 音频使用 abr

        def format_label(f):
            """格式化标签"""
            if f.get("vcodec") != "none":
                return f"{f.get('ext')} ({f.get('format_note', f.get('height', 0))}p)"
            else:
                # 音频格式
                return f"{f.get('ext')} ({int(f.get('abr', 0))}kb/s)"

        return {
            "formats": {
                "video_with_audio": [
                    {
                        "formatId": f.get("format_id"),
                        "label": format_label(f),
                        "ext": f.get("ext"),
                        "height": f.get("height"),
                        "width": f.get("width"),
                        "fps": f.get("fps"),
                        "fileSize": format_size(f),
                        "bitrate": format_bitrate(f),
                        "url": f.get("url"),
                        "vcodec": self.get_codec_label(f.get("vcodec")),
                        "acodec": self.get_codec_label(f.get("acodec")),
                    }
                    for f in sorted_formats["video_with_audio"]
                ],
                "video_only": [
                    {
                        "formatId": f.get("format_id"),
                        "label": format_label(f),
                        "ext": f.get("ext"),
                        "height": f.get("height"),
                        "width": f.get("width"),
                        "fps": f.get("fps"),
                        "fileSize": format_size(f),
                        "bitrate": format_bitrate(f),
                        "url": f.get("url"),
                        "vcodec": self.get_codec_label(f.get("vcodec")),
                    }
                    for f in sorted_formats["video_only"]
                ],
                "audio": [
                    {
                        "formatId": f.get("format_id"),
                        "label": format_label(f),
                        "ext": f.get("ext"),
                        "fileSize": format_size(f),
                        "bitrate": format_bitrate(f),
                        "url": f.get("url"),
                        "acodec": self.get_codec_label(f.get("acodec")),
                    }
                    for f in sorted_formats["audio"]
                ],
            },
            "title": info.get("title"),
            "duration": str(info.get("duration")),
            "thumbnailUrl": info.get("thumbnail"),
        }

    def get_codec_label(self, codec):
        """Get user-friendly codec label"""
        if not codec:
            return "Unknown"

        codec = codec.lower()

        # Video codecs
        if "av01" in codec:
            return "AV1 (New Format)"
        elif "avc1" in codec:
            return "H.264 (Common)"
        elif "vp9" in codec or "vp09" in codec:
            return "VP9 (Efficient)"

        # Audio codecs
        elif "opus" in codec:
            return "Opus (High Quality)"
        elif "mp4a" in codec:
            return "AAC (Common)"
        elif "vorbis" in codec:
            return "Vorbis"

        # Other cases
        return f"Other ({codec})"


class YoutubeTranscriber:

    def __init__(self, user_id: int, url: str, title: str, duration: int, storage):
        self.url = url
        self.user_id = user_id
        self.storage = storage
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp(prefix="youtube_")
        self.title = title
        self.filepath = None
        self.duration = duration
        self.tf = None
        self.task = None

    @db_transaction()
    def create_transcription_file(
        self,
        transcription_type,
        language_code=None,
        enable_speaker_diarization=False,
        folder_id=None,
    ):
        """创建转录文件"""

        tf = create_transcription_file(
            self.user_id,
            self.title,  # 使用 title 作为 filename
            "audio",  # 使用通用的 audio 类型，稍后会根据实际下载的文件更新
            0,  # file_size 还不知道
            "",  # md5 还不知道
            self.duration,
            transcription_type=transcription_type,
            source_type=TranscriptionFileSourceType.YOUTUBE,  # 设置来源类型为 YouTube
            source_url=self.url,  # 设置 YouTube URL
            language_code=language_code,
            enable_speaker_diarization=enable_speaker_diarization,
            folder_id=folder_id,
        )
        self.tf = tf
        return tf



    def download_audio(self):
        """下载YouTube音频"""
        # 用于捕获下载的文件路径
        downloaded_files = []

        def progress_hook(d):
            if d["status"] == "finished":
                downloaded_files.append(d["filename"])
                logger.info("Download finished: %s", d["filename"])

        ydl_opts = {
            "format": "bestaudio",
            "outtmpl": os.path.join(self.temp_dir, "%(title)s.%(ext)s"),
            "restrictfilenames": True,
            "progress_hooks": [progress_hook],
        }

        logger.info("Starting download for YouTube URL: %s", self.url)
        try:
            info = extract_youtube_info(self.url, ydl_opts, "audio download", True)

            if not info:
                logger.error("Failed to extract info from YouTube URL: %s", self.url)
                raise YoutubeDownloadError(
                    message="Failed to extract info from YouTube URL",
                    details={"url": self.url, "user_id": self.user_id},
                )

            self.title = info.get("title")
            self.duration = info.get("duration")
            logger.info(
                "Successfully extracted info - title: %s, duration: %ss",
                self.title,
                self.duration,
            )

            # 使用 progress_hook 捕获的文件路径
            if downloaded_files:
                self.filepath = downloaded_files[0]
                logger.info("Using file path from progress hook: %s", self.filepath)
            else:
                logger.error("No files captured by progress hook")
                raise YoutubeDownloadError(
                    message="Download completed but no file was captured by progress hook",
                    details={
                        "url": self.url,
                        "user_id": self.user_id,
                        "temp_dir": self.temp_dir,
                        "downloaded_files": downloaded_files,
                    },
                )
        except YoutubeExtractError as e:
            # 已经是自定义异常，直接转换为下载错误
            # 这里的错误消息已经在 extract_youtube_info 中处理过了
            raise YoutubeDownloadError(message=e.description, details=e.details) from e
        except Exception as e:
            # 其他异常转换为下载错误
            logger.exception("Failed to download audio: %s", str(e))
            raise YoutubeDownloadError(
                message=f"Failed to download audio: {str(e)}",
                details={
                    "url": self.url,
                    "user_id": self.user_id,
                    "original_error": str(e),
                },
            ) from e

    def upload_to_r2(self):
        """直接上传文件到 Cloudflare R2"""
        if not self.filepath:
            logger.error("Cannot upload to R2: filepath is None")
            raise YoutubeUploadError(
                message="Cannot upload to R2: filepath is None",
                details={"url": self.url, "user_id": self.user_id},
            )

        # 生成文件指纹
        try:
            logger.info("Generating file fingerprint for %s", self.filepath)
            with open(self.filepath, "rb") as f:
                file_content = f.read()
                fingerprint = hashlib.md5(file_content).hexdigest()
            logger.info(f"Generated fingerprint: {fingerprint}")
            self.update_transcription_file(fingerprint)
        except Exception as e:
            logger.exception("Failed to generate file fingerprint: %s", str(e))
            raise YoutubeUploadError(
                message=f"Failed to generate file fingerprint: {str(e)}",
                details={
                    "url": self.url,
                    "user_id": self.user_id,
                    "filepath": self.filepath,
                    "original_error": str(e),
                },
            ) from e

        try:
            # 直接上传文件
            if not self.tf or not self.tf.file_key:
                logger.error("Cannot upload to R2: file_key is None")
                raise YoutubeUploadError(
                    message="Cannot upload to R2: file_key is None",
                    details={"url": self.url, "user_id": self.user_id},
                )

            bucket_name = self.storage.config.get_bucket_name()
            logger.info(
                "Uploading file to R2 bucket: %s, key: %s",
                bucket_name,
                self.tf.file_key,
            )

            # 获取文件大小，用于日志
            file_size = os.path.getsize(self.filepath)
            logger.info("Uploading file size: %d bytes", file_size)

            # 使用 storage 的 upload_file 方法，它会自动使用上传专用客户端
            logger.info("Uploading YouTube file using storage.upload_file method")
            self.storage.upload_file(self.tf.file_key, self.filepath)
            logger.info(f"Successfully uploaded file to R2: {self.tf.file_key}")

            # 直接更新文件状态，避免复杂的状态检查逻辑
            logger.info(f"Updating file status after successful upload: file_id={self.tf.id}")
            self._complete_upload_direct()
            logger.info("File status updated successfully")

        except Exception as e:
            logger.exception(
                f"Failed to upload to R2: file_path={self.filepath}, error={str(e)}"
            )
            raise YoutubeUploadError(
                message=f"Failed to upload to R2: {str(e)}",
                details={
                    "url": self.url,
                    "user_id": self.user_id,
                    "file_id": self.tf.id if self.tf else None,
                    "file_path": self.filepath,
                    "original_error": str(e),
                },
            ) from e

    def _complete_upload_direct(self):
        """直接完成上传，避免复杂的状态检查"""
        try:
            from models.transcription_file import TranscriptionFile
            from models.file_storage import FileStorage
            from constants.transcription import TranscriptionFileStatus
            from datetime import datetime

            # 重新查询文件以确保数据最新
            tf = TranscriptionFile.query.get(self.tf.id)
            if not tf:
                raise ValueError(f"TranscriptionFile not found: {self.tf.id}")

            # 创建文件存储记录
            if tf.fingerprint:
                FileStorage.create_or_update_storage(
                    tf.user_id,
                    tf.fingerprint,
                    tf.file_type,
                    tf.file_key,
                    tf.file_size,
                )
                logger.info(f"Created/updated file storage record for fingerprint: {tf.fingerprint}")

            # 更新文件状态为已上传
            tf.status = TranscriptionFileStatus.uploaded.id
            tf.uploaded_time = datetime.now()
            db.session.commit()

            logger.info(f"Successfully updated file status to uploaded: file_id={tf.id}")

        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to complete upload directly: {str(e)}")
            raise

    def update_transcription_file(self, fingerprint):
        """更新转录文件并创建存储记录"""
        try:
            # 在新上下文中重新查询 tf (这里跨了 db_transaction 的上下文)
            from models.transcription_file import TranscriptionFile

            tf = TranscriptionFile.query.get(self.tf.id)

            # 从实际下载的文件路径中获取文件扩展名
            if self.filepath:
                file_ext = os.path.splitext(self.filepath)[1].lower().lstrip(".")
                # 如果扩展名为空或不是支持的音频格式，默认使用 mp3
                if not file_ext or file_ext not in SUPPORTED_AUDIO_FORMATS:
                    file_ext = "mp3"
            else:
                file_ext = "mp3"

            logger.info(
                "Detected file type: %s from filepath: %s", file_ext, self.filepath
            )

            if not self.filepath:
                raise ValueError("File path is None, cannot get file size")

            tf.file_size = os.path.getsize(self.filepath)
            tf.fingerprint = fingerprint
            tf.file_type = file_ext
            tf.file_key = self.storage.generate_key(
                tf.user_id, tf.fingerprint, tf.file_type
            )
            tf.file_url = self.storage.generate_file_url(tf.file_key)

            # 不在这里创建 FileStorage 记录
            # FileStorage 的创建统一在 complete_upload 中处理
            logger.info(
                f"Updated TranscriptionFile for YouTube: user_id={tf.user_id}, fingerprint={fingerprint}, "
                f"file_type={tf.file_type}, file_size={tf.file_size}"
            )

            db.session.commit()
            # 更新实例的 tf 引用
            self.tf = tf

        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to update transcription file: %s", str(e))
            raise

    def trigger_transcription(self):
        """触发转录"""
        if not self.tf:
            logger.error("Cannot trigger transcription: transcription file is None")
            raise YoutubeTranscriptionError(
                message="Cannot trigger transcription: transcription file is None",
                details={"url": self.url, "user_id": self.user_id},
            )

        logger.info(f"Triggering transcription for file_id: {self.tf.id}")
        try:
            self.task = create_transcription_task(self.user_id, self.tf.id)
            logger.info(
                f"Transcription task created successfully: {self.task.id if self.task else 'unknown'}"
            )
        except Exception as e:
            logger.exception(f"Failed to create transcription task: {str(e)}")
            raise YoutubeTranscriptionError(
                message=f"Failed to create transcription task: {str(e)}",
                details={
                    "url": self.url,
                    "user_id": self.user_id,
                    "file_id": self.tf.id,
                    "original_error": str(e),
                },
            ) from e

    def _cleanup_temp_dir(self):
        """清理临时目录及其内容"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.exception("Error cleaning up temp directory: %s", e)

    def cleanup(self):
        """清理临时目录及其内容"""
        self._cleanup_temp_dir()


class YoutubeTranscriptExtractor:
    def __init__(self, url: str):
        self.url = url
        self._info = None  # 缓存 extract_info 的结果

    def _extract_info(self):
        """Extract video information only once"""
        if self._info is None:
            logger.info("Extracting video information for URL: %s", self.url)
            ydl_opts = {
                "quiet": True,
                "no_warnings": True,
                "writesubtitles": True,
                "listsubtitles": True,
                "skip_download": True,
                "noplaylist": True,
            }

            try:
                self._info = extract_youtube_info(
                    self.url, ydl_opts, "transcript extraction"
                )
                if not self._info:
                    logger.warning("Extracted info is None for URL: %s", self.url)
            except Exception as e:
                logger.exception("Failed to extract video info: %s", e)
                raise

        return self._info

    def list_subtitles(self):
        """List available subtitles for the video"""
        try:
            info = self._extract_info()
            subtitles = info.get("subtitles", {})
            auto_captions = info.get("automatic_captions", {})

            # Combine manual and auto subtitles
            all_subtitles = []

            # Add manual subtitles
            for lang_code, sub_info in subtitles.items():
                all_subtitles.append(
                    {
                        "lang_code": lang_code,
                        "formats": [
                            {
                                "ext": format_info["ext"],
                                "url": format_info.get("url", ""),
                            }
                            for format_info in sub_info
                        ],
                        "name": sub_info[0].get("name", ""),
                        "is_auto": False,
                    }
                )
            # 如果有手动字幕，就不需要自动字幕了
            if all_subtitles:
                return {"subtitles": all_subtitles}

            # Add auto-generated captions (only the original ones with -orig)
            for lang_code, sub_info in auto_captions.items():
                if lang_code.endswith("-orig"):  # Only include original auto captions
                    all_subtitles.append(
                        {
                            "lang_code": lang_code,
                            "formats": [
                                {
                                    "ext": format_info["ext"],
                                    "url": format_info.get("url", ""),
                                }
                                for format_info in sub_info
                            ],
                            "name": sub_info[0].get("name", ""),
                            "is_auto": True,
                        }
                    )

            return {"subtitles": all_subtitles}

        except Exception as e:
            raise

    def download_subtitle(self, lang_code: str):
        """Download subtitle in VTT format"""
        try:
            info = self._extract_info()
            subtitles = info.get("subtitles", {})
            auto_captions = info.get("automatic_captions", {})

            # Check both manual subtitles and auto captions
            subtitle_formats = subtitles.get(lang_code) or auto_captions.get(lang_code)
            is_auto = lang_code.endswith("-orig")

            if not subtitle_formats:
                raise ValueError(f"No subtitles found for language: {lang_code}")

            # 查找 VTT 格式
            vtt_format = next((f for f in subtitle_formats if f["ext"] == "vtt"), None)

            if not vtt_format:
                raise ValueError("VTT format not available")

            # 下载 VTT 内容
            response = requests.get(vtt_format["url"])
            response.raise_for_status()

            # 如果是自动生成的字幕，进行去重处理
            if is_auto:
                return self.process_vtt(response.text)
            return response.text

        except Exception as e:
            logger.error("Failed to download subtitle: %s", str(e), exc_info=True)
            raise

    def process_vtt(self, vtt_content):
        """处理 VTT 内容，去除重复字幕"""
        # 解析 VTT 内容
        subtitles = self._parse_vtt(vtt_content)
        # 去重字幕
        optimized_subtitles = self._remove_duplicates(subtitles)
        # 转换回 VTT 格式
        return self._subtitles_to_vtt(optimized_subtitles)

    def _subtitles_to_vtt(self, subtitles):
        """将字幕数组转换为 VTT 格式"""
        vtt_output = "WEBVTT\n\n"
        for subtitle in subtitles:
            vtt_output += f"{subtitle['start']} --> {subtitle['end']}\n"
            vtt_output += f"{subtitle['text']}\n\n"
        return vtt_output

    def _remove_duplicates(self, subtitles):
        """去除重复的字幕"""
        optimized = []
        previous = None

        for subtitle in subtitles:
            if previous and subtitle["text"] == previous["text"]:
                # 如果内容相同，合并时间范围
                previous["end"] = subtitle["end"]
            else:
                # 新字幕
                previous = subtitle
                optimized.append(previous)

        return optimized

    def _parse_vtt(self, vtt_content):
        """解析 VTT 内容为字幕数组"""
        pattern = re.compile(
            r"(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})"
        )
        subtitles = []

        lines = vtt_content.strip().split("\n")
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            match = pattern.match(line)
            if match:
                start_time = match.group(1)
                end_time = match.group(2)
                text = lines[i + 1].strip() if i + 1 < len(lines) else ""
                subtitles.append({"start": start_time, "end": end_time, "text": text})
                i += 2  # 跳过时间戳和文本行
            else:
                i += 1  # 跳过非时间戳行

        return subtitles


class YoutubeTranscriptManager:
    @staticmethod
    def save_transcript(
        url: str, lang_code: str, lang_name: str, vtt_content: str, is_auto: bool
    ):
        """保存字幕，如果已存在则直接返回"""
        video_id = YoutubeTranscript.extract_video_id(url)
        if not video_id:
            raise ValueError("Invalid YouTube URL")

        try:
            # 先查找是否存在
            transcript = YoutubeTranscript.query.filter_by(
                video_id=video_id, lang_code=lang_code
            ).first()

            if transcript:
                # 如果存在，直接返回
                logger.info(
                    f"Transcript already exists for video {video_id}, language {lang_code}"
                )
                return transcript

            # 如果不存在，创建新记录
            transcript = YoutubeTranscript(
                video_id=video_id,
                lang_code=lang_code,
                lang_name=lang_name,  # 保存语言名称
                vtt_content=vtt_content,
                is_auto=is_auto,
            )
            db.session.add(transcript)
            db.session.commit()
            return transcript

        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to save transcript: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def get_transcript(video_id: str, lang_code: str, output_format: str = "vtt"):
        """获取字幕，支持格式转换"""
        transcript = YoutubeTranscript.query.filter_by(
            video_id=video_id, lang_code=lang_code
        ).first()

        if not transcript:
            return None

        if output_format == "vtt":
            return transcript.vtt_content

        # 使用 webvtt-py 进行格式转换
        with tempfile.NamedTemporaryFile(mode="w", suffix=".vtt", delete=False) as tmp:
            tmp.write(transcript.vtt_content)
            tmp_path = tmp.name

        try:
            captions = webvtt.read(tmp_path)

            if output_format == "srt":
                return YoutubeTranscriptManager._to_srt(captions)
            elif output_format == "csv":
                return YoutubeTranscriptManager._to_csv(captions)
            elif output_format == "txt":
                return YoutubeTranscriptManager._to_txt(captions)
            else:
                raise ValueError(f"Unsupported format: {output_format}")
        finally:
            os.unlink(tmp_path)

    @staticmethod
    def _to_srt(captions):
        """Convert to SRT format"""
        srt_lines = []
        for i, caption in enumerate(captions, 1):
            start = caption.start.replace(".", ",")
            end = caption.end.replace(".", ",")
            srt_lines.extend([str(i), f"{start} --> {end}", caption.text, ""])
        return "\n".join(srt_lines)

    @staticmethod
    def _to_csv(captions):
        """Convert to CSV format"""
        output = StringIO()
        writer = csv.writer(output)
        writer.writerow(["Start", "End", "Text"])

        for caption in captions:
            writer.writerow(
                [caption.start, caption.end, caption.text.replace("\n", " ")]
            )

        return output.getvalue()

    @staticmethod
    def _to_txt(captions):
        """Convert to plain text format"""
        return "\n".join(caption.text for caption in captions)
