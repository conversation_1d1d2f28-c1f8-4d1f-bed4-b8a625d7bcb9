"""
API Key management controller
"""

import logging
from datetime import datetime, timedelta
from flask import g

from models import db, db_transaction_decorator
from models.api_key import APIKey
from models.user import User
from resources.openapi_auth import generate_api_key
from exceptions.openapi import (
    APIKeyNotFoundError,
    APIKeyLimitExceededError,
    APIKeyNameExistsError,
)

logger = logging.getLogger(__name__)

# Maximum number of API keys per user
MAX_API_KEYS_PER_USER = 5

# Default rate limits for API keys
DEFAULT_RATE_LIMIT_PER_MINUTE = 10
DEFAULT_RATE_LIMIT_PER_DAY = 1000


@db_transaction_decorator
def list_user_api_keys(user_id):
    """List all active API keys for a user
    
    Args:
        user_id (int): User ID
        
    Returns:
        list: List of API key dictionaries
    """
    api_keys = APIKey.get_active_keys_for_user(user_id)
    
    # Convert to dict without full key (only preview)
    result = []
    for api_key in api_keys:
        key_dict = api_key.to_dict()
        # Generate a generic preview using key ID since we don't store the full key
        # Format: us_ + 28 stars + last 4 chars of ID
        key_dict["keyPreview"] = f"us_{'*' * 28}{api_key.id[-4:]}"
        result.append(key_dict)

    return result


@db_transaction_decorator
def create_api_key(user_id, name, expires_days=None):
    """Create a new API key for a user
    
    Args:
        user_id (int): User ID
        name (str): User-friendly name for the key
        expires_days (int, optional): Number of days until expiration
        
    Returns:
        dict: API key data including full key
        
    Raises:
        APIKeyLimitExceededError: If user has reached maximum keys
        APIKeyNameExistsError: If name already exists for user
    """
    # Check if user has reached maximum keys
    existing_keys = APIKey.get_active_keys_for_user(user_id)
    if len(existing_keys) >= MAX_API_KEYS_PER_USER:
        raise APIKeyLimitExceededError(f"Maximum number of API keys reached ({MAX_API_KEYS_PER_USER})")
    
    # Check if name already exists for this user
    existing_name = APIKey.query.filter_by(
        user_id=user_id,
        name=name,
        is_active=True
    ).first()
    if existing_name:
        raise APIKeyNameExistsError("API key name already exists")
    
    # Generate API key
    api_key, key_hash = generate_api_key()
    
    # Calculate expiration date
    expires_at = None
    if expires_days:
        expires_at = datetime.utcnow() + timedelta(days=expires_days)
    
    # Create API key record
    api_key_record = APIKey(
        user_id=user_id,
        name=name,
        key_hash=key_hash,
        expires_at=expires_at,
        rate_limit_per_minute=DEFAULT_RATE_LIMIT_PER_MINUTE,
        rate_limit_per_day=DEFAULT_RATE_LIMIT_PER_DAY
    )
    
    db.session.add(api_key_record)
    db.session.flush()  # Get the ID
    
    logger.info(f"Created API key {api_key_record.id} for user {user_id}")
    
    # Return with full key (only time it's available)
    return api_key_record.to_dict(include_full_key=True, full_api_key=api_key)


@db_transaction_decorator
def get_api_key(user_id, api_key_id):
    """Get a specific API key for a user
    
    Args:
        user_id (int): User ID
        api_key_id (str): API key ID
        
    Returns:
        dict: API key data (without full key)
        
    Raises:
        APIKeyNotFoundError: If key not found or doesn't belong to user
    """
    api_key = APIKey.get_by_id(api_key_id)
    
    if not api_key or api_key.user_id != user_id or not api_key.is_active:
        raise APIKeyNotFoundError("API key not found")
    
    # Return without full key, with generic preview
    key_dict = api_key.to_dict()
    key_dict["keyPreview"] = f"us_{'*' * 28}{api_key.id[-4:]}"

    return key_dict


@db_transaction_decorator
def update_api_key(user_id, api_key_id, name):
    """Update an API key name
    
    Args:
        user_id (int): User ID
        api_key_id (str): API key ID
        name (str): New name for the key
        
    Returns:
        dict: Updated API key data
        
    Raises:
        APIKeyNotFoundError: If key not found or doesn't belong to user
        APIKeyNameExistsError: If name already exists for user
    """
    api_key = APIKey.get_by_id(api_key_id)
    
    if not api_key or api_key.user_id != user_id or not api_key.is_active:
        raise APIKeyNotFoundError("API key not found")
    
    # Check if name already exists for this user (excluding current key)
    existing_name = APIKey.query.filter_by(
        user_id=user_id,
        name=name,
        is_active=True
    ).filter(APIKey.id != api_key_id).first()
    
    if existing_name:
        raise APIKeyNameExistsError("API key name already exists")
    
    # Update name
    api_key.name = name
    
    logger.info(f"Updated API key {api_key_id} name to '{name}' for user {user_id}")
    
    # Return without full key, with generic preview
    key_dict = api_key.to_dict()
    key_dict["keyPreview"] = f"us_{'*' * 28}{api_key.id[-4:]}"

    return key_dict


@db_transaction_decorator
def reset_api_key(user_id, api_key_id):
    """Reset an API key (generate new key value)
    
    Args:
        user_id (int): User ID
        api_key_id (str): API key ID
        
    Returns:
        dict: API key data with new full key
        
    Raises:
        APIKeyNotFoundError: If key not found or doesn't belong to user
    """
    api_key = APIKey.get_by_id(api_key_id)
    
    if not api_key or api_key.user_id != user_id or not api_key.is_active:
        raise APIKeyNotFoundError("API key not found")
    
    # Generate new API key
    new_api_key, new_key_hash = generate_api_key()
    
    # Update key hash and reset last used time
    api_key.key_hash = new_key_hash
    api_key.last_used_time = None
    
    logger.info(f"Reset API key {api_key_id} for user {user_id}")
    
    # Return with full key (only time it's available after creation)
    return api_key.to_dict(include_full_key=True, full_api_key=new_api_key)


@db_transaction_decorator
def delete_api_key(user_id, api_key_id):
    """Delete (deactivate) an API key
    
    Args:
        user_id (int): User ID
        api_key_id (str): API key ID
        
    Raises:
        APIKeyNotFoundError: If key not found or doesn't belong to user
    """
    api_key = APIKey.get_by_id(api_key_id)
    
    if not api_key or api_key.user_id != user_id or not api_key.is_active:
        raise APIKeyNotFoundError("API key not found")
    
    # Soft delete (deactivate)
    api_key.deactivate()
    
    logger.info(f"Deleted API key {api_key_id} for user {user_id}")
