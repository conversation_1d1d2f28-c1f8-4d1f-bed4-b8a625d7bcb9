import logging


from services.appsumo_service import AppSumoService
from exceptions.base import BaseAPIException
from exceptions.appsumo import (
    AppSumoLicenseActivationError,
)


logger = logging.getLogger(__name__)


class AppSumoOAuthController:
    @staticmethod
    def activate_license(user_id, code):
        """为已登录用户激活 AppSumo license"""
        logger.info("Starting AppSumo license activation for user_id=%s", user_id)
        if not code:
            logger.error("Missing OAuth code for user_id=%s", user_id)
            raise AppSumoLicenseActivationError(message="Missing OAuth code")

        try:
            logger.info("Processing OAuth code for user_id=%s", user_id)
            # 处理 OAuth 授权码，获取 license 信息
            license_data = AppSumoService.process_oauth_code(code)

            # 记录 license 信息（不包含敏感数据）
            logger.info(
                "OAuth code processed successfully for user_id=%s, license_key=%s, tier=%s",
                user_id,
                license_data.get("license_key"),
                license_data.get("tier"),
            )

            logger.info(
                "Activating license for user_id=%s, license_key=%s",
                user_id,
                license_data.get("license_key"),
            )
            # 激活 license
            AppSumoService.activate_user_license(user_id, license_data)

            logger.info(
                "License activated successfully for user_id=%s, license_key=%s",
                user_id,
                license_data.get("license_key"),
            )
            # 返回成功信息
            return {"success": True, "message": "License activated successfully"}
        except BaseAPIException as e:
            # 已经是 API 异常，记录详细信息后直接抛出
            logger.error(
                "API Exception during license activation for user_id=%s: %s (%s)",
                user_id,
                type(e).__name__,
                str(e),
            )
            raise
        except Exception as e:
            # 其他未知异常
            logger.exception(
                "Unexpected error activating license for user_id=%s: %s",
                user_id,
                str(e),
            )
            raise AppSumoLicenseActivationError(
                message=f"Error activating license: {str(e)}"
            )
