from models.user import User
from models.usage import Usage
from libs.send_email import send_reset_usage_email


def get_first_name(full_name):
    """Extract first name from full name"""
    return full_name.split()[0] if full_name else "there"


def send_reset_usage_email_by_user_id(user_id: int):
    user = User.get_by_id(user_id)
    usage = Usage.get_by_user_id(user_id)
    first_name = get_first_name(user.full_name)
    send_reset_usage_email(user.email, first_name, usage.transcription_minutes_quota)
