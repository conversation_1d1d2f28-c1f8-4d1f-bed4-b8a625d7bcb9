from datetime import datetime
import logging


from flask_restful import abort
from flask import current_app
from sqlalchemy.orm.attributes import flag_modified

from botocore.exceptions import ClientError, ConnectTimeoutError, ReadTimeoutError


from constants.language import LANGUAGES
from constants.storage import EXPIRE_FOR_READ_URL
from constants.task import TaskStatus, TaskType, TranscriptionType
from constants.transcription import (
    MAX_FILENAME_LENGTH,
    TranscriptionFileStatus,
    TranscriptionFileSourceType,
)
from constants.file_storage import FileState
from models.user import User
from models.user_activity import UserActivity
from libs.id_generator import id_generator
from libs.timeout import (
    with_graceful_degradation,
    with_thread_timeout,
    TimeoutError as CustomTimeoutError,
)
from models import insert_record, db_transaction, db
from models.task import Task
from models.task_result import TaskResult
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from exceptions.storage import FileNotFoundError
from controllers.task import create_transcription_task
from services.entitlement_service import EntitlementService
from exceptions.transcription import InsufficientTranscriptionQuotaError
from exceptions.storage import FreeUserFileCountLimitExceededError

logger = logging.getLogger(__name__)


@db_transaction()
def create_transcription_file(
    user_id,
    filename,
    file_type,
    file_size,
    fingerprint,
    duration,
    language_code=None,
    transcription_type=TranscriptionType.TRANSCRIPT,
    source_type=TranscriptionFileSourceType.UPLOAD,
    source_url=None,
    enable_speaker_diarization=False,
    folder_id=None,
    needs_preprocessing=False,
):
    """创建转录文件记录

    Args:
        user_id: 用户ID
        filename: 文件名
        file_type: 文件类型
        file_size: 文件大小
        fingerprint: 文件MD5指纹
        duration: 音频时长
        language_code: 语言代码
        transcription_type: 转录类型
        source_type: 文件来源类型，默认为 'upload'
        source_url: 媒体来源的URL，根据source_type不同而有不同含义
        folder_id: 文件夹ID，None表示未分类
        needs_preprocessing: 是否需要预处理，默认为False

    Returns:
        TranscriptionFile: 创建的转录文件记录
    """
    # 文件类型交给前端判断，要不然需要同时维护两份文件类型列表
    file_type = file_type.lower()
    if language_code:
        language = LANGUAGES.get(language_code.lower(), None)
        if not language:
            abort(403, message="unsupported language")
    else:
        language = None

    now = datetime.now()
    id_ = id_generator.get_id()
    storage = current_app.storage
    key = storage.generate_key(user_id, fingerprint, file_type)
    # TODO: 废弃 file_url, 因为 file_url 需要动态生成
    file_url = storage.generate_file_url(key)

    # 检查文件长度并截断
    if len(filename) > MAX_FILENAME_LENGTH:
        filename = filename[:MAX_FILENAME_LENGTH]

    # 创建转录文件记录
    transcription_file = TranscriptionFile(
        id=id_,
        user_id=user_id,
        filename=filename,
        file_type=file_type,
        file_url=file_url,
        file_key=key,
        file_size=file_size,
        fingerprint=fingerprint,
        duration=duration,
        language_code=language_code,
        language=language,
        status=TranscriptionFileStatus.uploading.id,
        uploaded_time=None,
        created_time=now,
        updated_time=now,
        transcription_type=transcription_type,
        source_type=source_type,
        source_url=source_url,
        enable_speaker_diarization=enable_speaker_diarization,
        folder_id=folder_id,
        needs_preprocessing=needs_preprocessing,
    )
    insert_record(transcription_file)

    return TranscriptionFile.query.get(id_)


@db_transaction()
def complete_upload(user_id, transcription_file_id):
    start_time = datetime.now()
    # check if transcription file exists
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")
    logger.info(
        f"[file_id={transcription_file_id}] Transcription file existence check took: {datetime.now() - start_time}"
    )

    # check if user is allowed to complete upload
    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to complete upload this file")
    logger.info(
        f"[file_id={transcription_file_id}] User permission check took: {datetime.now() - start_time}"
    )

    # check if transcription file status is uploading
    if transcription_file.status != TranscriptionFileStatus.uploading.id:
        abort(403, message="Transcription file status is not uploading")
    logger.info(
        f"[file_id={transcription_file_id}] Transcription file status check took: {datetime.now() - start_time}"
    )

    # 检查文件是否存在
    storage = current_app.storage
    if not verify_file_integrity(
        storage, transcription_file, transcription_file.file_key
    ):
        logger.error(
            "[file_id=%s] Transcription file not found in file storage",
            transcription_file_id,
        )
        abort(404, message="Transcription file not found in file storage")
    logger.info(
        f"[file_id={transcription_file_id}] File integrity verification took: {datetime.now() - start_time}"
    )

    # 文件上传完成，创建存储记录
    if transcription_file.fingerprint:
        FileStorage.create_or_update_storage(
            transcription_file.user_id,
            transcription_file.fingerprint,
            transcription_file.file_type,
            transcription_file.file_key,
            transcription_file.file_size,
        )
        logger.info(
            f"[file_id={transcription_file_id}] File storage record created/updated took: {datetime.now() - start_time}"
        )

    transcription_file.status = TranscriptionFileStatus.uploaded.id

    transcription_file.uploaded_time = datetime.now()
    logger.info(
        f"[file_id={transcription_file_id}] Transcription file status update took: {datetime.now() - start_time}"
    )

    return transcription_file


# Note: 有其他地方会传入不同的 file_key, 所以需要传入 file_key
def verify_file_integrity(storage, transcription_file, file_key):
    """验证文件存在性

    Args:
        storage: 存储服务实例
        transcription_file: 转录文件对象
        file_key: 文件存储键

    Returns:
        bool: 文件存在性检查结果

    Note:
        此函数只检查文件是否存在于存储中，不再验证 MD5 哈希值。
        包含超时控制和降级策略，在接近 gunicorn 超时限制时会自动降级返回 True。

        原因：前端对超过 2GB 的文件使用伪哈希而非真实 MD5，导致验证必然失败。
    """

    # 简化的文件存在性检查逻辑
    def do_verify():
        start_time = datetime.now()
        logger.info(
            f"[file_id={transcription_file.id}] Starting file existence check for key: {file_key}"
        )

        try:
            response = storage.client.head_object(
                Bucket=storage.config.get_bucket_name(), Key=file_key
            )

            elapsed = (datetime.now() - start_time).total_seconds()
            logger.info(
                f"[file_id={transcription_file.id}] File existence check completed in {elapsed:.2f}s"
            )

            # Debug: 打印 head_object 响应内容
            logger.info(
                f"[file_id={transcription_file.id}] head_object response: {response}"
            )

            # 只要文件存在就返回 True，不再验证 MD5
            actual_size = response.get("ContentLength", 0)
            logger.info(
                f"[file_id={transcription_file.id}] File exists with size: {actual_size} bytes"
            )

            return True

        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                logger.info(
                    f"[file_id={transcription_file.id}] File not found in storage"
                )
                # 直接返回 False，不抛出异常，避免触发降级策略
                return False
            logger.exception(
                f"[file_id={transcription_file.id}] Error checking file existence: {e}"
            )
            raise  # Re-raise other exceptions
        except (ConnectTimeoutError, ReadTimeoutError) as e:
            logger.exception(
                f"[file_id={transcription_file.id}] Timeout error after all retries: {str(e)}"
            )
            raise
        except Exception as e:
            logger.exception(
                f"[file_id={transcription_file.id}] Unexpected error checking file existence: {e}"
            )
            raise

    # 从配置中获取超时时间和降级策略设置
    config = current_app.config.get("FILE_INTEGRITY_CHECK", {})
    timeout_seconds = config.get("timeout_seconds", 10)  # 默认10秒
    enable_degradation = config.get("enable_graceful_degradation", True)  # 默认启用降级

    # 如果启用了降级策略，使用带有优雅降级的超时控制
    if enable_degradation:
        return with_graceful_degradation(
            timeout_seconds,
            do_verify,
            fallback_value=True,
            log_prefix=f"[file_id={transcription_file.id}]",
        )
    else:
        # 不启用降级策略，任何错误都会抛出
        result, error = with_thread_timeout(timeout_seconds, do_verify)

        if error is None:
            return result
        elif isinstance(error, FileNotFoundError):
            # 文件确实不存在，重新抛出异常
            raise error
        elif isinstance(error, CustomTimeoutError):
            logger.error(
                f"[file_id={transcription_file.id}] File integrity check timed out after {timeout_seconds}s and degradation is disabled"
            )
            raise error
        else:
            logger.error(
                f"[file_id={transcription_file.id}] Error during file integrity check: {str(error)}"
            )
            raise error


def list_transcription_files(user_id, cursor, limit):
    status_list = [
        TranscriptionFileStatus.uploading.id,
        TranscriptionFileStatus.uploaded.id,
        TranscriptionFileStatus.preprocessing.id,  # 新增：媒体预处理中
        TranscriptionFileStatus.preprocessing_failed.id,  # 新增：媒体预处理失败
        TranscriptionFileStatus.processing.id,
        TranscriptionFileStatus.partially_completed.id,
        TranscriptionFileStatus.completed.id,
        TranscriptionFileStatus.failed.id,
        TranscriptionFileStatus.completed_with_errors.id,
    ]

    # 获取 limit + 1 条记录
    files = TranscriptionFile.list(user_id, cursor, limit + 1, status_list)

    has_more = len(files) > limit
    if has_more:
        next_cursor = files[limit - 1].id  # 使用第 limit 条记录的 id (去掉多查的那条)
        files = files[:-1]  # 去掉多查的那条记录
    else:
        next_cursor = None

    files = [format_transcription_file(file) for file in files]
    return {
        "items": files,
        "hasMore": has_more,
        "nextCursor": str(next_cursor) if next_cursor else None,
    }


def format_transcription_file(transcription_file):
    storage = current_app.storage

    # Only generate presigned URL if original file is not deleted
    if not transcription_file.original_file_deleted and transcription_file.file_key:
        transcription_file.file_url = storage.generate_presigned_url_for_read(
            transcription_file.file_key,
            EXPIRE_FOR_READ_URL,
        )
    else:
        transcription_file.file_url = ""

    transcription_file.status = TranscriptionFileStatus.by_id(
        transcription_file.status
    ).name

    # 添加文件夹信息
    if transcription_file.folder_id:
        from models.folder import Folder

        folder = Folder.get_by_id(transcription_file.folder_id)
        if folder:
            transcription_file.folder_name = folder.name
        else:
            # 如果文件夹被删除了，设置为未分类
            transcription_file.folder_id = None
            transcription_file.folder_name = None
    else:
        # folder_id 为 None 表示未分类，不属于任何文件夹
        transcription_file.folder_name = None

    return transcription_file


def get_transcription_file_with_result(user_id, transcription_file_id):
    from config import is_development_env

    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to view this file")

    # 需要用 把 file_url 替换为可以访问的 url
    transcription_file = format_transcription_file(transcription_file)

    task_result = TaskResult.get_by_file_id(transcription_file_id)
    if task_result:
        transcription_file.result = task_result
        if task_result.corrected_text:
            transcription_file.result.text = task_result.corrected_text
        else:
            transcription_file.result.text = task_result.original_text

    tasks = Task.get_all_by_file_id(transcription_file_id)
    task_statues = {}
    task_errors = {}
    task_ids = {}  # 新增：任务ID映射
    transcription_time = 0  # 默认转录时间为0秒

    # 初始化为None，避免在非开发环境下返回空字符串
    transcription_file.requested_service_provider = None
    transcription_file.actual_service_provider = None

    for task in tasks:
        task_type_name = TaskType.by_id(task.task_type).name
        task_status = TaskStatus.by_id(task.status).name

        task_statues[task_type_name] = task_status
        task_errors[task_type_name] = task.error_message
        task_ids[task_type_name] = task.id  # 新增：记录任务ID

        # 计算转录任务的耗时（秒）
        if (
            task.task_type == TaskType.transcription.id
            and task.status == TaskStatus.completed.id
        ):
            if task.started_time and task.completed_time:
                # 计算时间差并转换为秒，不足1秒按1秒计算
                time_diff = task.completed_time - task.started_time
                seconds = time_diff.total_seconds()
                transcription_time = max(1, int(seconds))  # 不足1秒按1秒计算

            # 如果是开发环境，添加请求和实际使用的服务提供商信息
            if is_development_env():
                transcription_file.requested_service_provider = (
                    task.requested_service_provider
                )
                transcription_file.actual_service_provider = (
                    task.actual_service_provider
                )

    transcription_file.task_statuses = task_statues
    transcription_file.task_errors = task_errors
    transcription_file.task_ids = task_ids  # 新增：任务ID映射
    transcription_file.transcription_time = transcription_time

    # 添加对齐状态信息，用于前端判断说话人识别是否真正完成
    if transcription_file.enable_speaker_diarization:
        task_result = TaskResult.get_by_file_id(transcription_file.id)
        transcription_file.is_aligned = task_result.is_aligned if task_result else False
    else:
        # 如果没有开启说话人识别，则认为已对齐
        transcription_file.is_aligned = True

    return transcription_file


def simplify_segments(segments):
    """
    segments 里面的 tokens 字段不需要，删掉
    :param segments:
    :return:
    """
    new_segments = []
    for segment in segments:
        del segment["tokens"]
        new_segments.append(segment)
    return new_segments


@db_transaction()
def rename_transcription_file(user_id, transcription_file_id, new_filename):
    # Check if filename length is greater than MAX_FILENAME_LENGTH, if so, truncate it
    if len(new_filename) > MAX_FILENAME_LENGTH:
        new_filename = new_filename[:MAX_FILENAME_LENGTH]

    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to rename this file")

    # Skip update if the filename is the same
    if transcription_file.filename == new_filename:
        return transcription_file

    transcription_file.filename = new_filename
    transcription_file.updated_time = datetime.now()

    return transcription_file


@db_transaction()
def update_language_code(user_id, transcription_file_id, language_code):
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to update language code")

    language = LANGUAGES.get(language_code.lower(), None)
    if not language:
        abort(403, message="Unsupported language")

    transcription_file.language_code = language_code
    transcription_file.language = language
    transcription_file.updated_time = datetime.now()
    return transcription_file


@db_transaction()
def update_transcription_type(user_id, transcription_file_id, transcription_type):
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to update transcription type")

    transcription_file.transcription_type = transcription_type
    transcription_file.updated_time = datetime.now()
    return transcription_file


@db_transaction()
def update_speaker_diarization_setting(
    user_id, transcription_file_id, enable_speaker_diarization
):
    """更新说话人识别设置"""
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="Unauthorized")

    transcription_file.enable_speaker_diarization = enable_speaker_diarization
    transcription_file.updated_time = datetime.now()
    db.session.commit()

    return transcription_file


def list_transcription_files_by_page(
    user_id: int, page: int, page_size: int, offset: int, folder_filter: str = None
):
    """
    分页获取转录文件列表

    Args:
        user_id: 用户ID
        page: 页码
        page_size: 每页大小
        offset: 偏移量
        folder_filter: 文件夹过滤器，'unclassified' 表示获取未分类文件，None 表示获取所有文件
    """
    query = TranscriptionFile.query.filter_by(user_id=user_id, is_deleted=False)

    # 根据文件夹过滤器调整查询条件
    if folder_filter == "unclassified":
        # 只获取未分类的文件（folder_id 为 NULL）
        query = query.filter(TranscriptionFile.folder_id == None)

    # 获取总记录数
    total = query.count()

    # 获取当前页数据
    items = (
        query.order_by(TranscriptionFile.id.desc())
        .offset(offset)
        .limit(page_size)
        .all()
    )
    items = [format_transcription_file(item) for item in items]
    return {
        "items": items,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size,
    }


def _validate_migration_users(anonymous_user_id, target_user_id):
    """验证迁移用户的有效性

    Args:
        anonymous_user_id: 匿名用户ID
        target_user_id: 目标用户ID

    Returns:
        tuple: (anonymous_user, target_user)
    """
    anonymous_user = User.get_by_id(anonymous_user_id)
    if not anonymous_user or not anonymous_user.is_anonymous:
        abort(403, message="Source user must be anonymous")

    target_user = User.get_by_id(target_user_id)
    if not target_user or target_user.is_anonymous:
        abort(403, message="Target user must be regular user")

    return anonymous_user, target_user


def _migrate_file_storage_record(anonymous_user_id, target_user_id, fingerprint):
    """迁移 FileStorage 记录的通用逻辑

    Args:
        anonymous_user_id: 匿名用户ID
        target_user_id: 目标用户ID
        fingerprint: 文件指纹
    """
    if not fingerprint:
        return

    anonymous_fs = FileStorage.get_by_user_id_and_fingerprint_any_state(
        anonymous_user_id, fingerprint
    )
    if not anonymous_fs:
        logger.warning(
            f"No FileStorage record found for anonymous user {anonymous_user_id} with fingerprint {fingerprint}"
        )
        return

    # 检查目标用户是否已经有相同 fingerprint 的记录
    target_fs = FileStorage.get_by_user_id_and_fingerprint_any_state(
        target_user_id, fingerprint
    )

    if target_fs:
        # 目标用户已有相同文件，合并引用计数
        logger.info(
            f"Target user {target_user_id} already has FileStorage record for fingerprint {fingerprint}, "
            f"merging reference counts: {target_fs.reference_count} + {anonymous_fs.reference_count}"
        )
        target_fs.reference_count += anonymous_fs.reference_count
        target_fs.updated_time = datetime.now()
        target_fs.last_access_time = datetime.now()
        # 如果目标文件状态不是活跃的，恢复为活跃状态
        if target_fs.state != FileState.ACTIVE.value:
            target_fs.state = FileState.ACTIVE.value
        # 删除匿名用户的记录
        db.session.delete(anonymous_fs)
    else:
        # 目标用户没有相同文件，直接迁移
        logger.info(
            f"Migrating FileStorage record from user {anonymous_user_id} to user {target_user_id} for fingerprint {fingerprint}"
        )
        anonymous_fs.user_id = target_user_id
        anonymous_fs.updated_time = datetime.now()


@db_transaction()
def migrate_anonymous_transcription_file(new_user, file_id):
    """迁移匿名用户的转录文件到新用户
    Args:
        new_user_id: 新用户ID
        file_id: 文件ID
    Returns:
        TranscriptionFile: 迁移后的文件
    """
    file = TranscriptionFile.get_by_id(file_id)
    if not file:
        abort(404, message="Transcription file not found")

    new_user_id = new_user.id
    if file.user_id == new_user_id:
        return file

    anonymous_user = User.get_by_id(file.user_id)
    if not anonymous_user:
        abort(404, message="Anonymous user not found")

    if not anonymous_user.is_anonymous:
        abort(403, message="User is not anonymous")

    # 检查当前用户的当日转录文件数量。 防止用户利用匿名用户的转录文件进行无限转录
    check_free_user_transcribe_limit(new_user)

    # 标记匿名用户的活跃记录为已合并，避免重复统计
    merged_count = UserActivity.mark_as_merged(anonymous_user.id, new_user_id)
    if merged_count > 0:
        logger.info(
            f"Marked {merged_count} activity records as merged for anonymous user {anonymous_user.id} to user {new_user_id}"
        )

    # 根据文件状态执行迁移操作
    if file.status in [
        TranscriptionFileStatus.completed.id,
        TranscriptionFileStatus.partially_completed.id,
        TranscriptionFileStatus.completed_with_errors.id,
    ]:
        # 已完成的文件：需要额外的用户验证，然后完整迁移
        _validate_migration_users(anonymous_user.id, new_user_id)
        _migrate_file_storage_record(anonymous_user.id, new_user_id, file.fingerprint)
        file = migrate_anonymous_user_data(anonymous_user.id, new_user_id, file_id)
        logger.info(f"Migrated all data for completed file {file_id}")
    else:
        # 未完成的文件：简单迁移
        _migrate_file_storage_record(anonymous_user.id, new_user_id, file.fingerprint)
        file.user_id = new_user_id
        file.updated_time = datetime.now()
        create_transcription_task(new_user_id, file_id)
        logger.info(f"Created transcription task for migrated file {file_id}")

    return file


@db_transaction()
def migrate_anonymous_user_data(anonymous_user_id, target_user_id, file_id):
    """迁移匿名用户的最新文件数据到目标用户（完整迁移，用于已转录的文件）

    注意：FileStorage 记录的迁移由调用方负责处理，此函数不处理 FileStorage

    Args:
        anonymous_user_id: 匿名用户ID
        target_user_id: 目标用户ID
        file_id: 待迁移的文件ID
    """
    # 1. 获取待迁移的文件（用户身份已在调用前验证）
    file = TranscriptionFile.get_by_id(file_id)
    if not file or file.user_id != anonymous_user_id:
        abort(404, message="File not found or not owned by anonymous user")

    # 3. 迁移文件相关数据
    file.user_id = target_user_id
    file.updated_time = datetime.now()

    # FileStorage 记录的迁移由调用方负责处理

    # Task 和 TaskResult 不用迁移，因为没有记录 user_id

    # 4. 更新目标用户权益使用量
    EntitlementService.update_usage(
        user_id=target_user_id, duration=file.duration, file_id=file.id
    )

    logger.info(
        f"Migrated file {file_id} from anonymous user {anonymous_user_id} to user {target_user_id}"
    )

    return file


@db_transaction()
def unlock_transcription(user_id, transcription_file_id):
    """
    解锁之前因额度不足而部分未处理的转录文件

    Args:
        user_id: 用户ID
        transcription_file_id: 转录文件ID

    Returns:
        dict: 成功解锁的文件信息
    """
    # 获取文件并验证所有权
    file = TranscriptionFile.get_by_id(transcription_file_id)
    if not file:
        abort(404, message="Transcription file not found")

    if file.user_id != user_id:
        abort(403, message="Permission denied")

    # 检查是否需要解锁
    if file.insufficient_minutes <= 0:
        return {"message": "File already unlocked", "file_id": transcription_file_id}

    minutes_to_unlock = file.insufficient_minutes

    # 从权益系统扣除额度
    # TODO: 后续文案从 minutes 改成 credits
    try:
        EntitlementService.unlock_file(user_id, minutes_to_unlock, file.id)
    except InsufficientTranscriptionQuotaError as e:
        # 直接重新抛出原始异常，保持403状态码
        raise
    except Exception as e:
        logger.error(f"Failed to update entitlement for unlock: {str(e)}")
        raise

    # 更新文件状态
    file.insufficient_minutes = 0

    return {
        "message": "File unlocked successfully",
        "file_id": transcription_file_id,
        "minutes_unlocked": minutes_to_unlock,
    }


def check_free_user_transcribe_limit(user):
    """
    检查用户的每日转录文件数量限制
    Args:
        user: 用户对象
    Raises:
        FreeUserFileCountLimitExceededError: 当用户超过每日转录限制时
    """
    today_upload_count = TranscriptionFile.get_user_today_transcribe_file_count(user.id)
    daily_limit = user.daily_transcribe_limit

    if today_upload_count >= daily_limit:
        if user.is_anonymous:
            message = f"Anonymous users can only transcribe {daily_limit} files per day. Please sign in to transcribe more files."
        else:
            message = f"Free users can only transcribe {daily_limit} files per day. Subscribe or purchase a one-time package to transcribe more files."

        raise FreeUserFileCountLimitExceededError(message=message)


@db_transaction()
def update_segment_text(user_id, transcription_file_id, segment_id, new_text):
    """更新单个segment的文本内容

    Args:
        user_id: 用户ID
        transcription_file_id: 转录文件ID
        segment_id: 要更新的segment ID
        new_text: 新的文本内容

    Returns:
        dict: 更新后的segment对象

    Raises:
        404: 如果文件或segment不存在
        403: 如果用户无权编辑该文件
    """
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to edit this file")

    task_result = TaskResult.get_by_file_id(transcription_file_id)
    if not task_result:
        abort(404, message="Task result not found")

    # 获取segments
    segments = task_result.segments

    # 查找并更新指定的segment
    segment_found = False
    for segment in segments:
        # 确保类型一致，segment_id可能是字符串
        segment_id_int = int(segment_id)
        if segment["id"] == segment_id_int:
            # 保存原始文本，可以用于日志记录
            original_text = segment["text"]
            logger.info(
                "Updating segment %s for file %s. Original text: '%s', New text: '%s'",
                segment_id,
                transcription_file_id,
                original_text,
                new_text,
            )

            # 更新文本
            segment["text"] = new_text
            segment_found = True

            # 添加更多日志，帮助调试
            logger.info(
                "Segment %s updated successfully. New segment data: %s",
                segment_id,
                segment,
            )
            break

    if not segment_found:
        abort(404, message=f"Segment with ID {segment_id} not found")

    # 将修改后的segments赋值回task_result
    task_result.segments = segments
    # 踩坑：使用flag_modified显式标记segments字段已修改。直接修改JSON字段不会触发 SQLAlchemy 的变化检测机制，因此需要手动标记。
    flag_modified(task_result, "segments")
    logger.info("Marked segments field as modified")

    # 返回更新后的segment
    for segment in segments:
        if segment["id"] == int(segment_id):
            return {
                "id": segment["id"],
                "start_time": segment["start_time"],
                "end_time": segment["end_time"],
                "text": segment["text"],
                "speaker": segment.get("speaker", ""),
            }

    # 理论上不应该到达这里
    abort(500, message="Failed to retrieve updated segment")


@db_transaction()
def update_segments_batch(user_id, transcription_file_id, segment_updates):
    """批量更新segments的文本内容

    Args:
        user_id: 用户ID
        transcription_file_id: 转录文件ID
        segment_updates: 要更新的segment列表，格式为 [{"id": 1, "text": "新文本"}, ...]

    Returns:
        dict: 包含更新结果的字典

    Raises:
        404: 如果文件或任何segment不存在
        403: 如果用户无权编辑该文件
        400: 如果请求数据格式错误
    """
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to edit this file")

    task_result = TaskResult.get_by_file_id(transcription_file_id)
    if not task_result:
        abort(404, message="Task result not found")

    # 验证输入数据
    if not segment_updates or not isinstance(segment_updates, list):
        abort(400, message="Invalid segment updates data")

    # 获取segments
    segments = task_result.segments

    # 创建segment_id到segment的映射，便于快速查找
    segment_map = {segment["id"]: segment for segment in segments}

    # 验证所有要更新的segment_id都存在
    missing_segment_ids = []
    for update in segment_updates:
        if not isinstance(update, dict) or "id" not in update or "text" not in update:
            abort(
                400, message="Each segment update must contain 'id' and 'text' fields"
            )

        segment_id = int(update["id"])
        if segment_id not in segment_map:
            missing_segment_ids.append(segment_id)

    if missing_segment_ids:
        abort(404, message=f"Segments with IDs {missing_segment_ids} not found")

    # 执行批量更新
    updated_segments = []
    for update in segment_updates:
        segment_id = int(update["id"])
        new_text = update["text"]

        # 找到对应的segment并更新
        segment = segment_map[segment_id]
        original_text = segment["text"]

        logger.info(
            "Batch updating segment %s for file %s. Original text: '%s', New text: '%s'",
            segment_id,
            transcription_file_id,
            original_text,
            new_text,
        )

        # 通过引用直接更新segment
        segment["text"] = new_text
        updated_segments.append(
            {
                "id": segment["id"],
                "start_time": segment["start_time"],
                "end_time": segment["end_time"],
                "text": segment["text"],
                "speaker": segment.get("speaker", ""),
            }
        )

    # 将修改后的segments赋值回task_result
    task_result.segments = segments
    # 踩坑：使用flag_modified显式标记segments字段已修改
    flag_modified(task_result, "segments")
    logger.info("Marked segments field as modified for batch update")

    return {
        "updated_count": len(updated_segments),
        "segments": updated_segments,
    }


@db_transaction()
def update_speakers_batch(user_id, transcription_file_id, speaker_updates):
    """批量更新segments的说话人信息

    Args:
        user_id: 用户ID
        transcription_file_id: 转录文件ID
        speaker_updates: 要更新的说话人列表，格式为 [{"id": 1, "speaker": "A"}, ...]

    Returns:
        dict: 包含更新结果的字典

    Raises:
        404: 如果文件或任何segment不存在
        403: 如果用户无权编辑该文件
        400: 如果请求数据格式错误
    """
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to edit this file")

    task_result = TaskResult.get_by_file_id(transcription_file_id)
    if not task_result:
        abort(404, message="Task result not found")

    # 验证输入数据
    if not speaker_updates or not isinstance(speaker_updates, list):
        abort(400, message="Invalid speaker updates data")

    # 统一操作 segments 字段
    segments = task_result.segments
    logger.info(
        "Updating speakers in segments for file %s",
        transcription_file_id,
    )

    # 创建segment_id到segment的映射，便于快速查找
    segment_map = {segment["id"]: segment for segment in segments}

    # 验证所有要更新的segment_id都存在
    missing_segment_ids = []
    for update in speaker_updates:
        if (
            not isinstance(update, dict)
            or "id" not in update
            or "speaker" not in update
        ):
            abort(
                400,
                message="Each speaker update must contain 'id' and 'speaker' fields",
            )

        segment_id = int(update["id"])
        if segment_id not in segment_map:
            missing_segment_ids.append(segment_id)

    if missing_segment_ids:
        abort(404, message=f"Segments with IDs {missing_segment_ids} not found")

    # 执行批量更新
    updated_segments = []
    for update in speaker_updates:
        segment_id = int(update["id"])
        new_speaker = update["speaker"]

        # 找到对应的segment并更新
        segment = segment_map[segment_id]
        original_speaker = segment.get("speaker", "None")

        logger.info(
            "Batch updating speaker for segment %s in file %s. Original speaker: '%s', New speaker: '%s'",
            segment_id,
            transcription_file_id,
            original_speaker,
            new_speaker,
        )

        # 通过引用直接更新segment
        segment["speaker"] = new_speaker
        updated_segments.append(
            {
                "id": segment["id"],
                "start_time": segment["start_time"],
                "end_time": segment["end_time"],
                "text": segment["text"],
                "speaker": segment["speaker"],
            }
        )

    # 将修改后的segments赋值回task_result
    task_result.segments = segments
    flag_modified(task_result, "segments")
    logger.info("Marked segments field as modified for speaker batch update")

    return {
        "updated_count": len(updated_segments),
        "segments": updated_segments,
    }
