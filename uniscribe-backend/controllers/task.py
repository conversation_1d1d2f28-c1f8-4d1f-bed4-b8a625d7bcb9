import logging
from datetime import datetime

from flask import current_app
from flask_restful import abort
from sqlalchemy import insert

from constants.storage import EXPIRE_FOR_READ_URL
from constants.task import TaskStatus, TaskType, MAX_RETRY_COUNT, SUPPORT_EMAIL
from exceptions.task import (
    TaskNotFoundError,
    TaskPermissionDeniedError,
    TaskStatusNotRetryableError,
    TaskRetryLimitExceededError,
    TaskFileNotFoundError,
    TaskUnsupportedTypeError
)
from constants.transcription import TranscriptionFileStatus, TranscriptionFileSourceType
from constants.language import LANGUAGES
from controllers.usage import check_transcription_quota
from exceptions.transcription import InsufficientTranscriptionQuotaError
from libs.id_generator import id_generator
from models import (
    db_transaction,
    insert_record,
    insert_records,
    db_transaction_with_callbacks,
    add_transaction_callback,
)
from services.grayscale_service import get_consumption_mode
from models.task import Task
from models.task_result import TaskResult
from models.transcription_file import TranscriptionFile
from models.user import User
from models import db
from services.entitlement_service import EntitlementService
from services.task_queue_service import enqueue_task

logger = logging.getLogger(__name__)


def _enqueue_task_with_logging(task_type, task_data):
    """Task enqueue function with logging"""
    task_id = task_data.get("id", "unknown")
    file_id = task_data.get("file_id", "unknown")

    try:
        enqueue_task(task_type, task_data)
        # Success logs are recorded internally by enqueue_task, including specific stream name
    except Exception as e:
        logger.error(
            f"{task_type} task failed to join queue: task_id={task_id}, file_id={file_id}, error={str(e)}"
        )


def _requires_separate_diarization_task(selected_model):
    """
    判断选择的模型是否需要额外的说话人识别任务

    Args:
        selected_model: 选择的转录模型标识符

    Returns:
        bool: True 表示需要额外的说话人识别任务，False 表示不需要
    """
    # 导入放在函数内部避免循环导入
    from services.model_selection_service import ModelSelectionService

    # 一体化说话人识别模型，不需要额外的说话人识别任务
    integrated_diarization_models = [
        ModelSelectionService.REPLICATE_WHISPER_DIARIZATION,
        ModelSelectionService.FAL_WHISPER_DIARIZATION,
        ModelSelectionService.REPLICATE_WHISPERX_DIARIZATION,
        # 未来可能还有其他一体化模型
    ]

    return selected_model not in integrated_diarization_models


def _create_transcription_task_internal(user_id, transcription_file_id, skip_preprocessing_check=False):
    """内部转录任务创建函数，支持跳过预处理检查"""
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(403, message="User not allowed to create_transcription_task this file")

    # 检查是否需要媒体预处理（除非明确跳过）
    if not skip_preprocessing_check:
        # 首先检查是否前端标记需要预处理
        if transcription_file.needs_preprocessing:
            logger.info(f"File needs preprocessing (requested by frontend): file_id={transcription_file_id}")
            # 创建 ffmpeg 预处理任务（会在预处理过程中更新 duration）
            return create_media_preprocessing_task(
                user_id,
                transcription_file_id,
                preprocessing_type="ffmpeg",
                preprocessing_reason="frontend_requested"
            )

        # YouTube文件已经在下载时处理过了，跳过预处理检查
        if transcription_file.source_type == TranscriptionFileSourceType.YOUTUBE:
            logger.info(f"Skip preprocessing check for YouTube file: file_id={transcription_file_id}")
        else:
            # 只对非YouTube文件进行预处理检查
            from libs.ffmpeg_preprocessing import FFmpegPreprocessingTrigger
            should_preprocess, reason = FFmpegPreprocessingTrigger.should_preprocess(transcription_file)

            if should_preprocess:
                logger.info(f"File requires media preprocessing: file_id={transcription_file_id}, reason={reason}")
                # 创建媒体预处理任务而不是转录任务
                return create_media_preprocessing_task(
                    user_id,
                    transcription_file_id,
                    preprocessing_type="ffmpeg",
                    preprocessing_reason=reason or "unknown"
                )

    logger.info(f"File does not require preprocessing, creating transcription task directly: file_id={transcription_file_id}")

    # 获取用户信息以确定优先级
    user = User.get_by_id(user_id)
    if not user:
        abort(404, message="User not found")

    # 根据用户类型设置优先级：付费用户=1，免费用户=0
    priority = 1 if user.has_paid_plan else 0

    # 生成任务
    task_id = id_generator.get_id()

    status = TaskStatus.pending.id
    error_message = ""
    try:
        check_transcription_quota(user_id, transcription_file.duration)
    except InsufficientTranscriptionQuotaError as e:
        status = TaskStatus.failed.id
        error_message = e.description

    # 使用模型选择服务确定合适的模型
    from services.model_selection_service import ModelSelectionService

    selected_model = ModelSelectionService.select_transcription_model(
        user_id, transcription_file
    )

    # 决定消费模式（灰度逻辑）
    consumption_mode = get_consumption_mode(
        user_id, transcription_file_id, TaskType.transcription.id
    )

    values = {
        "id": task_id,
        "file_id": transcription_file_id,
        "status": status,
        "task_type": TaskType.transcription.id,
        "priority": priority,
        "error_message": error_message,
        "transcription_type": transcription_file.transcription_type,
        "requested_service_provider": selected_model,
        "consumption_mode": consumption_mode,
    }

    stmt = insert(Task).prefix_with("IGNORE").values(**values)
    db.session.execute(stmt)
    db.session.flush()

    # 获取创建的任务
    task = Task.get_by_file_id(file_id=transcription_file_id)

    # 如果任务状态为 pending 且使用队列模式，将其加入队列
    if status == TaskStatus.pending.id and consumption_mode == "redis_queue":
        # 生成可访问的预签名 URL
        storage = current_app.storage
        file_url = storage.generate_presigned_url_for_read(
            transcription_file.file_key,
            EXPIRE_FOR_READ_URL,
        )

        task_data = {
            "id": task_id,
            "task_type": TaskType.transcription.id,
            "file_id": transcription_file_id,
            "file_url": file_url,  # 使用预签名 URL
            "file_duration": transcription_file.duration,
            "language_code": transcription_file.language_code,
            "transcription_type": transcription_file.transcription_type,
            "requested_service_provider": selected_model,
            "priority": priority,
            "created_time": datetime.now().isoformat(),
        }

        # 使用事务后回调确保任务在数据库提交后才入队
        add_transaction_callback(
            lambda: _enqueue_task_with_logging("transcription", task_data)
        )

        logger.info(f"Transcription task will use queue mode: task_id={task_id}, user_id={user_id}")
    elif status == TaskStatus.pending.id:
        logger.info(f"Transcription task will use HTTP polling mode: task_id={task_id}, user_id={user_id}")

    # 如果开启了说话人识别，根据选择的模型决定是否需要创建额外的说话人识别任务
    if transcription_file.enable_speaker_diarization:
        # 检查选择的模型是否需要额外的说话人识别任务
        if _requires_separate_diarization_task(selected_model):
            create_speaker_diarization_task(user_id, transcription_file_id)

    return task


@db_transaction_with_callbacks
def create_transcription_task(user_id, transcription_file_id):
    """创建转录任务（公共接口，会检查是否需要预处理）"""
    return _create_transcription_task_internal(user_id, transcription_file_id, skip_preprocessing_check=False)


@db_transaction_with_callbacks
def create_transcription_task_after_preprocessing(user_id, transcription_file_id):
    """媒体预处理完成后创建转录任务（跳过预处理检查）"""
    return _create_transcription_task_internal(user_id, transcription_file_id, skip_preprocessing_check=True)


@db_transaction_with_callbacks
def create_speaker_diarization_task(user_id, transcription_file_id):
    """创建说话人识别任务"""
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    user = User.get_by_id(user_id)
    if not user:
        abort(404, message="User not found")

    # 根据用户类型设置优先级：付费用户=1，免费用户=0
    priority = 1 if user.has_paid_plan else 0

    # 生成任务
    task_id = id_generator.get_id()

    # 决定消费模式（灰度逻辑）
    consumption_mode = get_consumption_mode(
        user_id, transcription_file_id, TaskType.speaker_diarization.id
    )

    values = {
        "id": task_id,
        "file_id": transcription_file_id,
        "status": TaskStatus.pending.id,
        "task_type": TaskType.speaker_diarization.id,
        "priority": priority,
        "error_message": "",
        "consumption_mode": consumption_mode,
    }

    stmt = insert(Task).prefix_with("IGNORE").values(**values)
    db.session.execute(stmt)
    db.session.flush()

    # 获取创建的任务
    task = Task.get_by_file_id_and_type(
        file_id=transcription_file_id, task_type=TaskType.speaker_diarization.id
    )

    # 如果使用队列模式，将任务加入队列
    if consumption_mode == "redis_queue":
        # 生成可访问的预签名 URL
        storage = current_app.storage
        file_url = storage.generate_presigned_url_for_read(
            transcription_file.file_key,
            EXPIRE_FOR_READ_URL,
        )

        task_data = {
            "id": task_id,
            "task_type": TaskType.speaker_diarization.id,
            "file_id": transcription_file_id,
            "file_url": file_url,  # 使用预签名 URL
            "file_duration": transcription_file.duration,
            "language_code": transcription_file.language_code,
            "priority": priority,
            "created_time": datetime.now().isoformat(),
        }

        # 使用事务后回调确保任务在数据库提交后才入队
        add_transaction_callback(
            lambda: _enqueue_task_with_logging("speaker_diarization", task_data)
        )

        logger.info(
            f"Speaker diarization task will use queue mode: task_id={task_id}, user_id={user_id}"
        )
    else:
        logger.info(
            f"Speaker diarization task will use HTTP polling mode: task_id={task_id}, user_id={user_id}"
        )

    return task


@db_transaction_with_callbacks
def create_text_tasks(transcription_file_id):
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    user = User.get_by_id(transcription_file.user_id)
    if not user:
        abort(404, message="User not found")

    # Extract transcription text from segments using the common function
    try:
        transcription_text = extract_transcription_text_from_segments(transcription_file_id)
    except ValueError as e:
        abort(400, message=str(e))

    # 根据用户类型设置优先级：付费用户=1，免费用户=0
    priority = 1 if user.has_paid_plan else 0

    task_types = [
        TaskType.summary.id,
        TaskType.outline.id,
        TaskType.qa_extraction.id,
    ]

    tasks = []
    created_task_ids = []

    for task_type in task_types:
        task_id = id_generator.get_id()

        # 决定消费模式（灰度逻辑）
        consumption_mode = get_consumption_mode(
            user.id, transcription_file_id, task_type
        )

        task = Task(
            id=task_id,
            file_id=transcription_file_id,
            status=TaskStatus.pending.id,
            task_type=task_type,
            priority=priority,
            consumption_mode=consumption_mode,
        )
        tasks.append(task)
        created_task_ids.append((task_id, consumption_mode))

    insert_records(tasks)

    # 将文本任务加入队列 - 使用事务后回调（仅队列模式）
    for (task_id, consumption_mode), task_type in zip(created_task_ids, task_types):
        if consumption_mode == "redis_queue":
            task_data = {
                "id": task_id,
                "task_type": task_type,
                "file_id": transcription_file_id,
                "transcription_text": transcription_text,  # 添加转录文本
                "priority": priority,
                "created_time": datetime.now().isoformat(),
            }

            # 使用事务后回调确保任务在数据库提交后才入队
            add_transaction_callback(
                lambda td=task_data: _enqueue_task_with_logging("text", td)
            )

            logger.info(
                f"Text task will use queue mode: task_id={task_id}, task_type={task_type}"
            )
        else:
            logger.info(
                f"Text task will use HTTP polling mode: task_id={task_id}, task_type={task_type}"
            )

    # 提取任务ID用于返回
    task_ids_only = [task_id for task_id, _ in created_task_ids]

    return Task.get_by_ids(task_ids_only)


@db_transaction()
def get_next_task(task_type_filter=None):
    """获取下一个任务

    Args:
        task_type_filter: 任务类型过滤器，可选值：
            - 'transcription': 只获取转录任务
            - 'text': 只获取文本处理任务（摘要、大纲、问答等）
            - 'speaker_diarization': 只获取说话人识别任务
            - None: 获取任何类型的任务（保持向后兼容）
    """
    if task_type_filter == "transcription":
        next_task = Task.get_next_transcription_task()
    elif task_type_filter == "text":
        next_task = Task.get_next_text_task()
    elif task_type_filter == "speaker_diarization":
        next_task = Task.get_next_speaker_diarization_task()
    else:
        next_task = Task.get_next_task()

    if not next_task:
        abort(404, message="No task found")

    # 修改任务状态
    next_task.status = TaskStatus.processing.id
    next_task.started_time = datetime.now()

    # get file_url
    file = TranscriptionFile.get_by_id(next_task.file_id)
    if not file:
        db.session.delete(next_task)
        db.session.commit()
        logger.error("Deleted task %s due to missing file", next_task.id)
        abort(404, message=f"Transcription file not found: {next_task.file_id}")
    # file_url 转成可以公开读取的 url
    storage = current_app.storage
    file_url = storage.generate_presigned_url_for_read(
        file.file_key,
        EXPIRE_FOR_READ_URL,
    )
    next_task.file_url = file_url
    next_task.file_duration = file.duration
    next_task.language_code = file.language_code
    next_task.language = file.language
    return next_task


def _get_task_status_summary(tasks, file):
    """获取任务状态摘要"""
    # 获取关键任务
    transcription_task = next(
        (t for t in tasks if t.task_type == TaskType.transcription.id), None
    )
    diarization_task = next(
        (t for t in tasks if t.task_type == TaskType.speaker_diarization.id), None
    )
    media_preprocessing_task = next(
        (t for t in tasks if t.task_type == TaskType.media_preprocessing.id), None
    )

    # 转录任务状态
    transcription_status = transcription_task.status if transcription_task else None

    # 媒体预处理任务状态
    media_preprocessing_status = media_preprocessing_task.status if media_preprocessing_task else None

    # 说话人识别任务状态（如果开启了）
    diarization_status = None
    if file.enable_speaker_diarization and diarization_task:
        diarization_status = diarization_task.status

    # 检查对齐状态
    alignment_completed = True
    if file.enable_speaker_diarization:
        result = TaskResult.get_by_file_id(file.id)
        if not result or not result.diarization_segments:
            # 没有说话人识别结果，对齐肯定没完成
            alignment_completed = False
        elif result.diarization_segments and not result.is_aligned:
            # 有说话人识别结果但还没有对齐
            alignment_completed = False

    # 检查所有任务状态
    all_pending = all(t.status == TaskStatus.pending.id for t in tasks)
    any_processing = any(t.status == TaskStatus.processing.id for t in tasks)
    all_finalized = all(
        t.status in [TaskStatus.completed.id, TaskStatus.failed.id] for t in tasks
    )
    all_completed = all(t.status == TaskStatus.completed.id for t in tasks)

    # 检查预期任务是否都存在
    expected_task_types = [
        TaskType.transcription.id,
        TaskType.summary.id,
        TaskType.outline.id,
        TaskType.qa_extraction.id,
    ]
    if file.source_type == TranscriptionFileSourceType.YOUTUBE:
        expected_task_types.append(TaskType.media_preprocessing.id)
    if file.enable_speaker_diarization:
        expected_task_types.append(TaskType.speaker_diarization.id)

    task_types = [t.task_type for t in tasks]
    all_expected_tasks_exist = all(
        task_type in task_types for task_type in expected_task_types
    )

    return {
        "transcription_status": transcription_status,
        "media_preprocessing_status": media_preprocessing_status,
        "diarization_status": diarization_status,
        "alignment_completed": alignment_completed,
        "all_pending": all_pending,
        "any_processing": any_processing,
        "all_finalized": all_finalized,
        "all_completed": all_completed,
        "all_expected_tasks_exist": all_expected_tasks_exist,
        "critical_task_failed": critical_task_failed(tasks),
    }


@db_transaction()
def update_file_status(file_id):
    file = TranscriptionFile.get_by_id(file_id)
    if not file:
        abort(404, message="Transcription file not found")

    # 记录之前的状态，用于判断是否需要发送 webhook
    previous_status = file.status

    tasks = Task.get_all_by_file_id(file.id)
    status = _get_task_status_summary(tasks, file)

    # 状态判断逻辑（按优先级排序）

    # 1. 媒体预处理失败
    if status["media_preprocessing_status"] == TaskStatus.failed.id:
        file.status = TranscriptionFileStatus.preprocessing_failed.id
        return

    # 2. 媒体预处理进行中
    if status["media_preprocessing_status"] == TaskStatus.processing.id:
        file.status = TranscriptionFileStatus.preprocessing.id
        return

    # 3. 所有任务都是 pending 状态
    if status["all_pending"]:
        file.status = TranscriptionFileStatus.uploaded.id
        _send_webhook_if_needed(file, previous_status)
        return

    # 4. 关键任务失败
    if status["critical_task_failed"]:
        file.status = TranscriptionFileStatus.failed.id
        _send_webhook_if_needed(file, previous_status)
        return

    # 5. 转录任务失败
    if status["transcription_status"] == TaskStatus.failed.id:
        file.status = TranscriptionFileStatus.failed.id
        _send_webhook_if_needed(file, previous_status)
        return

    # 6. 转录任务正在处理中
    if status["transcription_status"] == TaskStatus.processing.id:
        file.status = TranscriptionFileStatus.processing.id
        _send_webhook_if_needed(file, previous_status)
        return

    # 7. 转录任务完成
    if status["transcription_status"] == TaskStatus.completed.id:
        if file.enable_speaker_diarization:
            if status["diarization_status"] == TaskStatus.completed.id:
                # 转录和说话人识别都完成
                if not status["alignment_completed"]:
                    file.status = TranscriptionFileStatus.processing.id  # 对齐进行中
                elif status["all_finalized"] and status["all_expected_tasks_exist"]:
                    file.status = (
                        TranscriptionFileStatus.completed.id
                        if status["all_completed"]
                        else TranscriptionFileStatus.completed_with_errors.id
                    )
                else:
                    file.status = TranscriptionFileStatus.partially_completed.id
            else:
                # 转录完成，说话人识别未完成
                file.status = TranscriptionFileStatus.partially_completed.id
        else:
            # 没有开启说话人识别
            if status["all_finalized"] and status["all_expected_tasks_exist"]:
                file.status = (
                    TranscriptionFileStatus.completed.id
                    if status["all_completed"]
                    else TranscriptionFileStatus.completed_with_errors.id
                )
            else:
                file.status = TranscriptionFileStatus.partially_completed.id
        _send_webhook_if_needed(file, previous_status)
        return

    # 6. 有任务正在处理中
    if status["any_processing"]:
        file.status = TranscriptionFileStatus.processing.id
        _send_webhook_if_needed(file, previous_status)
        return

    # 7. 默认状态
    file.status = TranscriptionFileStatus.processing.id
    _send_webhook_if_needed(file, previous_status)


def critical_task_failed(tasks):
    critical_task_types = [
        TaskType.transcription.id,
        TaskType.youtube_download.id,  # YouTube 下载也是关键任务
        TaskType.media_preprocessing.id,  # 媒体预处理也是关键任务
    ]
    return any(
        task.task_type in critical_task_types and task.status == TaskStatus.failed.id
        for task in tasks
    )


def _send_webhook_if_needed(file, previous_status):
    """检查是否需要发送 webhook 通知"""
    if not file.webhook_url:
        return

    # 只在状态变为最终状态时发送 webhook
    current_status = file.status
    final_success_statuses = [
        TranscriptionFileStatus.completed.id,
        TranscriptionFileStatus.completed_with_errors.id,
    ]
    final_failure_statuses = [TranscriptionFileStatus.failed.id]

    # 检查是否从非最终状态变为最终状态
    is_now_success = current_status in final_success_statuses
    is_now_failure = current_status in final_failure_statuses
    was_final = (
        previous_status in final_success_statuses
        or previous_status in final_failure_statuses
    )

    if (is_now_success or is_now_failure) and not was_final:
        # 异步发送 webhook，避免阻塞主流程
        from services.webhook_service import WebhookService

        try:
            WebhookService.send_completion_webhook(file, success=is_now_success)
        except Exception as e:
            logger.error(f"Failed to send webhook for file {file.id}: {str(e)}")



@db_transaction_with_callbacks
def create_media_preprocessing_task(
    user_id, transcription_file_id, preprocessing_type="youtube", preprocessing_reason="unknown",
    youtube_url=None, title=None
):
    """创建媒体预处理任务"""
    transcription_file = TranscriptionFile.get_by_id(transcription_file_id)
    if not transcription_file:
        abort(404, message="Transcription file not found")

    if transcription_file.user_id != user_id:
        abort(
            403,
            message="User not allowed to create media preprocessing task for this file",
        )

    # 获取用户信息以确定优先级
    user = User.get_by_id(user_id)
    if not user:
        abort(404, message="User not found")

    # 根据用户类型设置优先级：付费用户=1，免费用户=0
    priority = 1 if user.has_paid_plan else 0

    # 生成任务
    task_id = id_generator.get_id()

    values = {
        "id": task_id,
        "file_id": transcription_file_id,
        "status": TaskStatus.pending.id,  # 设为待处理，等待队列消费
        "task_type": TaskType.media_preprocessing.id,
        "priority": priority,
        "error_message": "",
        "created_time": datetime.now(),
        "consumption_mode": "redis_queue",  # 媒体预处理任务固定使用队列模式
    }

    stmt = insert(Task).prefix_with("IGNORE").values(**values)
    db.session.execute(stmt)
    db.session.flush()

    # 无论是新插入还是已存在，都获取任务
    task = Task.get_by_file_id_and_type(
        file_id=transcription_file_id, task_type=TaskType.media_preprocessing.id
    )

    if task:
        # 使用实际任务的 ID，而不是新生成的 task_id
        actual_task_id = task.id
        logger.info(f"Task found: generated_task_id={task_id}, actual_task_id={actual_task_id}")

        # 准备队列消息数据
        task_data = {
            # 核心字段
            'id': str(actual_task_id),  # 使用实际任务的 ID
            'task_type': str(TaskType.media_preprocessing.id),
            'file_id': str(transcription_file_id),
            'user_id': str(user_id),
            'priority': str(priority),
            'created_time': datetime.now().isoformat(),

            # 预处理特定字段
            'preprocessing_type': preprocessing_type,
            'preprocessing_reason': preprocessing_reason,
            'file_url': transcription_file.file_url or '',
            'filename': transcription_file.filename or '',
            'file_size': str(transcription_file.file_size or 0),
            'file_duration': str(transcription_file.duration or 0),
        }

        # 如果是YouTube任务，添加YouTube特有字段
        if preprocessing_type == "youtube" and youtube_url:
            task_data.update({
                'youtube_url': youtube_url,
            })

        # 如果是URL下载任务，添加source_url字段
        if preprocessing_type == "url_download" and transcription_file.source_url:
            task_data.update({
                'source_url': transcription_file.source_url,
            })

        logger.info(f"Task data prepared: {task_data}")

        # 使用事务后回调确保任务在数据库提交后才入队
        add_transaction_callback(
            lambda: _enqueue_task_with_logging("media_preprocessing", task_data)
        )

        logger.info(f"Media preprocessing task created: task_id={actual_task_id}, type={preprocessing_type}, reason={preprocessing_reason}")

    return task


@db_transaction()
def save_task_result(
    task_id,
    task_type,
    duration=0,  # 废弃了
    language="",  # 废弃了
    original_text=None,
    segments=None,
    summary=None,
    outline=None,
    qa_extraction=None,
    error_message=None,
    detected_language=None,
    service_provider=None,  # 实际使用的服务提供商/模型
    diarization_segments=None,  # 说话人识别分段
):
    task = Task.get_by_id(task_id)
    if not task:
        abort(404, message="Task not found")

    if task.task_type != task_type:
        abort(403, message="Task type mismatch")

    if error_message:
        task.error_message = error_message
        task.status = TaskStatus.failed.id
        return

    file = TranscriptionFile.get_by_id(task.file_id)
    if not file:
        abort(404, message="Transcription file not found")
    task.status = TaskStatus.completed.id
    task.completed_time = datetime.now()
    task.error_message = ""

    # 记录实际使用的模型
    if service_provider:
        task.actual_service_provider = service_provider

    if detected_language:
        detected_language = LANGUAGES.get(detected_language.lower(), None)

    if task_type == TaskType.transcription.id:
        # 允许多次提交
        result = TaskResult.get_by_file_id(file.id)
        if result:
            result.duration = duration
            result.language = language
            result.original_text = original_text
            result.segments = segments
            result.detected_language = detected_language
            # 如果没有开启说话人识别，直接保存原始结果到 original_segments 并标记为已对齐
            if not file.enable_speaker_diarization:
                result.original_segments = segments
                result.is_aligned = True
        else:
            result = TaskResult(
                id=id_generator.get_id(),
                file_id=file.id,
                duration=duration,
                language=language,
                original_text=original_text,
                segments=segments,
                detected_language=detected_language,
                # 如果没有开启说话人识别，直接保存原始结果到 original_segments 并标记为已对齐
                original_segments=(
                    segments if not file.enable_speaker_diarization else None
                ),
                is_aligned=not file.enable_speaker_diarization,
            )
            insert_record(result)

        # 更新用户权益使用量
        try:
            EntitlementService.update_usage(
                user_id=file.user_id, duration=file.duration, file_id=file.id
            )
        except Exception as e:
            logger.error(
                "Failed to update entitlement for user %s: %s", file.user_id, str(e)
            )
            raise

        # 显式提交事务，确保数据已保存到数据库
        db.session.commit()

        # 在事务提交后检查是否需要触发对齐任务
        _check_and_trigger_alignment(file.id)

    elif task_type == TaskType.summary.id:
        result = TaskResult.get_by_file_id(file.id)
        result.summary = summary
    elif task_type == TaskType.outline.id:
        result = TaskResult.get_by_file_id(file.id)
        result.outline = outline
    elif task_type == TaskType.qa_extraction.id:
        result = TaskResult.get_by_file_id(file.id)
        result.qa_extraction = qa_extraction
    elif task_type == TaskType.speaker_diarization.id:
        result = TaskResult.get_by_file_id(file.id)
        if result:
            result.diarization_segments = diarization_segments
        else:
            # 如果还没有转录结果，先创建一个基础记录
            result = TaskResult(
                id=id_generator.get_id(),
                file_id=file.id,
                duration=0,  # 将在转录任务完成时更新
                language="",  # 将在转录任务完成时更新
                original_text="",  # 将在转录任务完成时更新
                segments=[],  # 将在转录任务完成时更新
                diarization_segments=diarization_segments,
            )
            insert_record(result)

        # 显式提交事务，确保数据已保存到数据库
        db.session.commit()

        # 在事务提交后检查是否需要触发对齐任务
        _check_and_trigger_alignment(file.id)

    return


def _check_and_trigger_alignment(file_id):
    """检查转录和说话人识别任务是否都完成，如果是则触发对齐任务"""
    from services.alignment_queue_service import AlignmentQueueService

    # 检查转录任务和说话人识别任务是否都完成
    transcription_task = Task.get_by_file_id_and_type(
        file_id, TaskType.transcription.id
    )
    diarization_task = Task.get_by_file_id_and_type(
        file_id, TaskType.speaker_diarization.id
    )

    if (
        transcription_task
        and transcription_task.status == TaskStatus.completed.id
        and diarization_task
        and diarization_task.status == TaskStatus.completed.id
    ):

        # 检查是否已经有对齐结果
        # 使用 refresh=True 确保获取最新数据，避免在同一事务中的缓存问题
        result = TaskResult.get_by_file_id(file_id, refresh=True)
        if result and not result.is_aligned and result.diarization_segments:
            # 推送到对齐队列
            AlignmentQueueService.enqueue_alignment_task(
                {
                    "file_id": file_id,
                    "transcription_task_id": transcription_task.id,
                    "diarization_task_id": diarization_task.id,
                }
            )
        else:
            if not result:
                logger.error("Failed to find task result for file %s", file_id)
            elif not result.diarization_segments:
                logger.error("Failed to find diarization segments for file %s", file_id)
            elif result.is_aligned:
                logger.info("File %s has already been aligned", file_id)


# ==================== 辅助函数 ====================

def extract_transcription_text_from_segments(file_id):
    """Extract transcription text from segments

    Args:
        file_id: File ID

    Returns:
        str: Concatenated transcription text from segments

    Raises:
        ValueError: If no transcription result found or segments are invalid
    """
    # Get transcription result and extract text from segments
    task_result = TaskResult.get_by_file_id(file_id)
    if not task_result or not task_result.segments:
        raise ValueError("No transcription result found for this file")

    # Extract and concatenate all text from segments
    segments = task_result.segments
    if not isinstance(segments, list):
        logger.warning(
            f"Unexpected segments format for file {file_id}: {type(segments)}"
        )
        raise ValueError("Invalid transcription segments format")

    # Extract text from each segment, handling potential data issues
    text_parts = []
    for i, segment in enumerate(segments):
        if not isinstance(segment, dict):
            logger.warning(f"Segment {i} is not a dict for file {file_id}: {type(segment)}")
            continue

        text = segment.get("text")
        if text and isinstance(text, str):
            text_parts.append(text)

    transcription_text = " ".join(text_parts)

    if not transcription_text.strip():
        raise ValueError("Transcription text is empty")

    return transcription_text


# ==================== 任务重试相关函数 ====================

@db_transaction_with_callbacks
def retry_task(user_id, task_id, force=False):
    """Retry specified task with retry count validation

    Args:
        user_id: User ID
        task_id: Task ID
        force: Force retry even if task status doesn't normally allow retry

    Returns:
        bool: Whether retry was successful

    Raises:
        TaskNotFoundError: Task does not exist
        TaskFileNotFoundError: Task associated file does not exist
        TaskPermissionDeniedError: Insufficient permissions
        TaskStatusNotRetryableError: Task status does not allow retry (when force=False)
        TaskRetryLimitExceededError: Maximum retry limit exceeded
    """
    # Get task information
    task = Task.get_by_id(task_id)
    if not task:
        raise TaskNotFoundError(f"Task does not exist: {task_id}")

    # Verify that the task's associated file belongs to the current user
    transcription_file = TranscriptionFile.get_by_id(task.file_id)
    if not transcription_file:
        raise TaskFileNotFoundError(f"Task associated file does not exist: file_id={task.file_id}")

    if transcription_file.user_id != user_id:
        raise TaskPermissionDeniedError(f"No permission to retry this task: task_id={task_id}")

    # Check if task status allows retry (unless force is True)
    if not force and task.status not in [TaskStatus.failed.id, TaskStatus.completed.id]:
        raise TaskStatusNotRetryableError(f"Task status does not allow retry, current status: {task.status}")

    # Check retry count limit
    if task.retry_count >= MAX_RETRY_COUNT:
        raise TaskRetryLimitExceededError(
            f"Maximum retry limit ({MAX_RETRY_COUNT}) exceeded. "
            f"Please contact our support team at {SUPPORT_EMAIL} for assistance."
        )

    # If not queue mode, convert to queue mode for retry
    if task.consumption_mode != "redis_queue":
        logger.info(f"Converting task {task_id} from {task.consumption_mode} to redis_queue mode for retry")
        task.consumption_mode = "redis_queue"

    # Update task status and increment retry count
    task.status = TaskStatus.pending.id
    task.error_message = None
    task.started_time = None
    task.completed_time = None
    task.retry_count += 1  # Increment retry count

    # Prepare retry logic based on task type and add to transaction callback
    success = _prepare_retry_task_callback(task)

    if success:
        logger.info(f"Task retry prepared successfully: task_id={task_id}, user_id={user_id}, retry_count={task.retry_count}")
        return True
    else:
        logger.error(f"Task retry preparation failed: task_id={task_id}, user_id={user_id}")
        return False


def _prepare_retry_task_callback(task):
    """Prepare retry logic based on task type and add to transaction callback"""
    if task.task_type == TaskType.transcription.id:
        return _prepare_retry_transcription_callback(task)
    elif task.task_type in [TaskType.summary.id, TaskType.outline.id, TaskType.qa_extraction.id]:
        return _prepare_retry_text_callback(task)
    elif task.task_type == TaskType.speaker_diarization.id:
        return _prepare_retry_speaker_diarization_callback(task)
    elif task.task_type == TaskType.media_preprocessing.id:
        return _prepare_retry_media_preprocessing_callback(task)
    elif task.task_type == TaskType.translation.id:
        return _prepare_retry_translation_callback(task)
    else:
        logger.error(f"Unsupported task type: {task.task_type}")
        return False


def _prepare_retry_transcription_callback(task):
    """Prepare transcription task retry callback"""
    # Get transcription file information
    transcription_file = TranscriptionFile.get_by_id(task.file_id)
    if not transcription_file:
        logger.error(f"Transcription file does not exist: {task.file_id}")
        return False

    # Check if file has been uploaded
    if not transcription_file.uploaded_time:
        logger.error(f"File upload not completed: {task.file_id}")
        return False

    try:
        # Generate presigned URL
        storage = current_app.storage
        file_url = storage.generate_presigned_url_for_read(
            transcription_file.file_key,
            3600  # 1 hour expiration
        )

        # Build task data
        task_data = {
            "id": task.id,
            "task_type": TaskType.transcription.id,
            "file_id": task.file_id,
            "user_id": transcription_file.user_id,
            "file_url": file_url,
            "file_duration": transcription_file.duration,
            "language_code": transcription_file.language_code,
            "transcription_type": transcription_file.transcription_type,
            "requested_service_provider": task.requested_service_provider,
            "priority": task.priority,
            "created_time": datetime.now().isoformat(),
        }

        # Add transaction callback to enqueue task after database commit
        add_transaction_callback(
            lambda: _enqueue_task_with_logging("transcription", task_data)
        )

        logger.info(f"Transcription task retry callback prepared: {task.id}")
        return True

    except Exception as e:
        logger.exception(f"Transcription task retry preparation failed: {task.id}, error: {str(e)}")
        return False


def _prepare_retry_text_callback(task):
    """Prepare text processing task retry callback"""
    # Get transcription file information to obtain user_id
    transcription_file = TranscriptionFile.get_by_id(task.file_id)
    if not transcription_file:
        logger.error(f"Transcription file does not exist: {task.file_id}")
        return False

    try:
        # Extract latest transcription text from segments (not from transcription_text field)
        transcription_text = extract_transcription_text_from_segments(task.file_id)

        # Build task data
        task_data = {
            "id": task.id,
            "task_type": task.task_type,
            "file_id": task.file_id,
            "user_id": transcription_file.user_id,
            "transcription_text": transcription_text,  # Use latest text from segments
            "priority": task.priority,
            "created_time": datetime.now().isoformat(),
        }

        # Add transaction callback to enqueue task after database commit
        add_transaction_callback(
            lambda: _enqueue_task_with_logging("text", task_data)
        )

        logger.info(f"Text processing task retry callback prepared: {task.id}")
        return True

    except ValueError as e:
        logger.error(f"Failed to extract transcription text for retry: {task.id}, error: {str(e)}")
        return False
    except Exception as e:
        logger.exception(f"Text processing task retry preparation failed: {task.id}, error: {str(e)}")
        return False


def _prepare_retry_speaker_diarization_callback(task):
    """Prepare speaker diarization task retry callback"""
    # Get transcription file information
    transcription_file = TranscriptionFile.get_by_id(task.file_id)
    if not transcription_file:
        logger.error(f"Transcription file does not exist: {task.file_id}")
        return False

    # Check if file has been uploaded
    if not transcription_file.uploaded_time:
        logger.error(f"File upload not completed: {task.file_id}")
        return False

    try:
        # Generate presigned URL
        storage = current_app.storage
        file_url = storage.generate_presigned_url_for_read(
            transcription_file.file_key,
            3600  # 1 hour expiration
        )

        # Build task data
        task_data = {
            "id": task.id,
            "task_type": TaskType.speaker_diarization.id,
            "file_id": task.file_id,
            "user_id": transcription_file.user_id,
            "file_url": file_url,
            "file_duration": transcription_file.duration,
            "language_code": transcription_file.language_code,
            "priority": task.priority,
            "created_time": datetime.now().isoformat(),
        }

        # Add transaction callback to enqueue task after database commit
        add_transaction_callback(
            lambda: _enqueue_task_with_logging("speaker_diarization", task_data)
        )

        logger.info(f"Speaker diarization task retry callback prepared: {task.id}")
        return True

    except Exception as e:
        logger.exception(f"Speaker diarization task retry preparation failed: {task.id}, error: {str(e)}")
        return False


def _prepare_retry_media_preprocessing_callback(task):
    """Prepare media preprocessing task retry callback"""
    # Get transcription file information
    transcription_file = TranscriptionFile.get_by_id(task.file_id)
    if not transcription_file:
        logger.error(f"Transcription file does not exist: {task.file_id}")
        return False

    try:
        # Build task data
        task_data = {
            "id": task.id,
            "task_type": TaskType.media_preprocessing.id,
            "file_id": task.file_id,
            "user_id": transcription_file.user_id,
            "priority": task.priority,
            "created_time": datetime.now().isoformat(),
        }

        # Determine preprocessing type based on source type
        if transcription_file.source_type == TranscriptionFileSourceType.YOUTUBE:
            task_data.update({
                'youtube_url': transcription_file.source_url,
                'preprocessing_type': 'youtube'
            })
        elif transcription_file.source_url:
            # If there's a source URL but not YouTube, it's a URL download
            task_data.update({
                'source_url': transcription_file.source_url,
                'preprocessing_type': 'url_download'
            })
        else:
            # Regular file upload that needs ffmpeg preprocessing
            task_data['preprocessing_type'] = 'ffmpeg'

        # Add transaction callback to enqueue task after database commit
        add_transaction_callback(
            lambda: _enqueue_task_with_logging("media_preprocessing", task_data)
        )

        logger.info(f"Media preprocessing task retry callback prepared: {task.id}")
        return True

    except Exception as e:
        logger.exception(f"Media preprocessing task retry preparation failed: {task.id}, error: {str(e)}")
        return False


def _prepare_retry_translation_callback(task):
    """Prepare translation task retry callback"""
    # Get transcription file information to obtain user_id
    transcription_file = TranscriptionFile.get_by_id(task.file_id)
    if not transcription_file:
        logger.error(f"Transcription file does not exist: {task.file_id}")
        return False

    try:
        # Extract latest transcription text from segments (not from transcription_text field)
        transcription_text = extract_transcription_text_from_segments(task.file_id)

        # Build task data
        task_data = {
            "id": task.id,
            "task_type": TaskType.translation.id,
            "file_id": task.file_id,
            "user_id": transcription_file.user_id,
            "transcription_text": transcription_text,  # Use latest text from segments
            "priority": task.priority,
            "created_time": datetime.now().isoformat(),
        }

        # Add transaction callback to enqueue task after database commit
        add_transaction_callback(
            lambda: _enqueue_task_with_logging("text", task_data)  # Translation tasks use text queue
        )

        logger.info(f"Translation task retry callback prepared: {task.id}")
        return True

    except ValueError as e:
        logger.error(f"Failed to extract transcription text for retry: {task.id}, error: {str(e)}")
        return False
    except Exception as e:
        logger.exception(f"Translation task retry preparation failed: {task.id}, error: {str(e)}")
        return False
