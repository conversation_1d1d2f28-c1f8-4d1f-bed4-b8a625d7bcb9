from services.entitlement_service import EntitlementService
from models.transcription_file import TranscriptionFile
from models.entitlement import Entitlement


class UserLimits:
    """用户限制信息数据类"""

    def __init__(self, transcription_minutes_remaining, daily_transcription_count_remaining):
        self.transcription_minutes_remaining = transcription_minutes_remaining
        self.daily_transcription_count_remaining = daily_transcription_count_remaining


def get_user_limits(user_id, user):
    """
    获取用户的使用限制信息
    
    Args:
        user_id: 用户ID
        user: 用户对象
        
    Returns:
        UserLimits: 用户限制信息对象
    """
    # 1. 获取当月剩余转录时长
    credits = EntitlementService.get_user_credits(user_id)
    transcription_minutes_remaining = max(0, credits["remaining_credits"])
    
    # 2. 获取当天转录次数限制和剩余次数
    daily_limit = user.daily_transcribe_limit
    today_count = TranscriptionFile.get_user_today_transcribe_file_count(user_id)
    
    # 计算剩余次数，对于付费用户（无限制）返回一个大数值
    if daily_limit == float("inf"):
        daily_transcription_count_remaining = 999999  # 表示无限制
    else:
        daily_transcription_count_remaining = max(0, daily_limit - today_count)

    return UserLimits(
        transcription_minutes_remaining=transcription_minutes_remaining,
        daily_transcription_count_remaining=daily_transcription_count_remaining
    )
