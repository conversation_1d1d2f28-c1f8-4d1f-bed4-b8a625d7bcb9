import logging
from flask_restful import abort
from sqlalchemy.exc import IntegrityError
from models import db
from models.folder import Folder
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from controllers.transcription import format_transcription_file
from libs.id_generator import id_generator

logger = logging.getLogger(__name__)


def create_folder(user_id, name):
    """创建新文件夹"""
    # 验证文件夹名称长度
    if not name or len(name.strip()) == 0:
        abort(400, message="Folder name cannot be empty")

    name = name.strip()
    if len(name) > 40:
        abort(400, message="Folder name cannot exceed 40 characters")

    # 可以创建任何名称的文件夹，包括 "default"

    try:
        folder = Folder(id=id_generator.get_id(), user_id=user_id, name=name)
        db.session.add(folder)
        db.session.commit()

        # 添加转录记录数量
        folder.transcription_count = folder.get_transcription_count()
        return folder
    except IntegrityError:
        db.session.rollback()
        abort(400, message="Folder name already exists")


def delete_folder(user_id, folder_id):
    """删除文件夹及其中的所有转录记录"""
    logger.info("Starting delete_folder: user_id=%s, folder_id=%s", user_id, folder_id)

    folder = Folder.get_by_id(folder_id)
    if not folder:
        logger.error("Folder not found: folder_id=%s", folder_id)
        abort(404, message="Folder not found")

    if folder.user_id != user_id:
        abort(403, message="Unauthorized")

    logger.info(
        "Found folder: id=%s, name='%s', user_id=%s",
        folder.id,
        folder.name,
        folder.user_id,
    )

    # 获取文件夹中的所有转录记录
    files = TranscriptionFile.query.filter(
        TranscriptionFile.user_id == user_id,
        TranscriptionFile.folder_id == folder_id,
        TranscriptionFile.is_deleted == False,
    ).all()

    logger.info("Found %d files in folder %s", len(files), folder_id)

    if len(files) == 0:
        logger.info("No files to delete in this folder")
    else:
        # 打印找到的文件详情
        for i, file in enumerate(files):
            logger.info(
                "File %d: id=%s, filename='%s', fingerprint=%s, is_deleted=%s",
                i + 1,
                file.id,
                file.filename,
                file.fingerprint,
                file.is_deleted,
            )

    # 处理每个文件的存储引用计数并软删除
    deleted_files_count = 0
    for file in files:
        logger.info("Processing file: id=%s, filename='%s'", file.id, file.filename)

        # 减少文件存储引用计数（只有在原文件未被单独删除时才减少）
        if file.fingerprint and not file.original_file_deleted:
            logger.info(
                "Decrementing FileStorage reference for file %s, fingerprint=%s",
                file.id,
                file.fingerprint,
            )
            result = FileStorage.decrement_reference(file.user_id, file.fingerprint)
            if result:
                logger.info(
                    "FileStorage reference decremented successfully: new count=%s, state=%s",
                    result.reference_count,
                    result.state,
                )
            else:
                logger.warning(
                    "FileStorage.decrement_reference returned None for file %s", file.id
                )
        else:
            logger.warning(
                "File %s has no fingerprint, skipping FileStorage update", file.id
            )

        # 软删除转录记录
        logger.info("Marking file %s as deleted", file.id)
        file.is_deleted = True
        deleted_files_count += 1

    logger.info("Marked %d files as deleted", deleted_files_count)

    # 硬删除文件夹（而不是软删除）
    # 这样可以避免已删除文件夹占用名称的问题
    logger.info("Deleting folder %s from database", folder.id)
    db.session.delete(folder)

    try:
        db.session.commit()
        logger.info(
            "Successfully committed transaction: deleted folder %s and marked %d files as deleted",
            folder_id,
            deleted_files_count,
        )
    except Exception as e:
        logger.error("Error committing transaction: %s", str(e))
        db.session.rollback()
        raise

    return {"message": "Folder and all transcriptions deleted successfully"}


def update_folder_name(user_id, folder_id, new_name):
    """修改文件夹名称"""
    folder = Folder.get_by_id(folder_id)
    if not folder:
        abort(404, message="Folder not found")

    if folder.user_id != user_id:
        abort(403, message="Unauthorized")

    # 验证新名称
    if not new_name or len(new_name.strip()) == 0:
        abort(400, message="Folder name cannot be empty")

    new_name = new_name.strip()
    if len(new_name) > 40:
        abort(400, message="Folder name cannot exceed 40 characters")

    # 所有用户创建的文件夹都可以重命名

    try:
        folder.name = new_name
        db.session.commit()

        # 添加转录记录数量
        folder.transcription_count = folder.get_transcription_count()
        return folder
    except IntegrityError:
        db.session.rollback()
        abort(400, message="Folder name already exists")


def get_user_folders(user_id):
    """获取用户的所有文件夹（不包括默认文件夹）"""
    folders = Folder.get_by_user_id(user_id)

    # 为每个文件夹添加转录记录数量
    for folder in folders:
        folder.transcription_count = folder.get_transcription_count()

    return folders


def get_folder_transcriptions(user_id, folder_id, page, page_size, offset):
    """获取文件夹中的转录记录"""
    import logging

    logger = logging.getLogger(__name__)

    logger.info(
        "Getting transcriptions for folder_id=%s, user_id=%s", folder_id, user_id
    )

    folder = Folder.get_by_id(folder_id)
    if not folder:
        logger.error("Folder %s not found", folder_id)
        abort(404, message="Folder not found")

    logger.info("Found folder: %s, owner_id=%s", folder.name, folder.user_id)

    if folder.user_id != user_id:
        abort(403, message="Unauthorized")

    # 构建查询条件 - 获取指定文件夹中的转录记录
    query = TranscriptionFile.query.filter(
        TranscriptionFile.user_id == user_id,
        TranscriptionFile.folder_id == folder_id,
        TranscriptionFile.is_deleted == False,
    )

    # 获取总数
    total = query.count()
    logger.info("Found %d transcriptions in folder %s", total, folder_id)

    # 获取分页数据
    items = (
        query.order_by(TranscriptionFile.id.desc())
        .offset(offset)
        .limit(page_size)
        .all()
    )
    logger.info("Retrieved %d items for current page", len(items))

    # 格式化转录记录
    try:
        formatted_items = []
        for item in items:
            formatted_item = format_transcription_file(item)
            formatted_items.append(formatted_item)
        items = formatted_items
        logger.info("Successfully formatted %d transcription files", len(items))
    except Exception as e:
        logger.error("Error formatting transcription files: %s", str(e), exc_info=True)
        raise

    total_pages = (total + page_size - 1) // page_size

    # 添加转录记录数量
    try:
        folder.transcription_count = folder.get_transcription_count()
        logger.info("Folder transcription count: %d", folder.transcription_count)
    except Exception as e:
        logger.error("Error getting transcription count: %s", str(e), exc_info=True)
        folder.transcription_count = 0

    return {
        "items": items,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
        "folder": folder,
    }


def batch_move_transcriptions_to_folder(user_id, transcription_ids, folder_id):
    """批量将转录记录移动到指定文件夹"""
    # 验证目标文件夹（如果不为None）
    if folder_id is not None:
        folder = Folder.get_by_id(folder_id)
        if not folder:
            abort(404, message="Folder not found")

        if folder.user_id != user_id:
            abort(403, message="Unauthorized")

    # 初始化结果统计
    result = {"total": len(transcription_ids), "moved": 0, "failed": 0, "errors": []}

    # 批量处理转录记录
    for transcription_id in transcription_ids:
        try:
            # 尝试将字符串转换为整数（支持大整数字符串）
            if isinstance(transcription_id, str):
                transcription_id = int(transcription_id)

            # 获取转录记录
            transcription = TranscriptionFile.get_by_id(transcription_id)
            if not transcription:
                result["failed"] += 1
                result["errors"].append(
                    {
                        "transcriptionId": str(transcription_id),
                        "error": "Transcription not found",
                    }
                )
                continue

            # 验证所有权
            if transcription.user_id != user_id:
                result["failed"] += 1
                result["errors"].append(
                    {"transcriptionId": str(transcription_id), "error": "Unauthorized"}
                )
                continue

            # 更新转录记录的文件夹
            transcription.folder_id = folder_id
            result["moved"] += 1

        except ValueError:
            result["failed"] += 1
            result["errors"].append(
                {
                    "transcriptionId": str(transcription_id),
                    "error": "Invalid transcription ID format",
                }
            )
        except Exception as e:
            result["failed"] += 1
            result["errors"].append(
                {"transcriptionId": str(transcription_id), "error": str(e)}
            )

    # 提交所有更改
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        abort(500, message="Failed to commit batch move operation: %s" % str(e))

    return result
