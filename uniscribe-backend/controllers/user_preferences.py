import logging
from flask import g

from models import db_transaction
from models.user_preferences import UserPreferences

logger = logging.getLogger(__name__)


@db_transaction()
def update_user_preferences(preferences_data):
    """更新用户偏好设置

    Args:
        preferences_data (dict): 用户偏好设置数据

    Returns:
        UserPreferences: 更新后的用户偏好设置对象
    """
    user = g.user
    preferences = user.get_preferences()

    # 更新偏好设置
    if "timezone" in preferences_data:
        preferences.timezone = preferences_data["timezone"]
    if "notify_transcription_success" in preferences_data:
        preferences.notify_transcription_success = preferences_data[
            "notify_transcription_success"
        ]
    if "notify_quota_reset" in preferences_data:
        preferences.notify_quota_reset = preferences_data["notify_quota_reset"]
    if "notify_product_updates" in preferences_data:
        preferences.notify_product_updates = preferences_data["notify_product_updates"]

    logger.info("Updated user preferences for user %s", user.id)
    return preferences
