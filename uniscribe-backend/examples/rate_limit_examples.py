"""
通用 Rate Limit 使用示例

展示如何使用新的通用频率限制功能，包括装饰器和直接使用方式。
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import time
import logging
from libs.rate_limit import (
    rate_limit,
    RateLimiter,
    SlidingWindowStrategy,
    FixedWindowStrategy,
    RedisRateLimitBackend,
    MemoryRateLimitBackend,
    RateLimitExceeded,
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# 示例1: 基本装饰器使用 - 滑动窗口
@rate_limit(max_requests=3, window_size=10.0, key_prefix="api_calls")
def api_call_example(user_id: str, data: str):
    """模拟API调用，每10秒最多3次请求"""
    logger.info(f"Processing API call for user {user_id}: {data}")
    return f"Processed: {data}"


# 示例2: 固定窗口策略
@rate_limit(
    max_requests=5, window_size=60.0, strategy="fixed_window", key_prefix="user_actions"
)
def user_action_example(action: str):
    """用户操作，每分钟最多5次"""
    logger.info(f"User action: {action}")
    return f"Action completed: {action}"


# 示例3: 自定义标识符函数
def get_user_identifier(*args, **kwargs):
    """从参数中提取用户ID作为标识符"""
    return kwargs.get("user_id", "anonymous")


@rate_limit(
    max_requests=2,
    window_size=5.0,
    key_prefix="user_specific",
    identifier_func=get_user_identifier,
)
def user_specific_action(action: str, user_id: str):
    """每个用户独立的频率限制"""
    logger.info(f"User {user_id} performed action: {action}")
    return f"User {user_id}: {action}"


# 示例4: 非等待模式（抛出异常）
@rate_limit(max_requests=2, window_size=3.0, key_prefix="strict_limit", wait=False)
def strict_limited_function(data: str):
    """严格限制，超限时抛出异常而不是等待"""
    logger.info(f"Processing: {data}")
    return f"Processed: {data}"


# 示例5: 直接使用 RateLimiter 类
def direct_rate_limiter_example():
    """直接使用 RateLimiter 类的示例"""

    # 使用滑动窗口策略
    sliding_strategy = SlidingWindowStrategy(max_requests=3, window_size=10.0)
    limiter = RateLimiter(sliding_strategy, key_prefix="direct_usage")

    for i in range(5):
        allowed, wait_time = limiter.is_allowed(f"operation_{i}")
        if allowed:
            logger.info(f"Operation {i} allowed")
        else:
            logger.info(f"Operation {i} blocked, need to wait {wait_time:.2f}s")

        time.sleep(1)


# 示例6: 使用内存后端（适合测试）
def memory_backend_example():
    """使用内存后端的示例"""
    memory_backend = MemoryRateLimitBackend()
    strategy = SlidingWindowStrategy(max_requests=2, window_size=5.0)
    limiter = RateLimiter(strategy, backend=memory_backend, key_prefix="memory_test")

    for i in range(4):
        allowed, wait_time = limiter.is_allowed("test")
        logger.info(f"Request {i+1}: allowed={allowed}, wait_time={wait_time}")
        if not allowed and wait_time:
            time.sleep(wait_time)


# 示例7: 不同服务的不同限制策略
class APIService:
    """模拟API服务，不同端点有不同的频率限制"""

    @rate_limit(max_requests=10, window_size=60.0, key_prefix="api_search")
    def search(self, query: str):
        """搜索API - 每分钟10次"""
        return f"Search results for: {query}"

    @rate_limit(max_requests=5, window_size=60.0, key_prefix="api_upload")
    def upload(self, filename: str):
        """上传API - 每分钟5次"""
        return f"Uploaded: {filename}"

    @rate_limit(
        max_requests=100,
        window_size=3600.0,
        strategy="fixed_window",
        key_prefix="api_download",
    )
    def download(self, file_id: str):
        """下载API - 每小时100次，使用固定窗口"""
        return f"Downloaded file: {file_id}"


# 示例8: 错误处理
def error_handling_example():
    """演示错误处理"""
    try:
        # 快速调用多次，触发限制
        for i in range(5):
            strict_limited_function(f"data_{i}")
    except RateLimitExceeded as e:
        logger.error(f"Rate limit exceeded: {e}")


# 示例9: 高级配置 - 基于IP的限制
def get_client_ip(*args, **kwargs):
    """模拟获取客户端IP"""
    return kwargs.get("client_ip", "127.0.0.1")


@rate_limit(
    max_requests=100,
    window_size=3600.0,  # 1小时
    key_prefix="ip_limit",
    identifier_func=get_client_ip,
    strategy="fixed_window",
)
def handle_request(endpoint: str, client_ip: str):
    """基于IP的请求限制"""
    logger.info(f"Handling request to {endpoint} from {client_ip}")
    return f"Response for {endpoint}"


def main():
    """运行所有示例"""
    logger.info("=== Rate Limit Examples ===")

    # 示例1: 基本装饰器
    logger.info("\n1. Basic decorator example:")
    try:
        for i in range(4):
            result = api_call_example("user123", f"data_{i}")
            logger.info(f"Result: {result}")
    except Exception as e:
        logger.error(f"Error: {e}")

    # 示例2: 固定窗口
    logger.info("\n2. Fixed window example:")
    try:
        for i in range(3):
            result = user_action_example(f"action_{i}")
            logger.info(f"Result: {result}")
    except Exception as e:
        logger.error(f"Error: {e}")

    # 示例3: 用户特定限制
    logger.info("\n3. User-specific rate limiting:")
    try:
        # 不同用户有独立的限制
        user_specific_action("login", user_id="user1")
        user_specific_action("login", user_id="user2")
        user_specific_action("logout", user_id="user1")
    except Exception as e:
        logger.error(f"Error: {e}")

    # 示例4: 严格限制（抛出异常）
    logger.info("\n4. Strict limiting (throws exception):")
    error_handling_example()

    # 示例5: 直接使用
    logger.info("\n5. Direct RateLimiter usage:")
    direct_rate_limiter_example()

    # 示例6: 内存后端
    logger.info("\n6. Memory backend example:")
    memory_backend_example()

    # 示例7: API服务
    logger.info("\n7. API service example:")
    api_service = APIService()
    try:
        logger.info(api_service.search("python"))
        logger.info(api_service.upload("test.txt"))
        logger.info(api_service.download("file123"))
    except Exception as e:
        logger.error(f"Error: {e}")

    # 示例8: IP限制
    logger.info("\n8. IP-based limiting:")
    try:
        handle_request("/api/data", client_ip="***********")
        handle_request("/api/users", client_ip="***********")
    except Exception as e:
        logger.error(f"Error: {e}")


if __name__ == "__main__":
    main()
