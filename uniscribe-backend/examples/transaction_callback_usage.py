#!/usr/bin/env python3
"""
事务后回调的实际使用示例
"""

from models import db_transaction_with_callbacks, add_transaction_callback
from services.task_queue_service import enqueue_task
import logging

logger = logging.getLogger(__name__)


def _enqueue_task_with_logging(task_type, task_data):
    """带日志的任务入队函数"""
    task_id = task_data.get('id', 'unknown')
    file_id = task_data.get('file_id', 'unknown')

    try:
        enqueue_task(task_type, task_data)
        logger.info(f"{task_type}任务已加入队列: task_id={task_id}, file_id={file_id}")
    except Exception as e:
        logger.error(f"{task_type}任务加入队列失败: task_id={task_id}, error={str(e)}")


# 示例1: 转录任务创建
@db_transaction_with_callbacks
def create_transcription_task_example(user_id, file_id):
    """创建转录任务的示例"""
    
    # 1. 数据库操作
    task_id = create_task_in_database(file_id, 'transcription')
    
    # 2. 准备队列数据
    task_data = {
        'id': task_id,
        'task_type': 'transcription',
        'file_id': file_id,
        'user_id': user_id,
        'priority': 1,
        'created_time': datetime.now().isoformat()
    }
    
    # 3. 添加事务后回调 - 确保任务在数据库提交后才入队
    add_transaction_callback(
        _enqueue_task_with_logging,
        'transcription',
        task_data
    )
    
    return task_id


# 示例2: 批量文本任务创建
@db_transaction_with_callbacks
def create_text_tasks_example(file_id, task_types):
    """创建多个文本任务的示例"""
    
    task_ids = []
    
    for task_type in task_types:
        # 1. 数据库操作
        task_id = create_task_in_database(file_id, task_type)
        task_ids.append(task_id)
        
        # 2. 准备队列数据
        task_data = {
            'id': task_id,
            'task_type': task_type,
            'file_id': file_id,
            'priority': 0,
            'created_time': datetime.now().isoformat()
        }
        
        # 3. 为每个任务添加回调
        add_transaction_callback(
            _enqueue_task_with_logging,
            'text',
            task_data
        )
    
    return task_ids


# 示例3: 复杂的业务逻辑
@db_transaction_with_callbacks
def process_file_upload_example(user_id, file_info):
    """处理文件上传的复杂示例"""
    
    # 1. 创建文件记录
    file_id = create_file_record(user_id, file_info)
    
    # 2. 更新用户配额
    update_user_quota(user_id, file_info['duration'])
    
    # 3. 创建转录任务
    transcription_task_id = create_task_in_database(file_id, 'transcription')
    
    # 4. 如果需要说话人识别，创建相应任务
    diarization_task_id = None
    if file_info.get('enable_speaker_diarization'):
        diarization_task_id = create_task_in_database(file_id, 'speaker_diarization')
    
    # 5. 添加事务后回调
    
    # 转录任务入队
    transcription_data = prepare_transcription_task_data(transcription_task_id, file_id)
    add_transaction_callback(
        _enqueue_task_with_logging,
        'transcription',
        transcription_data
    )

    # 说话人识别任务入队（如果需要）
    if diarization_task_id:
        diarization_data = prepare_diarization_task_data(diarization_task_id, file_id)
        add_transaction_callback(
            _enqueue_task_with_logging,
            'speaker_diarization',
            diarization_data
        )
    
    # 发送通知邮件
    add_transaction_callback(
        send_upload_notification_email,
        user_id,
        file_id
    )
    
    return {
        'file_id': file_id,
        'transcription_task_id': transcription_task_id,
        'diarization_task_id': diarization_task_id
    }


# 示例4: 错误处理
@db_transaction_with_callbacks
def create_task_with_validation_example(user_id, file_id):
    """带验证的任务创建示例"""
    
    # 1. 验证用户权限
    if not validate_user_permission(user_id, file_id):
        raise PermissionError("用户无权限操作此文件")
    
    # 2. 验证配额
    if not check_user_quota(user_id):
        raise QuotaExceededError("用户配额不足")
    
    # 3. 创建任务
    task_id = create_task_in_database(file_id, 'transcription')
    
    # 4. 添加回调
    task_data = prepare_task_data(task_id, file_id)
    add_transaction_callback(
        _enqueue_task_with_logging,
        'transcription',
        task_data
    )
    
    # 如果任何步骤失败，事务会回滚，回调不会执行
    
    return task_id


# 辅助函数（示例）
def create_task_in_database(file_id, task_type):
    """在数据库中创建任务"""
    # 实际的数据库操作
    pass

def prepare_transcription_task_data(task_id, file_id):
    """准备转录任务数据"""
    return {
        'id': task_id,
        'task_type': 'transcription',
        'file_id': file_id,
        # ... 其他数据
    }

def send_upload_notification_email(user_id, file_id):
    """发送上传通知邮件"""
    logger.info(f"发送通知邮件: user_id={user_id}, file_id={file_id}")


# 使用示例
if __name__ == "__main__":
    # 这些函数在实际使用时会在 Flask 应用上下文中调用
    print("事务后回调使用示例")
    print("请在实际的 Flask 应用中使用这些函数")
