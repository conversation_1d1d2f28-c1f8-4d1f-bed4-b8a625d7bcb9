#!/usr/bin/env python
"""
超时工具函数使用示例

这个脚本展示了如何使用 libs/timeout.py 中的超时工具函数。
"""

import time
import logging
from libs.timeout import (
    with_thread_timeout,
    with_timeout_handler,
    with_graceful_degradation,
    TimeoutError,
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def slow_function(seconds):
    """一个耗时的函数，用于测试超时机制"""
    logger.info(f"开始执行耗时操作，预计耗时 {seconds} 秒")
    time.sleep(seconds)
    logger.info("耗时操作完成")
    return f"操作完成，耗时 {seconds} 秒"


def example_with_thread_timeout():
    """基本超时控制示例"""
    logger.info("\n=== 基本超时控制示例 ===")
    
    # 正常完成的情况
    logger.info("测试正常完成的情况（2秒操作，3秒超时）:")
    result, error = with_thread_timeout(3, slow_function, 2)
    if error:
        logger.error(f"操作失败: {error}")
    else:
        logger.info(f"操作成功: {result}")
    
    # 超时的情况
    logger.info("\n测试超时的情况（4秒操作，2秒超时）:")
    result, error = with_thread_timeout(2, slow_function, 4)
    if error:
        logger.error(f"操作失败: {error}")
    else:
        logger.info(f"操作成功: {result}")


def example_with_timeout_handler():
    """带有错误处理的超时控制示例"""
    logger.info("\n=== 带有错误处理的超时控制示例 ===")
    
    def handle_timeout(seconds):
        logger.warning(f"操作超时（{seconds}秒），返回默认结果")
        return "默认结果（超时）"
    
    def handle_error(error):
        logger.error(f"操作出错: {error}")
        return "默认结果（错误）"
    
    # 正常完成的情况
    logger.info("测试正常完成的情况（1秒操作，3秒超时）:")
    result = with_timeout_handler(
        3, 
        slow_function, 
        1,
        on_timeout=handle_timeout,
        on_error=handle_error
    )
    logger.info(f"结果: {result}")
    
    # 超时的情况
    logger.info("\n测试超时的情况（4秒操作，2秒超时）:")
    result = with_timeout_handler(
        2, 
        slow_function, 
        4,
        on_timeout=handle_timeout,
        on_error=handle_error
    )
    logger.info(f"结果: {result}")


def example_with_graceful_degradation():
    """带有优雅降级的超时控制示例"""
    logger.info("\n=== 带有优雅降级的超时控制示例 ===")
    
    # 正常完成的情况
    logger.info("测试正常完成的情况（1秒操作，3秒超时）:")
    result = with_graceful_degradation(
        3,
        slow_function,
        1,
        fallback_value="降级结果",
        log_prefix="[测试]"
    )
    logger.info(f"结果: {result}")
    
    # 超时的情况
    logger.info("\n测试超时的情况（4秒操作，2秒超时）:")
    result = with_graceful_degradation(
        2,
        slow_function,
        4,
        fallback_value="降级结果",
        log_prefix="[测试]"
    )
    logger.info(f"结果: {result}")


def example_error_handling():
    """错误处理示例"""
    logger.info("\n=== 错误处理示例 ===")
    
    def function_with_error():
        logger.info("执行一个会抛出异常的函数")
        raise ValueError("这是一个测试异常")
    
    # 使用基本超时控制
    logger.info("使用基本超时控制处理异常:")
    result, error = with_thread_timeout(3, function_with_error)
    if error:
        logger.error(f"操作失败: {error}")
    else:
        logger.info(f"操作成功: {result}")
    
    # 使用优雅降级
    logger.info("\n使用优雅降级处理异常:")
    result = with_graceful_degradation(
        3,
        function_with_error,
        fallback_value="降级结果（异常）",
        log_prefix="[测试异常]"
    )
    logger.info(f"结果: {result}")


def main():
    """主函数"""
    logger.info("开始超时工具函数示例")
    
    example_with_thread_timeout()
    example_with_timeout_handler()
    example_with_graceful_degradation()
    example_error_handling()
    
    logger.info("\n示例完成")


if __name__ == "__main__":
    main()
