"""
免费用户过期原始文件清理定时任务

该脚本用于定期清理免费用户超过指定天数的原始文件，但保留转录记录。
建议每天运行一次。

使用方法：
python -m crontab.free_user_file_cleanup
python -m crontab.free_user_file_cleanup --days 30 --dry-run
"""

import logging
import click
from datetime import datetime, timedelta
from app import app
from models.user import User
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from models.purchase import Purchase
from models.subscription import Subscription
from models.appsumo_license import AppSumoLicense
from models import db, db_transaction
from constants.transcription import OriginalFileDeleteReason

logger = logging.getLogger(__name__)


def has_recent_payment(user_id, days=30):
    """
    检查用户在指定天数内是否有付费记录
    
    Args:
        user_id: 用户ID
        days: 检查的天数，默认30天
        
    Returns:
        bool: 如果有付费记录返回True，否则返回False
    """
    cutoff_date = datetime.now() - timedelta(days=days)
    
    # 检查购买记录
    recent_purchase = Purchase.query.filter(
        Purchase.user_id == user_id,
        Purchase.created_time >= cutoff_date
    ).first()
    
    if recent_purchase:
        logger.info(f"User {user_id} has recent purchase: {recent_purchase.id}")
        return True
    
    # 检查订阅记录
    recent_subscription = Subscription.query.filter(
        Subscription.user_id == user_id,
        Subscription.created_time >= cutoff_date
    ).first()
    
    if recent_subscription:
        logger.info(f"User {user_id} has recent subscription: {recent_subscription.id}")
        return True
    
    # 检查AppSumo激活记录
    recent_appsumo = AppSumoLicense.query.filter(
        AppSumoLicense.user_id == user_id,
        AppSumoLicense.activation_time >= cutoff_date
    ).first()
    
    if recent_appsumo:
        logger.info(f"User {user_id} has recent AppSumo activation: {recent_appsumo.id}")
        return True
    
    return False


def cleanup_free_user_expired_files(days=30, dry_run=False):
    """
    清理免费用户过期的原始文件
    
    Args:
        days: 文件过期天数，默认30天
        dry_run: 是否为试运行模式
    """
    logger.info(f"Starting cleanup of free user expired files (older than {days} days)")
    
    cutoff_date = datetime.now() - timedelta(days=days)
    batch_size = 100
    total_processed = 0
    total_cleaned = 0
    total_skipped = 0
    
    # 查询需要处理的文件
    # 条件：
    # 1. 创建时间超过指定天数
    # 2. 原文件未被删除
    # 3. 不是软删除的记录
    query = TranscriptionFile.query.filter(
        TranscriptionFile.created_time < cutoff_date,
        TranscriptionFile.original_file_deleted == False,
        TranscriptionFile.is_deleted == False,
        TranscriptionFile.fingerprint.isnot(None),
        TranscriptionFile.fingerprint != ""
    ).order_by(TranscriptionFile.id)
    
    total_files = query.count()
    logger.info(f"Found {total_files} files to check")


    # 使用游标分页避免offset问题
    last_id = 0
    batch_num = 1

    while True:
        # 使用ID游标而不是offset，避免数据变更导致的跳过问题
        batch_query = query.filter(TranscriptionFile.id > last_id).limit(batch_size)
        batch = batch_query.all()

        if not batch:
            break

        logger.info(f"Processing batch {batch_num}, files {batch[0].id}-{batch[-1].id} ({len(batch)} files)")

        # 按用户分组处理
        user_files = {}
        for file in batch:
            if file.user_id not in user_files:
                user_files[file.user_id] = []
            user_files[file.user_id].append(file)
        
        for user_id, files in user_files.items():
            try:
                # 获取用户信息
                user = User.query.get(user_id)
                if not user:
                    logger.warning(f"User {user_id} not found, skipping files")
                    total_skipped += len(files)
                    continue
                
                # 检查是否为付费用户
                if user.has_paid_plan:
                    logger.debug(f"User {user_id} has paid plan, skipping {len(files)} files")
                    total_skipped += len(files)
                    continue
                
                # 检查是否有近期付费记录
                if has_recent_payment(user_id, days):
                    logger.info(f"User {user_id} has recent payment, skipping {len(files)} files")
                    total_skipped += len(files)
                    continue
                
                # 处理该用户的文件
                logger.info(f"Processing {len(files)} files for free user {user_id}")
                
                for file in files:
                    if not dry_run:
                        with db_transaction():
                            # 减少文件存储引用计数
                            if file.fingerprint:
                                FileStorage.decrement_reference(file.user_id, file.fingerprint)
                            
                            # 标记原文件已删除
                            file.original_file_deleted = True
                            file.original_file_delete_reason = OriginalFileDeleteReason.AUTO_CLEANUP.value
                            file.original_file_deleted_at = datetime.now()
                            
                            # 清空文件相关字段
                            file.file_url = ""
                            file.file_key = ""
                            
                            logger.info(f"Cleaned expired file: {file.id} ({file.filename})")
                    else:
                        logger.info(f"[DRY RUN] Would clean file: {file.id} ({file.filename})")
                    
                    total_cleaned += 1
                    
            except Exception as e:
                logger.error(f"Error processing files for user {user_id}: {e}")
                total_skipped += len(files)
        
        total_processed += len(batch)
        last_id = batch[-1].id  # 更新游标为当前批次最后一个ID
        batch_num += 1

        logger.info(f"Batch completed. Processed: {total_processed}, Cleaned: {total_cleaned}, Skipped: {total_skipped}, Last ID: {last_id}")
    
    logger.info(f"Free user file cleanup completed:")
    logger.info(f"  Total files processed: {total_processed}")
    logger.info(f"  Files cleaned: {total_cleaned}")
    logger.info(f"  Files skipped: {total_skipped}")
    logger.info(f"  Mode: {'DRY RUN' if dry_run else 'LIVE'}")


@click.command()
@click.option('--days', default=30, help='Files older than this many days will be cleaned (default: 30)')
@click.option('--dry-run', is_flag=True, help='Run in dry-run mode without making changes')
def main(days, dry_run):
    """免费用户过期原始文件清理任务"""
    logging.basicConfig(
        level=logging.INFO, 
        format="%(asctime)s - %(levelname)s - %(message)s"
    )
    
    with app.app_context():
        try:
            logger.info("Free user file cleanup cron job started")
            logger.info(f"Parameters: days={days}, dry_run={dry_run}")
            
            cleanup_free_user_expired_files(days=days, dry_run=dry_run)
            
            logger.info("Free user file cleanup cron job completed")
        except Exception as e:
            logger.exception(f"Free user file cleanup cron job failed with error: {str(e)}")
        finally:
            # 清理数据库会话
            db.session.remove()


if __name__ == "__main__":
    main()
