"""
AppSumo 邮件发送定时任务

该脚本用于定期向 AppSumo 用户发送后续邮件：
1. 首次激活后 2 天发送 follow-up reminder 邮件
2. 第二封邮件发送后 7 天发送 last touch 邮件

使用方法：
python -m crontab.appsumo_email_sender

0 14 * * * cd /path/to/uniscribe-backend && python -m crontab.appsumo_email_sender
"""

import logging
from datetime import datetime, timedelta
from app import app
from models import db
from models.user import User
from models.appsumo_license import AppSumoLicense
from libs.cache import RedisCache
from libs.send_email import (
    send_appsumo_follow_up_reminder_email,
    send_appsumo_last_touch_email,
)

logger = logging.getLogger(__name__)

# 定义邮件发送间隔
FOLLOW_UP_DAYS = 2  # 首次激活后 2 天发送第二封邮件
LAST_TOUCH_DAYS = 7  # 第二封邮件后 7 天发送第三封邮件

# 缓存前缀
CACHE_PREFIX = "appsumo_email"


def send_follow_up_emails():
    """发送 follow-up reminder 邮件"""
    logger.info("Starting to send AppSumo follow-up reminder emails")

    # 获取缓存实例
    cache = RedisCache(prefix=CACHE_PREFIX)

    # 查询所有已激活的 AppSumo 用户
    active_licenses = AppSumoLicense.query.filter(
        AppSumoLicense.status == "active",
        AppSumoLicense.user_id.isnot(None),
        AppSumoLicense.activation_time.isnot(None),
    ).all()

    logger.info(f"Found {len(active_licenses)} active AppSumo licenses")

    # 当前时间
    now = datetime.now()

    # 发送计数
    sent_count = 0
    skipped_count = 0

    for license in active_licenses:
        user_id = license.user_id
        activation_time = license.activation_time

        # 检查是否已发送过 onboarding 邮件
        onboarding_key = f"onboarding:{user_id}"
        if not cache.exists(onboarding_key):
            logger.info(f"Skipping user {user_id}: onboarding email not sent yet")
            skipped_count += 1
            continue

        # 检查是否已发送过 follow-up 邮件
        follow_up_key = f"follow_up:{user_id}"
        if cache.exists(follow_up_key):
            logger.info(f"Skipping user {user_id}: follow-up email already sent")
            skipped_count += 1
            continue

        # 检查激活时间是否已经过了 FOLLOW_UP_DAYS 天
        if now < activation_time + timedelta(days=FOLLOW_UP_DAYS):
            logger.info(
                f"Skipping user {user_id}: not yet time for follow-up email "
                f"(activated on {activation_time.isoformat()})"
            )
            skipped_count += 1
            continue

        # 获取用户信息
        user = User.get_by_id(user_id)
        if not user:
            logger.warning(f"User {user_id} not found, skipping follow-up email")
            skipped_count += 1
            continue

        # 发送 follow-up 邮件
        try:
            # 如果用户没有设置 first_name，使用 "Sumo-ling" 作为 fallback
            first_name = user.first_name.strip() if user.first_name else "Sumo-ling"

            logger.info(f"Sending follow-up email to user {user_id} ({user.email})")
            send_appsumo_follow_up_reminder_email(user.email, first_name)

            # 记录发送状态
            cache.set(
                follow_up_key,
                {"sent": True, "sent_time": now.isoformat()},
                ttl=365 * 24 * 3600,  # 1年过期
            )

            sent_count += 1
            logger.info(f"Successfully sent follow-up email to user {user_id}")
        except Exception as e:
            logger.error(f"Failed to send follow-up email to user {user_id}: {str(e)}")

    logger.info(
        f"Completed sending follow-up emails: sent={sent_count}, skipped={skipped_count}"
    )


def send_last_touch_emails():
    """发送 last touch 邮件"""
    logger.info("Starting to send AppSumo last touch emails")

    # 获取缓存实例
    cache = RedisCache(prefix=CACHE_PREFIX)

    # 查询所有已激活的 AppSumo 用户
    active_licenses = AppSumoLicense.query.filter(
        AppSumoLicense.status == "active",
        AppSumoLicense.user_id.isnot(None),
        AppSumoLicense.activation_time.isnot(None),
    ).all()

    logger.info(f"Found {len(active_licenses)} active AppSumo licenses")

    # 当前时间
    now = datetime.now()

    # 发送计数
    sent_count = 0
    skipped_count = 0

    for license in active_licenses:
        user_id = license.user_id

        # 检查是否已发送过 follow-up 邮件
        follow_up_key = f"follow_up:{user_id}"
        if not cache.exists(follow_up_key):
            logger.info(f"Skipping user {user_id}: follow-up email not sent yet")
            skipped_count += 1
            continue

        # 检查是否已发送过 last touch 邮件
        last_touch_key = f"last_touch:{user_id}"
        if cache.exists(last_touch_key):
            logger.info(f"Skipping user {user_id}: last touch email already sent")
            skipped_count += 1
            continue

        # 获取 follow-up 邮件发送时间
        follow_up_data = cache.get(follow_up_key)
        if not follow_up_data or "sent_time" not in follow_up_data:
            logger.warning(f"Invalid follow-up data for user {user_id}, skipping")
            skipped_count += 1
            continue

        # 解析发送时间
        try:
            follow_up_time = datetime.fromisoformat(follow_up_data["sent_time"])
        except (ValueError, TypeError):
            logger.warning(
                f"Invalid follow-up time format for user {user_id}, skipping"
            )
            skipped_count += 1
            continue

        # 检查 follow-up 邮件发送时间是否已经过了 LAST_TOUCH_DAYS 天
        if now < follow_up_time + timedelta(days=LAST_TOUCH_DAYS):
            logger.info(
                f"Skipping user {user_id}: not yet time for last touch email "
                f"(follow-up sent on {follow_up_time.isoformat()})"
            )
            skipped_count += 1
            continue

        # 获取用户信息
        user = User.get_by_id(user_id)
        if not user:
            logger.warning(f"User {user_id} not found, skipping last touch email")
            skipped_count += 1
            continue

        # 发送 last touch 邮件
        try:
            # 如果用户没有设置 first_name，使用 "Sumo-ling" 作为 fallback
            first_name = user.first_name.strip() if user.first_name else "Sumo-ling"

            logger.info(f"Sending last touch email to user {user_id} ({user.email})")
            send_appsumo_last_touch_email(user.email, first_name)

            # 记录发送状态
            cache.set(
                last_touch_key,
                {"sent": True, "sent_time": now.isoformat()},
                ttl=365 * 24 * 3600,  # 1年过期
            )

            sent_count += 1
            logger.info(f"Successfully sent last touch email to user {user_id}")
        except Exception as e:
            logger.error(f"Failed to send last touch email to user {user_id}: {str(e)}")

    logger.info(
        f"Completed sending last touch emails: sent={sent_count}, skipped={skipped_count}"
    )


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    with app.app_context():
        try:
            logger.info("AppSumo email sender cron job started")

            # 发送 follow-up 邮件
            send_follow_up_emails()

            # 发送 last touch 邮件
            send_last_touch_emails()

            logger.info("AppSumo email sender cron job completed")
        except Exception as e:
            logger.exception(
                f"AppSumo email sender cron job failed with error: {str(e)}"
            )
        finally:
            # 在 app context 中清理会话
            db.session.remove()
