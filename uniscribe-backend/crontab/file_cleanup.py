"""
文件清理定时任务

该脚本用于定期清理过期文件和引用计数为0的文件。
建议每天运行一次。

使用方法：
python -m crontab.file_cleanup
"""

import logging
from app import app
from tasks.file_cleanup_task import run_file_cleanup

logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    with app.app_context():
        try:
            logger.info("File cleanup cron job started")
            run_file_cleanup()
            logger.info("File cleanup cron job completed")
        except Exception as e:
            logger.exception(f"File cleanup cron job failed with error: {str(e)}")
        finally:
            # 在 app context 中清理会话
            from models import db
            db.session.remove()
