# */5 * * * * cd /path/to/your/project && /path/to/venv/bin/python reset_expired_usage.py >> /path/to/logfile.log 2>&1
# 5分钟执行一次

import logging

from models import db
from services.entitlement_service import EntitlementService
from app import app

logger = logging.getLogger(__name__)


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    with app.app_context():
        try:
            logger.info("Cron job started")
            # 重置用户权益
            try:
                EntitlementService.reset_periodic_entitlements()
                logger.info(
                    "[Entitlement System] Successfully reset periodic entitlements"
                )
            except Exception as e:
                logger.error(
                    f"[Entitlement System] Failed to reset entitlements: {str(e)}"
                )
                raise

            logger.info("Cron job completed")

        except Exception as e:
            logger.exception("Cron job failed with error")
        finally:
            # 在 app context 中清理会话
            db.session.remove()
