# 媒体预处理消费者部署指南

## 📋 概述

媒体预处理消费者负责处理大文件和复杂格式的音视频文件，使用 ffmpeg 进行音频提取和格式转换。

## 🔧 系统要求

### 必需依赖

1. **ffmpeg & ffprobe**

   - 版本要求：4.0+
   - 用途：音频提取、格式转换、媒体信息获取

2. **Python 环境**

   - 版本要求：3.8+
   - 依赖包：见 `requirements.txt`

3. **Redis**

   - 用途：任务队列
   - 版本要求：5.0+

4. **存储服务**
   - 支持：Cloudflare R2、腾讯云 COS
   - 用途：文件下载和上传

### 系统资源

- **内存**：建议 1GB+（处理大文件时需要更多）
- **磁盘空间**：临时目录至少 5GB 可用空间
- **CPU**：支持多核，ffmpeg 会使用多线程

## 🚀 部署步骤

### 1. 安装系统依赖

#### Ubuntu/Debian

```bash
sudo apt update
sudo apt install ffmpeg
```

#### CentOS/RHEL

```bash
sudo yum install epel-release
sudo yum install ffmpeg
```

#### macOS

```bash
brew install ffmpeg
```

### 2. 验证安装

```bash
ffmpeg -version
ffprobe -version
```

### 3. 环境检查

运行环境检查脚本：

```bash
cd /path/to/uniscribe-backend
./scripts/check_media_processor_requirements.sh
```

确保所有检查项都通过。

### 4. 配置环境变量

在 `.env` 文件中确保包含：

```bash
# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 存储配置
STORAGE_TYPE=s3  # 或 cos
CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_key
CLOUDFLARE_R2_BUCKET_NAME=your_bucket
CLOUDFLARE_R2_ENDPOINT_URL=your_endpoint

# 日志配置
LOG_LEVEL=INFO
```

### 5. 数据库迁移

执行数据库迁移添加必要字段：

```bash
mysql -u username -p database_name < migrations/add_ffmpeg_preprocessing_fields.sql
```

### 6. 启动服务

#### 开发环境

```bash
cd uniscribe-backend
python workers/media_preprocessing_consumer.py
```

#### 生产环境（systemd）

##### 单实例部署

1. 使用部署脚本（推荐）：

```bash
sudo ./scripts/deploy_media_processor.sh
```

或手动部署：

1. 复制服务文件：

```bash
sudo cp deploy/systemd/uniscribe-media-processor.service /etc/systemd/system/
```

2. 修改服务文件中的路径、用户和并发配置：

```bash
sudo nano /etc/systemd/system/uniscribe-media-processor.service
# 调整 --workers 参数来设置并发数
```

3. 启用并启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable uniscribe-media-processor
sudo systemctl start uniscribe-media-processor
```

4. 检查状态：

```bash
sudo systemctl status uniscribe-media-processor
sudo journalctl -u uniscribe-media-processor -f
```

##### 多实例部署（高并发场景）

对于高负载场景，可以部署多个消费者实例：

1. 复制模板服务文件：

```bash
sudo cp deploy/systemd/uniscribe-media-processor@.service /etc/systemd/system/
```

2. 启动多个实例：

```bash
sudo systemctl daemon-reload

# 启动 3 个实例
sudo systemctl enable uniscribe-media-processor@1
sudo systemctl enable uniscribe-media-processor@2
sudo systemctl enable uniscribe-media-processor@3

sudo systemctl start uniscribe-media-processor@1
sudo systemctl start uniscribe-media-processor@2
sudo systemctl start uniscribe-media-processor@3
```

3. 检查所有实例状态：

```bash
sudo systemctl status uniscribe-media-processor@*
sudo journalctl -u uniscribe-media-processor@1 -f
```

4. 管理所有实例：

```bash
# 停止所有实例
sudo systemctl stop uniscribe-media-processor@*

# 重启所有实例
sudo systemctl restart uniscribe-media-processor@*
```

## 📊 监控和日志

### 日志位置

- **开发环境**：控制台输出
- **生产环境**：systemd journal

### 查看日志

```bash
# 实时日志
sudo journalctl -u uniscribe-media-processor -f

# 最近日志
sudo journalctl -u uniscribe-media-processor -n 100

# 错误日志
sudo journalctl -u uniscribe-media-processor -p err
```

### 关键监控指标

1. **服务状态**：

   - 单实例：`systemctl status uniscribe-media-processor`
   - 多实例：`systemctl status uniscribe-media-processor@*`

2. **队列长度**：Redis 中 `tasks:media_preprocessing` 流的长度

3. **处理时间**：日志中的处理时间统计

4. **错误率**：失败任务的比例

5. **磁盘空间**：临时目录的可用空间

6. **并发指标**：

   - 活跃工作线程数：日志中的 "Active workers" 统计
   - 任务队列长度：内存中的任务队列大小
   - 线程池利用率：活跃线程数 / 最大线程数

7. **性能指标**：
   - 任务处理吞吐量：每分钟处理的任务数
   - 平均任务处理时间：从接收到完成的时间
   - 队列等待时间：任务在队列中的等待时间

## 🔍 故障排除

### 常见问题

#### 1. ffmpeg 不可用

```
错误：ffmpeg is not available
解决：安装 ffmpeg 并确保在 PATH 中
```

#### 2. Redis 连接失败

```
错误：Redis connection failed
解决：检查 Redis 服务状态和连接配置
```

#### 3. 存储访问失败

```
错误：Failed to download/upload file
解决：检查存储服务配置和网络连接
```

#### 4. 磁盘空间不足

```
错误：No space left on device
解决：清理临时文件，增加磁盘空间
```

#### 5. 内存不足

```
错误：Memory allocation failed
解决：增加系统内存或优化处理策略
```

### 调试命令

```bash
# 检查环境
./scripts/check_media_processor_requirements.sh

# 测试 ffmpeg
python -c "from libs.ffmpeg_preprocessing import check_ffmpeg_availability; print(check_ffmpeg_availability())"

# 测试 Redis 连接
python -c "import redis; r=redis.Redis(); print(r.ping())"

# 查看队列状态
redis-cli XINFO STREAM tasks:media_preprocessing
```

## 🔄 更新和维护

### 更新流程

1. 停止服务：`sudo systemctl stop uniscribe-media-processor`
2. 更新代码：`git pull`
3. 安装依赖：`pip install -r requirements.txt`
4. 运行检查：`./scripts/check_media_processor_requirements.sh`
5. 启动服务：`sudo systemctl start uniscribe-media-processor`

### 定期维护

- **日志轮转**：配置 logrotate
- **临时文件清理**：定期清理 `/tmp` 目录
- **性能监控**：监控 CPU、内存、磁盘使用情况
- **队列监控**：监控任务队列长度和处理速度

## 📈 性能优化

### 并发处理

媒体预处理消费者现在支持两种并发方式：

#### 1. 单实例多线程并发

```bash
# 启动单个实例，使用 3 个工作线程（默认）
python workers/media_preprocessing_consumer.py

# 自定义工作线程数量
python workers/media_preprocessing_consumer.py --workers 5

# 自定义消费者名称
python workers/media_preprocessing_consumer.py --consumer-name media-processor-2
```

#### 2. 多实例并发

```bash
# 启动多个消费者实例，每个使用不同的消费者名称
python workers/media_preprocessing_consumer.py --consumer-name media-processor-1 --workers 3 &
python workers/media_preprocessing_consumer.py --consumer-name media-processor-2 --workers 3 &
python workers/media_preprocessing_consumer.py --consumer-name media-processor-3 --workers 2 &
```

#### 并发配置建议

- **CPU 密集型任务**（ffmpeg 处理）：工作线程数 = CPU 核心数
- **I/O 密集型任务**（YouTube 下载、文件上传）：工作线程数 = CPU 核心数 × 2-4
- **混合负载**：建议从 3-5 个工作线程开始，根据实际性能调整

### 资源限制

在 systemd 服务中配置资源限制：

```ini
[Service]
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%
```

### ffmpeg 优化

根据硬件配置调整 ffmpeg 参数：

- 多线程：`-threads 4`
- 硬件加速：`-hwaccel auto`（如果支持）

## 🔐 安全考虑

1. **用户权限**：使用专用用户运行服务
2. **文件权限**：限制临时文件访问权限
3. **网络安全**：限制出站网络访问
4. **日志安全**：避免在日志中记录敏感信息
