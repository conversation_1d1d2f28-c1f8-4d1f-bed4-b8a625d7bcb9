# 文件夹搜索功能实现文档

## 概述

本文档描述了为 Uniscribe 转录系统实现的文件夹搜索功能，允许用户在特定文件夹下搜索转录记录。

## 功能特性

### 1. 搜索范围控制
- ✅ **全局搜索**：不指定文件夹参数时，在所有转录记录中搜索
- ✅ **未分类文件搜索**：使用 `folder=unclassified` 参数，只在未分类文件中搜索
- ✅ **特定文件夹搜索**：使用 `folder={folder_id}` 参数，只在指定文件夹中搜索

### 2. 安全性和权限控制
- ✅ **文件夹权限验证**：确保用户只能搜索自己的文件夹
- ✅ **文件夹存在性检查**：验证文件夹是否存在
- ✅ **错误处理**：提供清晰的错误信息

### 3. 向后兼容性
- ✅ **可选参数**：`folder` 参数为可选，不影响现有API调用
- ✅ **默认行为**：不提供 `folder` 参数时保持原有的全局搜索行为

## API 接口

### 搜索转录文件
```
GET /transcriptions/search
```

**请求参数：**
- `keyword` (必需): 搜索关键词
- `page` (可选): 页码，默认为 1
- `pageSize` (可选): 每页大小，默认为 20
- `folderId` (可选): 文件夹过滤器
  - 不提供或 `null`: 搜索所有文件
  - `"unclassified"`: 只搜索未分类文件
  - `"{folder_id}"`: 只搜索指定文件夹中的文件

**使用示例：**

1. **搜索所有文件**
```bash
GET /transcriptions/search?keyword=会议&page=1&pageSize=20
```

2. **搜索未分类文件**
```bash
GET /transcriptions/search?keyword=会议&folderId=unclassified&page=1&pageSize=20
```

3. **搜索特定文件夹**
```bash
GET /transcriptions/search?keyword=会议&folderId=123&page=1&pageSize=20
```

**响应格式：**
```json
{
  "items": [
    {
      "id": "1",
      "filename": "会议记录.mp3",
      "fileType": "audio/mpeg",
      "status": "completed",
      "folderId": "123",
      "folderName": "工作文件",
      ...
    }
  ],
  "total": 15,
  "page": 1,
  "pageSize": 20,
  "totalPages": 1
}
```

## 实现细节

### 1. 代码修改

**文件：** `uniscribe-backend/resources/transcription.py`

**修改内容：**
- 在 `TranscriptionSearchResource.get()` 方法中添加了 `folder` 参数处理
- 根据 `folder` 参数值构建不同的查询条件
- 添加了文件夹权限验证逻辑

**核心逻辑：**
```python
# 构建基础查询条件
query = TranscriptionFile.query.filter(
    TranscriptionFile.user_id == user_id,
    TranscriptionFile.filename.like(f"%{args.keyword}%"),
    TranscriptionFile.is_deleted == False,
)

# 根据文件夹过滤器调整查询条件
if args.folder == 'unclassified':
    # 只搜索未分类的文件（folder_id 为 NULL）
    query = query.filter(TranscriptionFile.folder_id == None)
elif args.folder and args.folder != 'unclassified':
    # 搜索指定文件夹中的文件
    try:
        folder_id = int(args.folder)
        # 验证文件夹是否存在且属于当前用户
        from models.folder import Folder
        folder = Folder.get_by_id(folder_id)
        if not folder or folder.user_id != user_id:
            abort(404, message="Folder not found")
        
        query = query.filter(TranscriptionFile.folder_id == folder_id)
    except ValueError:
        abort(400, message="Invalid folder ID")
```

### 2. 错误处理

**错误类型：**
- `400 Bad Request`: 无效的文件夹ID格式
- `404 Not Found`: 文件夹不存在或无权限访问

**错误响应示例：**
```json
{
  "message": "Folder not found"
}
```

### 3. 性能考虑

- **索引优化**：利用现有的 `user_id`、`folder_id`、`filename` 索引
- **查询优化**：使用单次查询获取结果，避免N+1问题
- **权限检查**：在查询前进行文件夹权限验证，避免无效查询

## 测试

### 1. 单元测试

**文件：** `uniscribe-backend/tests/test_folder_search.py`

**测试覆盖：**
- ✅ 参数验证逻辑
- ✅ 查询构建逻辑
- ✅ 错误处理场景

**运行测试：**
```bash
cd uniscribe-backend
python3 -m pytest tests/test_folder_search.py -v
```

### 2. 手动测试场景

1. **基本搜索功能**
   - 搜索所有文件
   - 搜索未分类文件
   - 搜索特定文件夹

2. **权限验证**
   - 尝试搜索不存在的文件夹
   - 尝试搜索其他用户的文件夹

3. **错误处理**
   - 提供无效的文件夹ID
   - 提供空的搜索关键词

## 部署说明

### 1. 代码部署
- 无需数据库迁移
- 无需额外配置
- 重启应用即可生效

### 2. 兼容性
- ✅ 向后兼容现有API调用
- ✅ 不影响现有前端功能
- ✅ 可逐步迁移到新的搜索方式

## 前端集成建议

### 1. UI 改进建议
- 在搜索框附近添加文件夹选择器
- 显示当前搜索范围（全部/未分类/特定文件夹）
- 在搜索结果中显示文件所属文件夹

### 2. API 调用示例
```javascript
// 搜索所有文件
const searchAllFiles = async (keyword, page = 1) => {
  const response = await fetch(`/transcriptions/search?keyword=${keyword}&page=${page}`);
  return response.json();
};

// 搜索未分类文件
const searchUnclassifiedFiles = async (keyword, page = 1) => {
  const response = await fetch(`/transcriptions/search?keyword=${keyword}&folder=unclassified&page=${page}`);
  return response.json();
};

// 搜索特定文件夹
const searchFolderFiles = async (keyword, folderId, page = 1) => {
  const response = await fetch(`/transcriptions/search?keyword=${keyword}&folder=${folderId}&page=${page}`);
  return response.json();
};
```

## 总结

文件夹搜索功能已成功实现，具备以下优势：

1. **功能完整**：支持全局搜索、未分类搜索和文件夹搜索
2. **安全可靠**：完善的权限验证和错误处理
3. **向后兼容**：不影响现有功能
4. **性能优化**：高效的查询实现
5. **易于扩展**：为未来功能扩展预留空间

用户现在可以在前端实现更精确的搜索体验，提高文件管理效率。
