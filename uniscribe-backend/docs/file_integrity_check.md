# 文件完整性验证降级策略

## 背景

在生产环境中，我们发现 `verify_file_integrity` 函数有时会超时（超过 30 秒），导致 gunicorn worker 重启。这个问题不能稳定复现，可能与 Cloudflare R2 存储服务有关。

为了解决这个问题，我们实现了一个降级策略，在接近 gunicorn 超时限制时自动降级返回成功，避免 worker 重启。

## 实现方案

1. 添加配置选项，允许控制是否启用降级策略和超时时间
2. 使用线程超时机制，在指定时间内如果无法完成验证，则根据配置决定是否降级
3. 详细的日志记录，便于问题排查

## 配置选项

在 `config.py` 中添加了以下配置：

```python
FILE_INTEGRITY_CHECK = {
    "enable_graceful_degradation": True,  # 是否启用降级策略
    "timeout_seconds": 3,  # 超时时间（秒）
}
```

- `enable_graceful_degradation`: 是否启用降级策略，默认为 `True`
- `timeout_seconds`: 超时时间（秒），默认为 3 秒

## 降级策略

当文件完整性验证超时或发生其他错误时：

1. 如果 `enable_graceful_degradation` 为 `True`：
   - 记录警告日志
   - 假设文件存在并且完整，返回 `True`
   - 允许用户继续上传流程

2. 如果 `enable_graceful_degradation` 为 `False`：
   - 记录错误日志
   - 抛出原始异常
   - 用户上传流程将失败

## 日志说明

降级策略会记录详细的日志，包括：

- 文件 ID
- 超时时间
- 是否启用降级
- 错误详情（如果有）

示例日志：

```
[file_id=123456] File integrity check timed out after 25s
[file_id=123456] Graceful degradation enabled, assuming file exists
```

## 风险和注意事项

1. 启用降级策略可能导致一些不存在或损坏的文件被错误地标记为有效
2. 这是一种权衡，我们选择提高服务可用性，而接受少量文件可能存在问题的风险
3. 如果发现大量文件验证超时，应该调查 Cloudflare R2 存储服务的性能问题

## 监控建议

1. 监控包含 "File integrity check timed out" 的日志
2. 如果超时频率增加，考虑调查存储服务性能问题
3. 如果确实需要严格的文件完整性验证，可以在配置中禁用降级策略
