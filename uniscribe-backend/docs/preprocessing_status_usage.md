# 媒体预处理状态使用说明

## 📋 新增状态定义

在 `constants/transcription.py` 中新增了两个文件状态：

```python
class TranscriptionFileStatus(TypedEnum):
    # ... 现有状态 ...
    preprocessing = 8  # 媒体预处理中
    preprocessing_failed = 9  # 媒体预处理失败
```

## 🔄 状态流转图

```mermaid
flowchart TD
    A[uploaded] --> B{需要预处理?}
    B -->|是| C[preprocessing]
    B -->|否| D[processing]
    
    C -->|成功| E[uploaded]
    C -->|失败| F[preprocessing_failed]
    
    E --> D
    F --> G[用户可重试]
    G --> C
    
    D --> H[completed/failed]
    
    style C fill:#ffeb3b
    style F fill:#ffcdd2
    style E fill:#c8e6c9
```

## 📍 状态赋值位置

### 1. `preprocessing = 8` 的赋值

**位置**: `workers/media_preprocessing_consumer.py` 第363行

```python
def _process_ffmpeg_task(self, task_id, fields):
    # ...
    # 更新文件状态为预处理中
    tf.status = TranscriptionFileStatus.preprocessing.id
    db.session.commit()
    # ...
```

**触发时机**: 媒体预处理消费者开始处理 ffmpeg 任务时

### 2. `preprocessing_failed = 9` 的赋值

**位置**: `workers/media_preprocessing_consumer.py` 第415行

```python
def _process_ffmpeg_task(self, task_id, fields):
    try:
        # ... 预处理逻辑 ...
    except Exception as e:
        # 标记文件状态为预处理失败
        tf.status = TranscriptionFileStatus.preprocessing_failed.id
        db.session.commit()
        raise
```

**触发时机**: 媒体预处理过程中发生任何异常时

### 3. 预处理成功后的状态重置

**位置**: `workers/media_preprocessing_consumer.py` 第401行

```python
def _process_ffmpeg_task(self, task_id, fields):
    # ... 预处理成功 ...
    tf.status = TranscriptionFileStatus.uploaded.id  # 预处理完成，可以转录
    db.session.commit()
```

**说明**: 预处理成功后，文件状态重置为 `uploaded`，然后创建转录任务

## 🔍 状态检查位置

### 1. 文件列表查询

**位置**: `controllers/transcription.py` 第292-301行

```python
def list_transcription_files(user_id, after_id, limit):
    status_list = [
        TranscriptionFileStatus.uploading.id,
        TranscriptionFileStatus.uploaded.id,
        TranscriptionFileStatus.preprocessing.id,  # ✅ 已添加
        TranscriptionFileStatus.preprocessing_failed.id,  # ✅ 已添加
        TranscriptionFileStatus.processing.id,
        # ... 其他状态 ...
    ]
```

### 2. 文件状态更新逻辑

**位置**: `controllers/task.py` 第516-525行

```python
def update_file_status(file_id):
    # ...
    # 1. 媒体预处理失败
    if status["media_preprocessing_status"] == TaskStatus.failed.id:
        file.status = TranscriptionFileStatus.preprocessing_failed.id
        return

    # 2. 媒体预处理进行中
    if status["media_preprocessing_status"] == TaskStatus.processing.id:
        file.status = TranscriptionFileStatus.preprocessing.id
        return
    # ...
```

### 3. 任务状态摘要

**位置**: `controllers/task.py` 第444行和第488行

```python
def _get_task_status_summary(tasks, file):
    # 获取媒体预处理任务状态
    media_preprocessing_status = media_preprocessing_task.status if media_preprocessing_task else None
    
    return {
        "media_preprocessing_status": media_preprocessing_status,
        # ... 其他状态 ...
    }
```

## 🎨 前端显示

### 1. CLI 工具显示

**位置**: `cli/file_commands.py` 第144-155行

```python
def _get_status_name(status_id):
    status_map = {
        # ... 其他状态 ...
        TranscriptionFileStatus.preprocessing.id: "媒体预处理中",
        TranscriptionFileStatus.preprocessing_failed.id: "媒体预处理失败",
    }

def _get_status_emoji(status_id):
    emoji_map = {
        # ... 其他状态 ...
        TranscriptionFileStatus.preprocessing.id: "🎬",  # 媒体预处理中
        TranscriptionFileStatus.preprocessing_failed.id: "🚫",  # 媒体预处理失败
    }
```

### 2. 前端状态映射（需要前端更新）

前端需要添加对这两个新状态的支持：

```javascript
const STATUS_MAP = {
  // ... 现有状态 ...
  8: { name: '媒体预处理中', icon: '🎬', color: '#ff9800' },
  9: { name: '媒体预处理失败', icon: '🚫', color: '#f44336' },
}
```

## 🔄 完整的状态流转

### 正常流程

1. **文件上传** → `uploaded` (2)
2. **用户点击转录** → 检查是否需要预处理
3. **需要预处理** → `preprocessing` (8)
4. **预处理完成** → `uploaded` (2) → 自动创建转录任务
5. **开始转录** → `processing` (3)
6. **转录完成** → `completed` (5)

### 异常流程

1. **预处理失败** → `preprocessing_failed` (9)
2. **用户可以重试** → 重新触发预处理 → `preprocessing` (8)

## 📊 监控和统计

### 需要监控的指标

1. **预处理中的文件数量**
   ```sql
   SELECT COUNT(*) FROM transcription_file 
   WHERE status = 8 AND is_deleted = FALSE;
   ```

2. **预处理失败的文件数量**
   ```sql
   SELECT COUNT(*) FROM transcription_file 
   WHERE status = 9 AND is_deleted = FALSE;
   ```

3. **预处理成功率**
   ```sql
   SELECT 
     COUNT(CASE WHEN status != 9 THEN 1 END) * 100.0 / COUNT(*) as success_rate
   FROM transcription_file 
   WHERE status IN (8, 9) OR (status = 2 AND media_processing_time > 0);
   ```

## 🚨 告警规则

建议设置以下告警：

1. **预处理失败率过高**: 失败率 > 10%
2. **预处理队列积压**: 预处理中的文件数量 > 50
3. **预处理时间过长**: 单个文件预处理时间 > 30分钟

## 🔧 故障排除

### 常见问题

1. **文件卡在 `preprocessing` 状态**
   - 检查媒体预处理消费者是否运行
   - 检查 ffmpeg 是否可用
   - 查看消费者日志

2. **大量文件 `preprocessing_failed`**
   - 检查 ffmpeg 安装和配置
   - 检查存储服务连接
   - 查看具体错误信息

3. **预处理完成但没有创建转录任务**
   - 检查 `create_transcription_task_after_preprocessing` 调用
   - 检查用户权限和配额

### 调试命令

```bash
# 查看预处理状态的文件
python -m cli.file_commands list-files --status 8,9

# 检查媒体预处理消费者状态
sudo systemctl status uniscribe-media-processor

# 查看预处理任务队列
redis-cli XINFO STREAM tasks:media_preprocessing
```

## ✅ 总结

这两个新状态完整地支持了媒体预处理功能：

- **`preprocessing = 8`**: 表示文件正在进行媒体预处理
- **`preprocessing_failed = 9`**: 表示媒体预处理失败，用户可以重试

状态的使用已经覆盖了：
- ✅ 状态赋值（消费者中）
- ✅ 状态检查（文件列表、状态更新）
- ✅ 前端显示（CLI 工具）
- ✅ 监控统计（SQL 查询）

前端需要更新以支持这两个新状态的显示。
