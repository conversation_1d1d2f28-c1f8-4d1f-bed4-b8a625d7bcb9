# 文件夹管理功能实现文档

## 概述

本文档描述了为 Uniscribe 转录系统实现的文件夹管理功能，允许用户通过文件夹组织和管理转录记录。

## 功能特性

### 1. 核心功能
- ✅ 新增文件夹（名称不能重复）
- ✅ 删除文件夹（连同其中的转录记录）
- ✅ 修改文件夹名称
- ✅ 查询文件夹下的转录记录
- ✅ 获取所有文件夹列表
- ✅ 移动转录记录到其他文件夹

### 2. 未分类文件管理
- ✅ 新转录记录默认为未分类状态（folder_id = NULL）
- ✅ 用户可以将未分类文件移动到自己创建的文件夹中
- ✅ 前端显示"All Files"包含所有文件，用户创建的文件夹只显示对应文件

### 3. 业务规则
- ✅ 文件夹名称最大40个Unicode字符
- ✅ 不支持嵌套文件夹（只支持一级）
- ✅ 删除文件夹时直接删除其中所有转录记录
- ✅ 文件夹只对创建者可见
- ✅ 可以创建任何名称的文件夹（包括 "default"）
- ✅ 所有用户创建的文件夹都可以删除和重命名

## 数据库设计

### 新增表：folders
```sql
CREATE TABLE folders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    name VARCHAR(40) NOT NULL,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    UNIQUE KEY uix_user_folder_name (user_id, name)
);
```

### 修改表：transcription_file
```sql
ALTER TABLE transcription_file 
ADD COLUMN folder_id BIGINT NULL COMMENT '所属文件夹ID，NULL表示在默认文件夹';
```

## API 接口

### 1. 获取所有文件夹
```
GET /folders
```
**响应示例：**
```json
{
  "folders": [
    {
      "id": "1",
      "name": "工作文件",
      "isDefault": false,
      "transcriptionCount": 5,
      "createdTime": "2024-01-01T00:00:00Z",
      "updatedTime": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. 创建文件夹
```
POST /folders
Content-Type: application/json

{
  "name": "工作文件"
}
```

### 3. 修改文件夹名称
```
PATCH /folders/{folder_id}
Content-Type: application/json

{
  "name": "新名称"
}
```

### 4. 删除文件夹
```
DELETE /folders/{folder_id}
```

### 5. 获取文件夹中的转录记录
```
GET /folders/{folder_id}/transcriptions?page=1&pageSize=20
```

### 6. 移动转录记录到文件夹
```
PATCH /transcriptions/{transcription_id}/folder
Content-Type: application/json

{
  "folderId": 123  // null 表示移动到未分类状态
}
```

### 7. 获取未分类文件
```
GET /transcriptions/page?folder=unclassified&page=1&pageSize=20
```

## 文件结构

### 新增文件
- `models/folder.py` - 文件夹数据模型
- `controllers/folder.py` - 文件夹业务逻辑
- `resources/folder.py` - 文件夹API资源
- `fields/folder.py` - 文件夹字段定义
- `migrations/create_folders.sql` - 数据库迁移脚本

### 修改文件
- `models/transcription_file.py` - 添加 folder_id 字段
- `controllers/transcription.py` - 修改格式化函数添加文件夹信息
- `controllers/user.py` - 用户创建时自动创建默认文件夹
- `fields/transcription.py` - 添加文件夹相关字段
- `routes.py` - 注册文件夹相关路由

## 部署说明

### 1. 数据库迁移
执行迁移脚本：
```bash
mysql -u username -p database_name < migrations/create_folders.sql
```

### 2. 应用重启
重启应用以加载新的模型和路由。

### 3. 验证
运行测试脚本验证功能：
```bash
python test_folder_functionality.py
```

## 兼容性说明

### 现有数据
- 现有转录记录的 `folder_id` 为 NULL，表示未分类状态
- 前端可以通过 `folderName` 字段显示文件夹名称（NULL 表示未分类）
- 不会自动创建任何文件夹，用户根据需要自己创建

### API 兼容性
- 现有转录记录 API 响应中新增 `folderId` 和 `folderName` 字段
- 不影响现有功能的使用

## 注意事项

1. **事务处理**：所有数据库操作都在事务中进行，确保数据一致性
2. **权限控制**：所有操作都需要用户认证，只能操作自己的文件夹
3. **错误处理**：提供详细的错误信息和状态码
4. **性能考虑**：文件夹查询使用了适当的索引
5. **数据完整性**：使用唯一约束防止重复文件夹名称
