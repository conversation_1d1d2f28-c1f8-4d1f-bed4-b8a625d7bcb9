# 队列统计功能更新

## 概述

本次更新为 Redis Streams 队列系统添加了更详细的统计功能，特别是**未读取消息统计**，以便更好地监控队列状态和系统健康。

## 新增功能

### 1. 未读取消息统计

新增了 `get_unread_messages()` 方法，用于统计**已入队但未被消费者读取**的消息数量。

**统计原理**：

- **总消息数 (total)**: 使用 `XLEN` 命令获取 stream 中的所有消息数量
- **待处理数 (pending)**: 使用 `XINFO GROUPS` 获取消费者组中已读取但未确认的消息数量
- **未读取数 (unread)**: 使用 `XRANGE` 从 `last-delivered-id` 之后计算未读取的消息数量

### 2. 消息状态流转

```
入队 -> 未读取 -> 已读取但未确认(pending) -> 已确认(完成)
|      |         |                        |
|      |         |                        +-- 从队列中移除
|      |         +-- 消费者读取但还在处理中
|      +-- 等待消费者读取
+-- 刚刚加入队列
```

## API 更新

### `/queue/stats` 接口增强

原有接口保持兼容，新增以下字段：

```json
{
  "status": "healthy",
  "alerts": [],
  "summary": {
    "total_queues": 8,
    "total_messages": 118,
    "total_pending": 1,
    "total_unread": 117,
    "active_queues": 5,
    "queues_with_pending": 1,
    "queues_with_unread": 5
  },
  "stats": {
    "queue_lengths": { ... },
    "pending_messages": { ... },
    "unread_messages": {          // 新增
      "media_preprocessing": 0,
      "transcription:high": 0,
      "text:high": 0,
      ...
    },
    "total_unread": 0             // 新增
  }
}
```

### 新增健康检查规则

- **待处理消息积压**: pending > 50 个
- **未读取消息积压**: unread > 100 个（新增）
- **队列总长度过大**: total > 1000 个

## 新增方法

### TaskQueueService 类

```python
def get_unread_messages(self, task_type: str, consumer_group: str = 'workers') -> int:
    """获取未读取消息数量（已入队但未被消费者读取）"""

def get_all_queue_stats(self, consumer_group: str = 'workers') -> Dict[str, Dict[str, int]]:
    """获取所有队列的详细统计信息"""
```

## 使用示例

### 1. 获取单个队列统计

```python
from services.task_queue_service import TaskQueueService

queue_service = TaskQueueService()

# 获取转录队列统计
total = queue_service.get_queue_length('transcription:high')
pending = queue_service.get_pending_messages('transcription:high')
unread = queue_service.get_unread_messages('transcription:high')

print(f"总消息: {total}, 待处理: {pending}, 未读取: {unread}")
```

### 2. 获取所有队列详细统计

```python
stats = queue_service.get_all_queue_stats()
for task_type, stat in stats.items():
    print(f"{task_type}: total={stat['total']}, pending={stat['pending']}, unread={stat['unread']}")
```

### 3. 通过 API 获取统计

```bash
curl http://localhost:8000/queue/stats | jq '.stats.unread_messages'
```

## 监控建议

### 关键指标

1. **未读取消息数 (unread)**：表示等待处理的任务数量

   - 持续增长可能表示消费者处理能力不足
   - 建议阈值：< 100

2. **待处理消息数 (pending)**：表示正在处理但未完成的任务

   - 持续增长可能表示任务处理异常或超时
   - 建议阈值：< 50

3. **未读取率**：unread / total \* 100%
   - 高未读取率表示任务积压严重
   - 建议阈值：< 80%

### 告警规则

```python
# 示例告警逻辑
def check_queue_health(stats):
    alerts = []

    for queue_type, stat in stats['detailed_stats'].items():
        if stat['unread'] > 100:
            alerts.append(f"{queue_type} 未读取任务过多: {stat['unread']}")

        if stat['pending'] > 50:
            alerts.append(f"{queue_type} 待处理任务积压: {stat['pending']}")

        if stat['total'] > 0:
            unread_rate = (stat['unread'] / stat['total']) * 100
            if unread_rate > 80:
                alerts.append(f"{queue_type} 未读取率过高: {unread_rate:.1f}%")

    return alerts
```

## 注意事项

1. **准确计算**：使用 `last-delivered-id` 和 `XRANGE` 准确计算未读取消息数量
2. **兼容性**：保持了与现有 API 的向后兼容性
3. **性能**：新增统计不会显著影响系统性能
4. **实时性**：统计数据反映调用时的实时状态
5. **错误处理**：当 Redis 操作失败时，会优雅降级返回合理的默认值

## 测试

运行测试脚本验证功能：

```bash
# 基本功能测试
python test_queue_stats.py

# API 接口测试
python test_api_stats.py
```
