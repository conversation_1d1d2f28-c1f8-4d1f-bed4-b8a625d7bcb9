# 文件存储管理系统

本文档描述了文件存储管理系统的设计和使用方法。

## 设计目标

1. 实现文件引用计数机制，确保只有当所有引用都被删除时才删除实际文件
2. 实现免费用户文件30天自动清理策略
3. 确保用户删除转录记录时，相应的存储文件也能被正确处理
4. 使用基于状态的文件管理，替代原有的基于 `is_deleted` 标志的方式

## 数据库设计

### FileStorage 表

该表用于跟踪存储在 Cloudflare R2 中的实际文件及其引用情况：

```sql
CREATE TABLE `file_storage` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `fingerprint` varchar(32) NOT NULL COMMENT '文件MD5指纹',
  `file_type` varchar(20) NOT NULL,
  `file_key` varchar(255) NOT NULL COMMENT '存储系统中的文件键',
  `file_size` int NOT NULL,
  `reference_count` int NOT NULL DEFAULT 1 COMMENT '引用计数',
  `state` varchar(20) NOT NULL DEFAULT 'active' COMMENT '文件状态：active，pending_deletion，deleted',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_access_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后访问时间，用于判断文件是否过期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_fingerprint` (`user_id`,`fingerprint`),
  KEY `idx_userId` (`user_id`),
  KEY `idx_fingerprint` (`fingerprint`),
  KEY `idx_createdTime` (`created_time`),
  KEY `idx_state` (`state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

## 功能实现

### 文件状态管理

文件状态分为三种：

1. `ACTIVE`：活跃状态，文件可以被正常访问
2. `PENDING_DELETION`：待删除状态，文件引用计数为0，等待清理
3. `DELETED`：已删除状态，文件已从存储中删除

文件状态转换流程：

1. 文件上传时，状态为 `ACTIVE`
2. 当文件引用计数降为0时，状态变为 `PENDING_DELETION`
3. 文件清理任务将状态为 `PENDING_DELETION` 的文件从存储中删除，并将状态更新为 `DELETED`

### 文件引用计数

1. 当用户上传文件时，系统会检查该用户是否已有相同指纹的文件
   - 如果有活跃文件，增加引用计数
   - 如果有已删除文件，将其状态恢复为活跃，并重置引用计数为1
   - 如果没有，创建新的存储记录

2. 当用户删除转录记录时，系统会减少对应文件的引用计数
   - 当引用计数降为0时，将文件状态设置为 `PENDING_DELETION`

### 文件清理机制

系统包含两种文件清理机制：

1. **定期清理任务**：每天运行一次，清理：
   - 免费用户超过30天未访问的文件（通过查询用户表动态判断用户类型）
   - 状态为 `PENDING_DELETION` 的文件

2. **一次性清理脚本**：用于清理历史遗留的已删除转录记录对应的文件

### 重新上传已删除文件

当用户重新上传一个已被标记为删除的文件时，系统会：

1. 查找任何状态的文件存储记录
2. 如果找到已删除的记录，将其状态恢复为 `ACTIVE`，并重置引用计数为1
3. 如果找到活跃的记录，增加引用计数
4. 如果没有找到记录，创建一个新的记录

## 使用方法

### 上线顺序

由于这是首次上线文件存储管理系统，请按照以下顺序进行上线：

#### 1. 准备工作

1. 备份数据库（如果有现有数据）
   ```bash
   mysqldump -u username -p database_name > backup_before_migration.sql
   ```

2. 准备数据库创建脚本
   ```sql
   CREATE TABLE `file_storage` (
   `id` bigint NOT NULL AUTO_INCREMENT,
   `user_id` bigint NOT NULL,
   `fingerprint` varchar(32) NOT NULL COMMENT '文件MD5指纹',
   `file_type` varchar(20) NOT NULL,
   `file_key` varchar(255) NOT NULL COMMENT '存储系统中的文件键',
   `file_size` int NOT NULL,
   `reference_count` int NOT NULL DEFAULT '1' COMMENT '引用计数',
   `state` enum('active','pending_deletion','deleted') NOT NULL DEFAULT 'active' COMMENT '文件状态',
   `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   `last_access_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后访问时间，用于判断文件是否过期',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uk_user_fingerprint` (`user_id`,`fingerprint`),
   KEY `idx_userId` (`user_id`),
   KEY `idx_fingerprint` (`fingerprint`),
   KEY `idx_createdTime` (`created_time`),
   KEY `idx_state` (`state`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
   ```

#### 2. 代码部署

1. 部署新代码，包括：
   - `FileStorage` 模型，使用 `state` 字段管理文件状态
   - 文件清理任务，清理待删除状态的文件
   - 文件查询和创建方法，包括处理已删除文件的逻辑

#### 3. 数据库创建

1. 执行数据库创建脚本，创建 `file_storage` 表
   ```bash
   mysql -u username -p database_name < migrations/create_file_storage.sql
   ```

2. 执行文件存储迁移脚本，将现有的转录文件记录迁移到新的 `FileStorage` 表中
   ```bash
   # 查看将要迁移的数据（不做实际更改）
   flask migrate-file-storage --dryrun

   # 执行实际迁移
   flask migrate-file-storage
   ```

#### 4. 验证与监控

1. 验证文件上传功能是否正常
   - 上传新文件
   - 重复上传相同文件，确认引用计数增加

2. 验证文件访问功能是否正常
   - 访问上传的文件

3. 验证文件删除功能是否正常
   - 删除转录记录，确认引用计数减少
   - 删除所有引用，确认文件状态变为 `PENDING_DELETION`

4. 验证文件清理任务是否正常
   - 手动触发文件清理任务
   - 检查待删除文件是否被正确清理

5. 监控系统日志，确保没有异常错误

### 文件清理

文件清理由定时任务 `file_cleanup_task.py` 处理，可以手动触发：

```bash
# 手动触发文件清理任务
python -m crontab.file_cleanup
```

此任务将清理两类文件：
1. 免费用户超过30天未访问的文件
2. 引用计数为0的文件（状态为 `PENDING_DELETION` 的文件）


### 设置定时任务

在 crontab 中添加以下条目，每天凌晨3点运行文件清理任务：

```
0 3 * * * cd /path/to/uniscribe-backend && python -m crontab.file_cleanup
```

## 注意事项

1. 在执行数据迁移和文件清理操作前，建议先使用 `--dryrun` 选项查看将要进行的更改
2. 数据迁移和文件清理操作可能需要较长时间，建议在系统低峰期执行
3. 确保在执行操作前已备份重要数据
4. 整个迁移过程中，文件清理任务可能会暂时无法正常工作，建议在低峰期进行迁移
5. 确保所有与文件存储相关的代码都已更新，包括API、后台任务等
6. 对于已删除的文件，转录记录仍然可以访问，只是原始媒体文件不再可用

## 回滚计划

如果在上线过程中遇到问题，可以按照以下步骤进行回滚：

1. 回滚代码到之前的版本

2. 删除 `file_storage` 表（如果已创建）
   ```sql
   DROP TABLE IF EXISTS file_storage;
   ```

3. 如果需要，从备份中恢复数据库
