# Azure MySQL SSL 证书更新指南

## 背景

Azure 通知 DigiCertGlobalRootCA.pem 证书将在 2025年7月31日后不再安全，要求更新到新的证书组合。

## 更新内容

### 1. 新增合并证书文件

创建了新的合并证书文件：`cert/azure-mysql-combined-ca.pem`

该文件包含以下三个根证书：
- **DigiCert Global Root CA** (原有证书，保持兼容性)
- **DigiCert Global Root G2** (新证书)
- **Microsoft RSA Root Certificate Authority 2017** (Azure 推荐)

### 2. 更新配置

修改了 `config.py` 中的 `ProductionConfig`：

```python
# 更新前
SQLALCHEMY_ENGINE_OPTIONS = {
    "connect_args": {
        "ssl": {"ca": os.path.join(BASE_DIR, "cert", "DigiCertGlobalRootCA.pem")},
    }
}

# 更新后
SQLALCHEMY_ENGINE_OPTIONS = {
    "connect_args": {
        "ssl": {"ca": os.path.join(BASE_DIR, "cert", "azure-mysql-combined-ca.pem")},
    }
}
```

## 部署步骤

### 1. 验证本地环境

在部署前，先在本地测试新证书：

```bash
cd uniscribe-backend
python test_azure_ssl_update.py
```

预期输出：
```
🔒 测试 Azure MySQL SSL 证书更新...
============================================================
✅ 证书文件存在: /path/to/cert/azure-mysql-combined-ca.pem
✅ 证书文件包含 3 个证书
  - ✅ DigiCert Global Root CA
  - ✅ DigiCert Global Root G2
  - ✅ Microsoft RSA Root Certificate Authority 2017
🔗 测试数据库连接...
✅ SSL 连接成功，加密算法: TLS_AES_256_GCM_SHA384
  - SSL 版本: TLSv1.3
  - 证书验证模式: VERIFY_CA
✅ 数据库查询测试通过
============================================================
🎉 Azure MySQL SSL 证书更新测试通过！
📝 证书更新完成，可以安全使用到 2042 年
```

### 2. 生产环境部署

1. **上传新证书文件**：
   ```bash
   # 将新的合并证书文件上传到生产服务器
   scp cert/azure-mysql-combined-ca.pem user@server:/home/<USER>/uniscribe-mono/uniscribe-backend/cert/
   ```

2. **更新代码**：
   ```bash
   # 在生产服务器上更新代码
   git pull origin main
   ```

3. **重启服务**：
   ```bash
   # 重启 Python 后端服务
   sudo systemctl restart uniscribe-backend
   
   # 检查服务状态
   sudo systemctl status uniscribe-backend
   ```

4. **验证连接**：
   ```bash
   # 在生产服务器上运行测试
   cd /home/<USER>/uniscribe-mono/uniscribe-backend
   python test_azure_ssl_update.py
   ```

### 3. 回滚计划

如果新证书出现问题，可以快速回滚：

1. **恢复配置**：
   ```python
   # 在 config.py 中临时恢复旧配置
   SQLALCHEMY_ENGINE_OPTIONS = {
       "connect_args": {
           "ssl": {"ca": os.path.join(BASE_DIR, "cert", "DigiCertGlobalRootCA.pem")},
       }
   }
   ```

2. **重启服务**：
   ```bash
   sudo systemctl restart uniscribe-backend
   ```

## 证书有效期

- **DigiCert Global Root CA**: 有效期至 2031年11月10日
- **DigiCert Global Root G2**: 有效期至 2038年1月15日  
- **Microsoft RSA Root Certificate Authority 2017**: 有效期至 2042年7月18日

合并证书确保了长期兼容性，可以安全使用到 2042 年。

## 监控和验证

### 日常监控

在应用启动日志中查看 SSL 连接状态：
```
✅ MySQL SSL: TLS_AES_256_GCM_SHA384
✅ Redis SSL 连接正常
```

### 定期验证

建议每月运行一次证书测试：
```bash
python test_azure_ssl_update.py
```

### 告警设置

如果 SSL 连接失败，应用会在日志中记录错误，建议设置相应的监控告警。

## 注意事项

1. **保留旧证书**：按照 Azure 建议，保留了原有的 DigiCertGlobalRootCA.pem 文件，确保向后兼容
2. **证书顺序**：合并证书中的顺序按照 Azure 文档建议排列
3. **权限设置**：确保证书文件有正确的读取权限
4. **备份**：在更新前备份原有配置和证书文件

## 相关链接

- [Azure MySQL 根证书轮换文档](https://learn.microsoft.com/en-us/azure/mysql/flexible-server/concepts-root-certificate-rotation)
- [DigiCert 根证书下载](https://www.digicert.com/kb/digicert-root-certificates.htm)
