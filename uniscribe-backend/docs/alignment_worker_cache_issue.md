# Alignment Worker 缓存问题分析与修复

## 问题描述

在生产环境中发现一个奇怪的bug：

```
No diarization segments found for file 7342861685427933184
```

**现象**：
- 数据库中已经存在 `diarization_segments` 数据
- 使用 `retrigger_alignment.py` 重新触发仍然报同样错误
- 重启 `alignment_worker` 后问题解决

## 问题分析

经过深入分析，发现这实际上是**两个独立但相关的问题**：

### 问题1：事务提交时机错误

**根本原因**：`_check_and_trigger_alignment` 在事务提交前被调用

#### 代码问题
```python
# controllers/task.py (修复前)
@db_transaction()  # ❌ 这个装饰器实际上无效（是 contextmanager，不是装饰器）
def save_task_result(...):
    elif task_type == TaskType.speaker_diarization.id:
        result = TaskResult.get_by_file_id(file.id)
        result.diarization_segments = diarization_segments  # 只在内存中更新
        
        # ❌ 事务还没提交就调用了！
        _check_and_trigger_alignment(file.id)
```

#### 问题流程
1. 说话人识别任务完成，调用 `save_task_result()`
2. 在内存中更新 `result.diarization_segments = data`
3. **立即调用** `_check_and_trigger_alignment(file.id)`
4. 此时事务还未提交，数据库中实际上还没有数据
5. `_check_and_trigger_alignment` 中的查询看不到未提交的更改
6. 但条件 `if result and not result.is_aligned` 仍然为真（只检查 `is_aligned`）
7. 任务被错误地 enqueue 了
8. `alignment_worker` 收到任务时，数据库中确实还没有数据

### 问题2：SQLAlchemy Session 缓存问题

**根本原因**：长期运行的 `alignment_worker` 进程中，SQLAlchemy 缓存了对象实例

#### 缓存机制
```python
# alignment_worker.py 的运行方式
with app.app_context():  # 长期存在的 app context
    while True:  # 无限循环
        task_result = TaskResult.get_by_file_id(file_id)  # 使用同一个 session
        # session 从未被清理，对象被缓存
```

#### 问题流程
1. `alignment_worker` 启动，第一次处理 `file_id=7342861685427933184`
2. 查询 `TaskResult.get_by_file_id(7342861685427933184)` 返回对象A
3. 此时 `diarization_segments = None`，对象A被 SQLAlchemy 缓存
4. 任务失败（没有 diarization_segments）
5. 其他进程完成说话人识别，更新数据库
6. 手动使用 `retrigger_alignment.py` 重新触发任务
7. `alignment_worker` 再次处理同一个 `file_id`
8. **查询返回缓存的对象A**（仍然是 `diarization_segments = None`）
9. 任务再次失败

#### 为什么重启能解决问题
重启 `alignment_worker` 清除了所有 SQLAlchemy 缓存，重新查询时从数据库获取最新数据。

## 环境差异分析

### 为什么 HTTP 请求中不会遇到这个问题

**HTTP 请求的 Session 生命周期**：
```python
# 每个 HTTP 请求
@app.route('/api/something')
def handle_request():
    # Flask 自动创建 request context
    task_result = TaskResult.get_by_file_id(file_id)
    # ... 处理请求
    # Flask 自动调用 teardown_appcontext
    # 自动调用 db.session.remove()
```

**Flask-SQLAlchemy 的自动清理机制**：
```python
@app.teardown_appcontext
def shutdown_session(exception=None):
    db.session.remove()  # 清除当前线程的 session 和所有缓存
```

### 环境对比

| 特性 | HTTP请求 | alignment_worker |
|------|----------|------------------|
| **Session生命周期** | 短暂（几秒） | 长期（几小时/几天） |
| **自动清理** | ✅ 每个请求结束后自动清理 | ❌ 没有自动清理机制 |
| **并发修改** | 🔄 很少，通常在同一事务中 | ⚠️ 经常，其他进程会修改数据 |
| **缓存问题** | 🟢 几乎不会遇到 | 🔴 容易遇到 |

## 修复方案

### 修复1：确保事务提交时机

```python
# controllers/task.py
elif task_type == TaskType.speaker_diarization.id:
    result = TaskResult.get_by_file_id(file.id)
    if result:
        result.diarization_segments = diarization_segments
    else:
        # 创建新记录...
        
    # ✅ 显式提交事务，确保数据已保存到数据库
    db.session.commit()
    
    # ✅ 在事务提交后检查是否需要触发对齐任务
    _check_and_trigger_alignment(file.id)
```

### 修复2：增强业务逻辑检查

```python
# controllers/task.py
def _check_and_trigger_alignment(file_id):
    # 使用 refresh=True 确保获取最新数据，避免在同一事务中的缓存问题
    result = TaskResult.get_by_file_id(file_id, refresh=True)
    
    # ✅ 同时检查 is_aligned 和 diarization_segments
    if result and not result.is_aligned and result.diarization_segments:
        # 推送到对齐队列
        AlignmentQueueService.enqueue_alignment_task({...})
```

### 修复3：SQLAlchemy 缓存处理

#### 方案A：查询级别的修复
```python
# models/task_result.py
@classmethod
def get_by_file_id(cls, file_id, refresh=False):
    if refresh:
        # 使用 populate_existing() 强制从数据库重新加载数据，覆盖缓存
        result = cls.query.filter_by(file_id=file_id).populate_existing().first()
    else:
        result = cls.query.filter_by(file_id=file_id).first()
    return result
```

#### 方案B：Worker级别的修复
```python
# workers/alignment_worker.py
def run(self):
    while self.running:
        if task_data:
            # 处理任务
            success = self.process_alignment_task(task_data)
            
            # ✅ 清理 SQLAlchemy session 缓存，避免长期运行进程的缓存问题
            db.session.remove()
```

#### 方案C：关键查询使用强制刷新
```python
# workers/alignment_worker.py
def process_alignment_task(self, task_data):
    # ✅ 获取任务结果，强制从数据库刷新以避免缓存问题
    task_result = TaskResult.get_by_file_id(file_id, refresh=True)
```

## 验证方法

### 测试脚本
创建了多个测试脚本来验证修复效果：

1. `cli/test_cache_issue.py` - 测试基本缓存行为
2. `cli/test_real_cache_scenario.py` - 模拟真实问题场景
3. `cli/test_worker_session_fix.py` - 验证修复效果

### 部署验证
1. 重启 alignment_worker: `sudo systemctl restart alignment_worker`
2. 测试之前失败的文件: `python cli/retrigger_alignment.py --file-id 7342861685427933184`
3. 监控日志，确认不再出现 "No diarization segments found" 错误

## 经验总结

### 关键教训

1. **长期运行进程需要特别注意 SQLAlchemy 缓存问题**
2. **事务提交时机对数据一致性至关重要**
3. **业务逻辑检查应该尽可能完整和防御性**
4. **装饰器的实际效果需要仔细验证**

### 最佳实践

1. **长期运行的 Worker 应该定期清理 Session**：
   ```python
   # 每次任务处理后
   db.session.remove()
   ```

2. **关键查询使用强制刷新**：
   ```python
   # 对于可能被其他进程修改的数据
   result = Model.get_by_id(id, refresh=True)
   ```

3. **事务边界要明确**：
   ```python
   # 确保相关操作在事务提交后执行
   db.session.commit()
   trigger_dependent_operations()
   ```

4. **业务逻辑要防御性编程**：
   ```python
   # 检查所有必要条件
   if result and not result.is_aligned and result.diarization_segments:
   ```

## 相关文件

- `controllers/task.py` - 主要修复位置
- `workers/alignment_worker.py` - Worker 缓存修复
- `models/task_result.py` - 查询级别修复
- `cli/retrigger_alignment.py` - 手动触发脚本

---

**修复日期**: 2025-06-23  
**影响范围**: 生产环境 alignment_worker  
**修复状态**: ✅ 已完成
