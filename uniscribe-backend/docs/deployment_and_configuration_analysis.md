# 部署与配置架构分析

## 概述

本文档分析当前系统的部署架构、配置管理、容器化方案以及相关的运维配置。

## 部署架构

### 环境差异

#### 开发环境 - Docker 容器化部署

**Docker Compose 架构**

```yaml
# 主要服务组件
services:
  mysql_db: # MySQL 8.0 数据库
  redis: # Redis 缓存和队列
  app: # Flask 主应用
  stripe_consumer: # Stripe 支付事件消费者
  alignment_worker: # 说话人对齐工作进程
```

**服务依赖关系**

```
┌─────────────────┐    ┌─────────────────┐
│   mysql_db      │    │     redis       │
│   (MySQL 8.0)   │    │   (缓存/队列)    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │      app        │
         │ (Flask 主应用)   │
         └─────────────────┘
                     │
         ┌───────────┼───────────┐
         │           │           │
┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│stripe_      │ │alignment_   │ │uniscribe-   │
│consumer     │ │worker       │ │service (Go) │
└─────────────┘ └─────────────┘ └─────────────┘
```

**网络配置**

- **app-network**: 自定义桥接网络，用于服务间通信
- **host network**: alignment_worker 使用主机网络模式
- **端口映射**: MySQL 3306→3307, Redis 6379, Flask App 8000

#### 生产环境 - VM 直接部署

**架构概览**

```
┌─────────────────────────────────────────────────────────┐
│                    Production VM                        │
│                                                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              Systemd Services                       │ │
│  │         /home/<USER>/uniscribe-mono              │ │
│  │                                                     │ │
│  │  ┌─────────────┐  ┌─────────────┐                 │ │
│  │  │ uniscribe-  │  │ uniscribe-  │                 │ │
│  │  │ backend     │  │ service     │                 │ │
│  │  │ (Gunicorn+  │  │ (Go Binary) │                 │ │
│  │  │  Gevent)    │  │             │                 │ │
│  │  │ :8000       │  │             │                 │ │
│  │  └─────────────┘  └─────────────┘                 │ │
│  │                                                     │ │
│  │  ┌─────────────┐  ┌─────────────┐                 │ │
│  │  │ stripe-     │  │ alignment-  │                 │ │
│  │  │ consumer    │  │ worker      │                 │ │
│  │  │ (Python)    │  │ (Python)    │                 │ │
│  │  └─────────────┘  └─────────────┘                 │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                  Azure Cloud Services                  │
│                                                         │
│  ┌─────────────┐              ┌─────────────┐          │
│  │   Azure     │              │   Azure     │          │
│  │ Database    │              │   Cache     │          │
│  │ for MySQL   │              │ for Redis   │          │
│  │             │              │             │          │
│  └─────────────┘              └─────────────┘          │
└─────────────────────────────────────────────────────────┘
```

**进程管理**

- **Systemd**: 所有服务进程由 systemd 管理
- **自动重启**: 进程异常退出时自动重启
- **日志管理**: 通过 journalctl 统一管理日志
- **用户权限**: 使用 `chendahui` 用户运行，避免 root 权限

**常用管理命令**

```bash
# 服务状态查看
sudo systemctl status uniscribe-backend
sudo systemctl status uniscribe-service
sudo systemctl status stripe-consumer
sudo systemctl status alignment-worker

# 服务启动/停止/重启
sudo systemctl start uniscribe-backend
sudo systemctl stop uniscribe-backend
sudo systemctl restart uniscribe-backend

# 查看日志
sudo journalctl -u uniscribe-backend -f
sudo journalctl -u uniscribe-service --since "1 hour ago"

# 启用/禁用开机自启
sudo systemctl enable uniscribe-backend
sudo systemctl disable uniscribe-backend

# 重新加载配置
sudo systemctl daemon-reload
sudo systemctl reload uniscribe-backend
```

## 配置管理

### Backend (Python) 配置

#### 核心配置类 (config.py)

```python
class Config:
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.getenv("SQLALCHEMY_DATABASE_URI")

    # 第三方服务配置
    TENCENT_CLOUD = {...}    # 腾讯云存储
    OPENAI = {...}           # OpenAI API
    CLOUDFLARE_R2 = {...}    # Cloudflare R2 存储
    STRIPE = {...}           # Stripe 支付
    SUPABASE = {...}         # Supabase 认证
    REDIS = {...}            # Redis 配置

    # 业务配置
    FILE_INTEGRITY_CHECK = {...}  # 文件完整性检查
    LTD_STORAGE_LIMITS = {...}    # LTD 套餐存储限制
    APPSUMO_PLAN_MAPPING = {...}  # AppSumo 计划映射
```

#### 环境变量管理

```bash
# 数据库
SQLALCHEMY_DATABASE_URI=mysql+pymysql://user:pass@host:port/db

# 第三方服务密钥
TENCENT_CLOUD_SECRET_ID=xxx
TENCENT_CLOUD_SECRET_KEY=xxx
OPENAI_API_KEY=xxx
CLOUDFLARE_R2_ACCESS_KEY=xxx
CLOUDFLARE_R2_SECRET_KEY=xxx
STRIPE_SECRET_KEY=xxx
SUPABASE_URL=xxx
SUPABASE_ANON_KEY=xxx

# Redis 配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=xxx

# 其他配置
YOUTUBE_PROXY=xxx
AXIOM_TOKEN=xxx
USERCHECK_API_KEY=xxx
RESEND_API_KEY=xxx
```

### Service (Go) 配置

#### 配置结构 (config.go)

```go
type Config struct {
    WebBackendHost    string  // Backend API 地址
    DashscopeApiKey   string  // 阿里云 API Key
    ProxyURLStr       string  // 代理配置
    OpenaiApiKey      string  // OpenAI API Key
    ReplicateApiToken string  // Replicate API Token
    FalApiKey         string  // FAL API Key
    DeepInfraApiKey   string  // DeepInfra API Key
}
```

#### 环境变量加载

```go
// 使用 godotenv 加载 .env 文件
err := godotenv.Load()
if err != nil {
    log.Println("No .env file found, using system environment variables")
}
```

## 容器镜像构建

### Dockerfile 分析

#### 基础镜像和环境

```dockerfile
FROM python:3.12.3
WORKDIR /app

# 代理配置支持
ARG http_proxy
ARG https_proxy
ARG all_proxy

# 时区设置为 UTC
RUN ln -sf /usr/share/zoneinfo/UTC /etc/localtime
```

#### 系统依赖安装

```dockerfile
# 配置国内镜像源加速
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl wget ca-certificates gnupg lsb-release \
    ffmpeg  # 音频处理依赖
```

#### Python 包管理优化

```dockerfile
# 使用清华大学镜像源
ENV PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple/
ENV PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn

# 使用 uv 加速包安装
RUN pip install --no-cache-dir uv
RUN uv pip install --system -r requirements.txt
```

#### 特殊依赖处理

```dockerfile
# WeasyPrint + CJK 字体安装
COPY install_weasyprint.sh /tmp/install_weasyprint.sh
RUN chmod +x /tmp/install_weasyprint.sh && \
    /tmp/install_weasyprint.sh
```

### 构建脚本 (docker-build.sh)

#### 智能代理处理

```bash
# 方法1: 尝试使用代理构建
export https_proxy=http://127.0.0.1:7890
docker build -t flask-boilerplate \
  --network=host \
  --build-arg http_proxy=$http_proxy \
  --build-arg https_proxy=$https_proxy

# 方法2: 代理失败时不使用代理
if [ $? -ne 0 ]; then
    unset https_proxy
    docker build -t flask-boilerplate .
fi
```

## 运行时配置

### 应用启动配置

#### Gunicorn 配置

#### 开发环境 - Docker 配置

```dockerfile
# Dockerfile 中的启动命令
CMD ["gunicorn",
     "--worker-class", "gevent",
     "--workers", "4",
     "--timeout", "30",
     "--bind", "0.0.0.0:8000",
     "app:app"]
```

#### 生产环境 - Systemd 服务配置

**uniscribe-backend.service (实际生产环境配置)**

```ini
[Unit]
Description=UniScribe backend
After=network.target

[Service]
User=chendahui
Group=chendahui
WorkingDirectory=/home/<USER>/uniscribe-mono/uniscribe-backend
EnvironmentFile=/home/<USER>/uniscribe-mono/uniscribe-backend/.env
Environment=PYTHONPATH=/home/<USER>/uniscribe-mono/uniscribe-backend
ExecStart=/home/<USER>/uniscribe-mono/uniscribe-backend/.venv/bin/gunicorn \
    --worker-class gevent \
    --workers 2 \
    --timeout 30 \
    --bind 0.0.0.0:8000 \
    app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

**配置说明**：

- **用户权限**: 使用 `chendahui` 用户运行，避免 root 权限
- **工作目录**: 项目源码目录 `/home/<USER>/uniscribe-mono/uniscribe-backend`
- **虚拟环境**: 使用项目内的 `.venv` 虚拟环境
- **Worker 配置**: 2 个 gevent worker，适合 I/O 密集型任务
- **网络绑定**: `0.0.0.0:8000` 允许外部访问（通过防火墙控制）
- **自动重启**: 进程异常时自动重启

**uniscribe-service.service (Go)**

```ini
[Unit]
Description=UniScribe Go Service
After=network.target uniscribe-backend.service
Requires=uniscribe-backend.service

[Service]
User=chendahui
Group=chendahui
WorkingDirectory=/home/<USER>/uniscribe-mono/uniscribe-service
EnvironmentFile=/home/<USER>/uniscribe-mono/uniscribe-service/.env
ExecStart=/home/<USER>/uniscribe-mono/uniscribe-service/bin/uniscribe-service
Restart=always

[Install]
WantedBy=multi-user.target
```

**stripe-consumer.service**

```ini
[Unit]
Description=UniScribe Stripe Event Consumer
After=network.target uniscribe-backend.service
Requires=uniscribe-backend.service

[Service]
User=chendahui
Group=chendahui
WorkingDirectory=/home/<USER>/uniscribe-mono/uniscribe-backend
Environment=PYTHONPATH=/home/<USER>/uniscribe-mono/uniscribe-backend
EnvironmentFile=/home/<USER>/uniscribe-mono/uniscribe-backend/.env
ExecStart=/home/<USER>/uniscribe-mono/uniscribe-backend/.venv/bin/python workers/stripe_consumer.py
Restart=always

[Install]
WantedBy=multi-user.target
```

**alignment-worker.service**

```ini
[Unit]
Description=UniScribe Speaker Alignment Worker
After=network.target redis.service
Requires=redis.service

[Service]
User=chendahui
Group=chendahui
WorkingDirectory=/home/<USER>/uniscribe-mono/uniscribe-backend
Environment=PYTHONPATH=/home/<USER>/uniscribe-mono/uniscribe-backend
EnvironmentFile=/home/<USER>/uniscribe-mono/uniscribe-backend/.env
ExecStart=/home/<USER>/uniscribe-mono/uniscribe-backend/.venv/bin/python workers/alignment_worker.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## 存储和数据管理

### 数据库配置

#### 开发环境 - Docker 容器

**MySQL 配置**

```yaml
mysql_db:
  image: mysql:8.0
  command: --default-authentication-plugin=mysql_native_password
    --default-time-zone='+00:00'
  environment:
    MYSQL_ROOT_PASSWORD: shiyin_root
    MYSQL_DATABASE: shiyin
    MYSQL_USER: shiyin_developer
    MYSQL_PASSWORD: shiyin_developer
    TZ: UTC
  volumes:
    - db_data:/var/lib/mysql
```

**Redis 配置**

```yaml
redis:
  image: redis:7.2-alpine
  command: redis-server --requirepass shiyin_redis
  volumes:
    - redis_data:/data
```

#### 生产环境 - Azure 托管服务

**Azure Database for MySQL**

- **服务类型**: Azure Database for MySQL - Flexible Server
- **连接方式**: 通过 SSL 加密连接
- **配置管理**: 通过 Azure Portal 或 CLI 管理
- **备份**: 自动备份，支持点时间恢复
- **监控**: Azure Monitor 集成监控
- **高可用**: 可配置区域冗余高可用性

**连接配置示例**:

```bash
# .env 文件中的连接字符串
SQLALCHEMY_DATABASE_URI=mysql+pymysql://username:<EMAIL>:3306/database_name?ssl_ca=DigiCertGlobalRootCA.crt.pem&ssl_disabled=False
```

**Azure Cache for Redis**

- **服务类型**: Azure Cache for Redis
- **层级**: Standard 或 Premium 层
- **连接方式**: 通过 SSL 加密连接
- **配置管理**: 通过 Azure Portal 管理
- **备份**: 支持数据持久化和备份
- **监控**: Azure Monitor 集成监控
- **高可用**: 内置高可用性和故障转移

**连接配置示例**:

```bash
# .env 文件中的 Redis 配置
REDIS_HOST=cachename.redis.cache.windows.net
REDIS_PORT=6380
REDIS_PASSWORD=your_redis_access_key
REDIS_SSL=True
```

**Azure 服务优势**:

- **托管服务**: 无需管理底层基础设施
- **自动备份**: 自动化备份和恢复
- **安全性**: 内置安全功能和合规性
- **可扩展性**: 按需扩展计算和存储资源
- **监控**: 集成的监控和告警功能

### 外部存储集成

#### Cloudflare R2

- **用途**: 音频文件存储
- **配置**: 通过环境变量配置访问密钥
- **特性**: S3 兼容 API

#### 腾讯云 COS

- **用途**: 备用存储方案
- **配置**: 通过环境变量配置密钥
- **区域**: ap-nanjing

## 监控和日志

### 日志配置

#### 应用日志

- **时区**: 统一使用 UTC
- **格式**: 结构化日志输出
- **级别**: 可通过环境变量配置

#### 错误追踪

- **Sentry**: 集成 Sentry 进行错误追踪
- **配置**: 通过 AXIOM_TOKEN 环境变量

### 健康检查

#### 服务依赖检查

```python
# 数据库连接检查
SQLALCHEMY_DATABASE_URI 连接测试

# Redis 连接检查
redis.ping() 测试

# 外部服务检查
各 API 密钥有效性验证
```

## 安全配置

### 网络安全

- **内部通信**: 使用 Docker 内部网络
- **外部访问**: 仅暴露必要端口
- **代理支持**: 支持 HTTP/HTTPS/SOCKS5 代理

### 密钥管理

- **环境变量**: 所有敏感信息通过环境变量传递
- **容器隔离**: 不同服务使用独立的环境变量作用域
- **版本控制**: .env 文件不纳入版本控制

## 部署环境对比

### 开发环境特点

- **部署方式**: Docker Compose 容器化
- **网络**: 使用自定义桥接网络 (app-network)
- **端口**: 映射到本地端口便于调试 (3307, 8000)
- **数据持久化**: 使用 Docker volumes
- **进程管理**: Docker 容器自动重启
- **日志**: Docker logs 查看
- **配置**: 通过 docker-compose.yml 和环境变量

### 生产环境特点

- **部署方式**: VM 直接部署 + Azure 托管服务
- **应用服务**: Systemd 管理的应用进程
- **数据库**: Azure Database for MySQL (托管服务)
- **缓存**: Azure Cache for Redis (托管服务)
- **网络**: 应用绑定 0.0.0.0:8000，数据库通过 SSL 连接
- **数据持久化**: Azure 托管服务自动备份
- **进程管理**: Systemd 服务管理应用进程
- **日志**: journalctl 查看应用日志，Azure Monitor 监控数据库
- **配置**: .env 文件和 systemd 配置

### 环境切换考虑

- **配置兼容性**: 环境变量保持一致
- **数据迁移**: 数据库和文件存储的迁移策略
- **监控差异**: 不同环境的监控方案
- **部署流程**: 开发测试后的生产部署流程

## 优化建议

### 性能优化

1. **镜像大小**: 使用多阶段构建减小镜像体积
2. **启动时间**: 优化依赖安装和缓存策略
3. **资源使用**: 根据负载调整 worker 数量

### 安全加固

1. **最小权限**: 使用非 root 用户运行应用
2. **网络隔离**: 进一步细化网络访问控制
3. **密钥轮换**: 实现自动化密钥轮换机制

### 运维改进

1. **健康检查**: 添加更完善的健康检查端点
2. **监控指标**: 集成 Prometheus/Grafana 监控
3. **日志聚合**: 使用 ELK 或类似方案聚合日志

## 总结

当前系统采用了双环境部署策略：开发环境使用 Docker 容器化方案，具有良好的可移植性和环境一致性；生产环境使用 VM 直接部署，通过 Systemd 管理进程，具有更好的性能和稳定性。

### 架构优势

- **环境隔离**: 开发和生产环境清晰分离
- **配置统一**: 通过环境变量实现配置管理
- **进程管理**: Systemd 提供可靠的进程管理和自动重启
- **日志管理**: 统一的日志收集和查看方式
- **资源控制**: 通过 Systemd 实现资源限制和监控

### 改进空间

- **监控增强**: 需要更完善的应用级监控
- **部署自动化**: 可以引入 CI/CD 流程
- **配置管理**: 考虑使用配置管理工具
- **备份策略**: 需要完善的数据备份和恢复方案
- **安全加固**: 进一步的安全配置和审计
