# 用户限制查询 API

## 概述

新增的用户限制查询 API 允许前端在用户点击 Quick Actions 模块中的 upload 或 youtube 按钮时，先查询当前用户的使用限制，以决定是否显示免费用户升级引导弹框。

## API 接口

### 获取用户限制信息

**接口地址:** `GET /user/limits`

**认证要求:** 需要 Bearer Token

**请求示例:**

```bash
curl -X GET http://localhost:8000/user/limits \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应格式:**

```json
{
  "transcriptionMinutesRemaining": 120,
  "dailyTranscriptionCountRemaining": 2
}
```

**响应字段说明:**

| 字段名                             | 类型    | 说明                     |
| ---------------------------------- | ------- | ------------------------ |
| `transcriptionMinutesRemaining`    | integer | 当月剩余转录时长（分钟） |
| `dailyTranscriptionCountRemaining` | integer | 当天剩余转录次数         |

## 业务逻辑

### 前端判断升级提示逻辑

前端可以根据返回的数据自行判断是否显示升级提示：

**对于免费用户**，当满足以下任一条件时显示升级提示：

1. **当月转录时长余额 <= 0**（`transcriptionMinutesRemaining <= 0`）
2. **当天转录次数余额 <= 0**（`dailyTranscriptionCountRemaining <= 0`）

**前端判断逻辑示例：**

```javascript
const shouldShowUpgradePrompt =
  response.transcriptionMinutesRemaining <= 0 ||
  response.dailyTranscriptionCountRemaining <= 0;
```

**注意：** 付费用户的 `dailyTranscriptionCountRemaining` 会返回 999999 表示无限制，因此不会触发升级提示。

### 用户类型判断

- **免费用户**: `has_paid_plan = false`

  - 当月转录时长限制：根据 entitlement 系统计算
  - 当天转录次数限制：3 次（匿名用户 2 次）

- **付费用户**: `has_paid_plan = true`
  - 当月转录时长限制：根据订阅计划
  - 当天转录次数限制：无限制（返回 999999 表示）

### 数据来源

1. **转录时长余额**: 通过 `EntitlementService.get_user_credits()` 获取
2. **转录次数统计**: 通过 `TranscriptionFile.get_user_today_transcribe_file_count()` 获取
3. **用户类型**: 通过 `user.has_paid_plan` 属性判断
4. **重置时间**: 通过 `Entitlement.get_next_reset_time()` 获取

## 前端集成建议

### 使用流程

1. 用户点击 upload 或 youtube 按钮
2. 前端调用 `/user/limits` 接口
3. 根据返回的数据判断是否显示升级提示
4. 如果需要显示升级提示，显示升级引导弹框
5. 否则，继续正常的上传/转录流程

### 示例代码

```javascript
async function handleQuickAction(actionType) {
  try {
    const response = await fetch("/user/limits", {
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
    });

    const limits = await response.json();

    // 前端判断是否显示升级提示
    const shouldShowUpgradePrompt =
      limits.transcriptionMinutesRemaining <= 0 ||
      limits.dailyTranscriptionCountRemaining <= 0;

    if (shouldShowUpgradePrompt) {
      // 显示升级引导弹框
      showUpgradePrompt({
        reason:
          limits.transcriptionMinutesRemaining <= 0
            ? "monthly_limit_exceeded"
            : "daily_limit_exceeded",
        remainingMinutes: limits.transcriptionMinutesRemaining,
        remainingCount: limits.dailyTranscriptionCountRemaining,
      });
    } else {
      // 继续正常流程
      if (actionType === "upload") {
        showUploadDialog();
      } else if (actionType === "youtube") {
        showYoutubeDialog();
      }
    }
  } catch (error) {
    console.error("Failed to check user limits:", error);
    // 发生错误时，可以选择继续正常流程或显示错误提示
  }
}
```

## 错误处理

### 401 Unauthorized

```json
{
  "message": "Bearer token is required"
}
```

### 500 Internal Server Error

```json
{
  "message": "Internal server error"
}
```

## 扩展性

该接口设计考虑了未来的扩展需求：

1. **AI 使用次数余额**: 可以在响应中添加 `aiUsageRemaining` 字段
2. **其他服务限制**: 可以添加翻译、字幕等服务的限制信息
3. **更细粒度的限制**: 可以按文件大小、时长等维度进行限制

## 测试

运行测试脚本验证接口功能：

```bash
docker exec uniscribe-backend-app-1 python tests/test_user_limits.py
```

## 相关文件

- **资源类**: `resources/user_limits.py`
- **控制器**: `controllers/user_limits.py`
- **字段定义**: `fields/usage.py`
- **路由注册**: `routes.py`
- **测试文件**: `tests/test_user_limits.py`
