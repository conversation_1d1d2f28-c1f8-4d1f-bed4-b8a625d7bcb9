# 任务系统重构路线图

## 概述

基于对当前任务系统的深入分析和业务需求，本文档提供了一个详细的重构路线图，重点解决性能瓶颈并增加关键业务功能。

## 重构目标

### 主要目标

1. **异步队列架构**: 将轮询机制替换为 Redis Streams 队列，大幅提升响应速度
2. **媒体预处理支持**: 支持大文件和复杂格式的服务端预处理
3. **用户体验优化**: 支持任务重试、免费用户手动触发、文本任务重新触发
4. **成本优化**: 免费用户文本任务改为手动触发，减少不必要的计算成本

### 成功指标

- 任务处理延迟：5秒 → 100ms（减少98%）
- 支持2GB+大文件自动预处理
- 免费用户文本任务成本降低60%
- 任务失败重试成功率提升至95%

## 重构阶段规划

### 第一阶段：队列基础设施 + 媒体预处理 (2-3周)

#### 1.1 Redis Streams 队列系统

**目标**: 将轮询机制替换为基于 Redis Streams 的消息队列

**具体任务**:

```python
# 新增任务队列服务
class TaskQueueService:
    def __init__(self):
        self.redis_client = redis.Redis(...)
        self.streams = {
            'media_preprocessing': 'tasks:media_preprocessing',  # 新增
            'transcription': 'tasks:transcription',
            'text': 'tasks:text',
            'speaker_diarization': 'tasks:speaker_diarization',
            'retry': 'tasks:retry'  # 重试队列
        }

    def enqueue_task(self, task_type: str, task_data: dict):
        stream_name = self.streams[task_type]
        return self.redis_client.xadd(stream_name, task_data)

    def consume_tasks(self, task_type: str, consumer_group: str):
        # 实现消费者逻辑
        pass
```

#### 1.2 媒体预处理任务

**目标**: 支持大文件和复杂格式的服务端预处理

**触发条件**:

- 文件大小 > 2GB
- 视频格式文件（.mov, .avi, .mkv, .mp4）
- 前端 ffmpeg.wasm 处理失败的文件
- YouTube 视频下载

**技术方案决策**:
经过分析，考虑到 YouTube 下载需要 yt-dlp (Python 库)，决定将所有媒体预处理任务放在 Python 环境中处理：

**Python 媒体预处理服务**:

```python
class MediaPreprocessingService:
    def process_youtube_task(self, youtube_url, file_id):
        # 使用 yt-dlp 下载音频
        audio_file = self.download_youtube_audio(youtube_url)
        r2_url = self.upload_to_r2(audio_file, file_id)
        self.update_file_record(file_id, r2_url)

    def process_ffmpeg_task(self, original_file_url, file_id):
        # 下载原始文件并用 ffmpeg 提取音频
        local_file = self.download_file(original_file_url)
        audio_file = self.extract_audio_with_ffmpeg(local_file)
        r2_url = self.upload_to_r2(audio_file, file_id)
        self.update_file_record(file_id, r2_url)
```

**部署架构**:

```
Backend API → Queue → Media Preprocessing Worker (Python) → 转录任务 → Go Service
```

**部署策略**:

- 媒体预处理工作进程独立部署（避免影响 API 性能）
- 可与 Go Service 部署在同一台机器（共享 CPU 密集型资源）
- 使用 systemd 独立管理各个组件

**架构优化**:

- **YouTube集成ffmpeg预处理**：YouTube下载时直接集成ffmpeg处理，避免重复任务
- **单一任务模式**：每个YouTube转录只创建一个media_preprocessing任务
- **智能预处理检查**：YouTube文件跳过重复的预处理检查

**预期收益**:

- 任务响应延迟从 5 秒降低到 100ms 以内
- 支持2GB+大文件处理
- 减少数据库查询压力
- 支持并行处理不同类型任务
- **避免重复任务**：YouTube转录任务数量减少50%

#### 1.2 Go Service 重构

**目标**: 修改 Go Service 以支持 Redis Streams 消费

**具体任务**:

```go
// 新增 Redis Streams 消费者
type StreamConsumer struct {
    client     *redis.Client
    streamName string
    groupName  string
}

func (c *StreamConsumer) ConsumeMessages(ctx context.Context) {
    for {
        messages, err := c.client.XReadGroup(ctx, &redis.XReadGroupArgs{
            Group:    c.groupName,
            Consumer: "worker-1",
            Streams:  []string{c.streamName, ">"},
            Count:    1,
            Block:    time.Second,
        }).Result()

        if err != nil {
            continue
        }

        // 处理消息
        for _, message := range messages {
            c.processMessage(message)
        }
    }
}
```

#### 1.3 数据库结构调整

**目标**: 支持新功能的数据库设计

**新增字段**:

```sql
-- 任务表新增字段
ALTER TABLE task ADD COLUMN retry_count INT DEFAULT 0;


-- 新增任务类型枚举值
-- TaskType.media_preprocessing = 10
```

**文件状态扩展**:

```python
class FileStatus(TypedEnum):
    uploading = 1
    uploaded = 2
    preprocessing = 3  # 新增
    transcribing = 4
    transcription_completed = 5  # 新增：转录完成，等待手动触发文本任务
    text_processing = 6
    completed = 7
    failed = 8
    preprocessing_failed = 9  # 新增
```

### 第二阶段：用户体验优化 (2-3周)

#### 2.1 任务重试机制

**目标**: 支持失败任务的手动重试

**核心功能**:

```python
class TaskRetryResource(Resource):
    @auth_required
    def post(self):
        task_id = parser.get_argument("taskId", type=int, required=True)
        task = Task.get_by_id(task_id)

        # 检查重试次数限制
        if task.retry_count >= 3:
            return {"error": "重试次数已达上限，请联系客服"}, 400

        # 重置任务状态并重新入队
        task.status = TaskStatus.pending.id
        task.error_message = None
        task.retry_count += 1

        queue_name = get_queue_name_by_task_type(task.task_type)
        enqueue_task(queue_name, task.to_dict())

        return {"message": "任务已重新加入队列"}
```

**预期收益**:

- 减少客服工作量
- 提升用户体验
- 任务成功率提升至95%

#### 2.2 状态管理统一

**目标**: 建立统一的任务状态管理机制

**设计方案**:

```python
class TaskStateMachine:
    VALID_TRANSITIONS = {
        TaskStatus.PENDING: [TaskStatus.PROCESSING, TaskStatus.FAILED],
        TaskStatus.PROCESSING: [TaskStatus.COMPLETED, TaskStatus.FAILED],
        TaskStatus.FAILED: [TaskStatus.PENDING],  # 支持重试
        TaskStatus.COMPLETED: []  # 终态
    }

    def transition(self, task_id: int, to_state: TaskStatus):
        with db.transaction():
            task = Task.get_by_id(task_id)
            if to_state not in self.VALID_TRANSITIONS[task.status]:
                raise InvalidTransitionError()

            # 记录状态变更历史
            TaskHistory.create(
                task_id=task_id,
                from_state=task.status,
                to_state=to_state,
                timestamp=datetime.now()
            )

            task.status = to_state
            task.save()
```

#### 2.3 错误处理增强

**目标**: 实现更智能的错误处理和重试机制

**重试策略**:

```python
class RetryPolicy:
    def __init__(self):
        self.strategies = {
            'network_error': ExponentialBackoff(max_retries=5),
            'rate_limit': LinearBackoff(max_retries=3),
            'service_unavailable': ExponentialBackoff(max_retries=10),
            'invalid_input': NoRetry()
        }

    def should_retry(self, error_type: str, attempt: int) -> bool:
        strategy = self.strategies.get(error_type, NoRetry())
        return strategy.should_retry(attempt)
```

#### 2.4 免费用户文本任务手动触发

**目标**: 免费用户文本任务改为手动开启，节省成本

**核心逻辑**:

```python
def update_file_status_after_transcription(file_id):
    file_info = TranscriptionFile.get_by_id(file_id)
    user = User.get_by_id(file_info.user_id)

    if user.plan == "free":
        # 免费用户：转录完成，等待手动触发文本任务
        file_info.status = FileStatus.transcription_completed.id
    else:
        # 付费用户：自动创建文本任务
        create_text_tasks(file_id)
        file_info.status = FileStatus.text_processing.id

class TriggerTextTasksResource(Resource):
    @auth_required
    def post(self):
        file_id = parser.get_argument("fileId", type=int, required=True)
        task_types = parser.get_argument("taskTypes", type=list, required=True)

        # 验证文件状态和用户权限
        file_info = TranscriptionFile.get_by_id(file_id)
        if file_info.status != FileStatus.transcription_completed.id:
            return {"error": "文件状态不允许触发文本任务"}, 400

        # 创建选定的文本任务
        for task_type in task_types:
            task = Task.create(file_id=file_id, task_type=task_type, manual_trigger=True)
            enqueue_task("text", task.to_dict())

        file_info.status = FileStatus.text_processing.id
        return {"message": "文本任务已开始处理"}
```

#### 2.5 文本任务重新触发

**目标**: 支持基于修改后文本的任务重新触发

**核心功能**:

```python
class RetriggerTextTasksResource(Resource):
    @auth_required
    def post(self):
        file_id = parser.get_argument("fileId", type=int, required=True)
        task_types = parser.get_argument("taskTypes", type=list, required=True)
        updated_segments = parser.get_argument("updatedSegments", type=list, required=False)

        # 更新转录结果
        if updated_segments:
            task_result = TaskResult.get_by_file_id(file_id)
            task_result.segments = updated_segments
            task_result.original_text = " ".join([seg.get("text", "") for seg in updated_segments])
            db.session.commit()

        # 创建新的文本任务
        for task_type in task_types:
            task = Task.create(
                file_id=file_id,
                task_type=task_type,
                manual_trigger=True,
                parent_task_id=None  # 标记为重新触发的任务
            )
            enqueue_task("text", task.to_dict())

        return {"message": "文本任务重新触发成功"}
```

**预期收益**:

- 免费用户文本任务成本降低60%
- 用户可按需选择文本处理类型
- 支持基于修改后文本的重新处理
- 提升用户满意度

### 第三阶段：监控与运维 (1-2周)

#### 3.1 指标监控系统

**目标**: 建立全面的系统监控

**监控指标**:

```python
# 业务指标
- 任务处理时长分布
- 各AI模型成功率
- 用户配额使用情况
- 队列长度和积压情况

# 技术指标
- 服务响应时间
- 错误率和错误类型分布
- 资源使用率（CPU、内存、网络）
- 数据库连接池状态
```

**实现方案**:

```python
from prometheus_client import Counter, Histogram, Gauge

# 定义指标
task_counter = Counter('tasks_total', 'Total tasks processed', ['task_type', 'status'])
task_duration = Histogram('task_duration_seconds', 'Task processing duration', ['task_type'])
queue_length = Gauge('queue_length', 'Current queue length', ['queue_type'])

# 在业务代码中记录指标
def process_task(task):
    start_time = time.time()
    try:
        # 处理任务
        result = do_process(task)
        task_counter.labels(task_type=task.type, status='success').inc()
        return result
    except Exception as e:
        task_counter.labels(task_type=task.type, status='error').inc()
        raise
    finally:
        duration = time.time() - start_time
        task_duration.labels(task_type=task.type).observe(duration)
```

#### 3.2 日志聚合和分析

**目标**: 统一日志格式，便于问题排查

**日志标准化**:

```python
import structlog

logger = structlog.get_logger()

# 结构化日志记录
logger.info(
    "task_started",
    task_id=task.id,
    task_type=task.type,
    user_id=task.user_id,
    file_size=task.file_size
)
```

#### 3.3 告警机制

**目标**: 及时发现和响应系统异常

**告警规则**:

```yaml
# Prometheus 告警规则
groups:
  - name: task_system
    rules:
      - alert: HighTaskFailureRate
        expr: rate(tasks_total{status="error"}[5m]) > 0.1
        for: 2m
        annotations:
          summary: "Task failure rate is high"

      - alert: QueueBacklog
        expr: queue_length > 100
        for: 5m
        annotations:
          summary: "Task queue has significant backlog"
```

### 第四阶段：性能优化 (2-3周)

#### 4.1 数据库优化

**目标**: 提升数据库查询性能

**优化措施**:

```sql
-- 添加复合索引
CREATE INDEX idx_task_status_priority_created
ON task(status, priority, created_time);

-- 分离历史数据
CREATE TABLE task_history (
    id BIGINT PRIMARY KEY,
    task_id BIGINT,
    status_change JSON,
    created_time TIMESTAMP,
    INDEX idx_task_id (task_id),
    INDEX idx_created_time (created_time)
);

-- 分区表（按月分区）
ALTER TABLE task_history
PARTITION BY RANGE (YEAR(created_time) * 100 + MONTH(created_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ...
);
```

#### 4.2 缓存策略

**目标**: 减少重复计算和数据库查询

**缓存设计**:

```python
class TaskCache:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.ttl = {
            'task_result': 3600,      # 1小时
            'user_quota': 300,        # 5分钟
            'model_selection': 1800   # 30分钟
        }

    def get_task_result(self, task_id: int):
        cache_key = f"task_result:{task_id}"
        cached = self.redis.get(cache_key)
        if cached:
            return json.loads(cached)

        # 从数据库获取并缓存
        result = TaskResult.get_by_task_id(task_id)
        if result:
            self.redis.setex(
                cache_key,
                self.ttl['task_result'],
                json.dumps(result.to_dict())
            )
        return result
```

#### 4.3 并发优化

**目标**: 提高系统并发处理能力

**优化方案**:

```python
# 连接池优化
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_size': 20,
    'max_overflow': 30,
    'pool_pre_ping': True,
    'pool_recycle': 3600
}

# Redis 连接池
redis_pool = redis.ConnectionPool(
    host='redis',
    port=6379,
    max_connections=50,
    retry_on_timeout=True
)

# 异步处理优化
import asyncio
import aioredis

async def process_tasks_batch(tasks):
    async with aioredis.from_url("redis://redis:6379") as redis:
        tasks_coroutines = [process_single_task(task, redis) for task in tasks]
        results = await asyncio.gather(*tasks_coroutines, return_exceptions=True)
        return results
```

## 风险管理

### 技术风险

1. **数据迁移风险**: 制定详细的数据备份和恢复计划
2. **性能回退风险**: 建立性能基准测试和回滚机制
3. **兼容性风险**: 保持 API 向后兼容，渐进式升级

### 业务风险

1. **服务中断风险**: 采用蓝绿部署，确保零停机升级
2. **数据丢失风险**: 实施多重备份和实时同步
3. **用户体验风险**: 在测试环境充分验证后再上线

### 缓解措施

```python
# 实施计划检查点
checkpoints = [
    {
        'phase': '基础设施升级',
        'success_criteria': [
            '消息队列延迟 < 100ms',
            '系统可用性 > 99.5%',
            '错误率 < 0.1%'
        ],
        'rollback_plan': '切换回轮询机制'
    },
    {
        'phase': '架构优化',
        'success_criteria': [
            '服务响应时间 < 500ms',
            '资源使用率 < 80%',
            '新功能部署时间 < 30min'
        ],
        'rollback_plan': '回退到单体架构'
    }
]
```

## 实施时间表

### 总体时间安排

- **第1-3周**: 队列基础设施 + 媒体预处理
- **第4-6周**: 用户体验优化功能
- **第7-8周**: 监控与性能优化
- **第9周**: 测试和上线

### 详细里程碑

1. **Week 1**: Redis Streams 队列系统实现
2. **Week 2**: 媒体预处理任务开发
3. **Week 3**: Go Service 队列消费重构
4. **Week 4**: 任务重试机制实现
5. **Week 5**: 免费用户手动触发功能
6. **Week 6**: 文本任务重新触发功能
7. **Week 7**: 监控系统和性能优化
8. **Week 8**: 全面测试和调优
9. **Week 9**: 生产环境部署

## 成功验收标准

### 性能指标

- 任务处理延迟: < 100ms (当前: 5s)
- 支持2GB+大文件自动预处理
- 队列处理吞吐量: > 500 tasks/min (当前: 200 tasks/min)

### 功能指标

- 任务重试成功率: > 95%
- 免费用户文本任务成本降低: > 60%
- 媒体预处理成功率: > 90%
- 文本任务重新触发响应时间: < 5s

### 可靠性指标

- 系统可用性: > 99.9% (当前: 99.5%)
- 数据一致性: 100% (无数据丢失)
- 错误率: < 0.05% (当前: 0.1%)

### 用户体验指标

- 任务失败后重试操作便利性: 用户满意度 > 90%
- 免费用户手动触发文本任务使用率: > 70%
- 文本修改后重新触发使用率: > 50%

## 总结

这个重构路线图专注于解决当前系统的核心痛点，同时增加关键的业务功能：

### 核心改进

1. **队列架构**: 将轮询机制替换为 Redis Streams，大幅提升响应速度
2. **媒体预处理**: 支持大文件和复杂格式的服务端处理
3. **用户体验**: 任务重试、手动触发、重新触发等功能显著提升用户满意度
4. **成本优化**: 免费用户文本任务手动触发，有效控制运营成本

### 实施策略

- **保持架构简单**: 不进行微服务拆分，保持当前的双服务架构
- **渐进式改进**: 分阶段实施，每个阶段都有明确的验收标准
- **风险可控**: 保持向后兼容，支持快速回滚
- **团队适配**: 考虑团队规模和运维能力，避免过度复杂化

### 预期收益

- **性能提升**: 任务处理延迟减少98%，吞吐量提升150%
- **成本节约**: 免费用户文本任务成本降低60%
- **用户体验**: 支持任务重试和重新触发，提升用户满意度
- **运维效率**: 简化的监控和告警机制，降低运维负担

通过这个务实的重构方案，既能解决当前的技术债务，又能为业务发展提供强有力的技术支撑。
