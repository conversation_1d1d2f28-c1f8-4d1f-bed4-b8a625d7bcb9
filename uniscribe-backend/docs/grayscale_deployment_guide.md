# 灰度发布部署指南

## 🎯 概述

本指南介绍如何部署和使用任务系统的灰度发布功能，实现从 HTTP 轮询到 Redis 队列的平滑迁移。

## 🏗️ 系统架构

### 灰度前（原有架构）

```
Python 后端 → 创建任务到数据库
Go 服务 → HTTP 轮询获取任务 → 处理任务
```

### 灰度后（新架构）

```
Python 后端 → 创建任务到数据库 → 根据灰度规则决定是否入队
                                ↓
                        Redis 优先级队列
                    (高优先级 + 低优先级)
                                ↓
Go 服务 → 同时支持 HTTP 轮询 + Redis 队列消费 → 优先处理付费用户任务
```

## 📋 部署步骤

### 1. 数据库迁移

执行数据库迁移脚本：

```bash
# 连接到数据库
mysql -h your-db-host -u username -p database_name

# 执行迁移脚本
source migrations/add_consumption_mode.sql

# 验证字段添加成功
DESCRIBE task;
```

### 2. Python 后端部署

#### 2.1 更新代码

确保包含以下新文件：

- `services/grayscale_service.py` - 灰度规则服务
- `resources/grayscale.py` - 灰度监控接口
- 更新的 `controllers/task.py` - 任务创建逻辑
- 更新的 `models/task.py` - 数据库模型

#### 2.2 环境变量配置

```bash
# 基础配置（默认关闭队列模式）
ENABLE_QUEUE_MODE=false

# 可选的灰度配置（当 ENABLE_QUEUE_MODE=true 时生效）
QUEUE_MODE_PERCENTAGE=0                    # 百分比灰度 (0-100)
QUEUE_MODE_USER_WHITELIST=""               # 用户白名单 (逗号分隔)
```

#### 2.3 部署命令

```bash
# 停止服务
sudo systemctl stop uniscribe-backend

# 更新代码
git pull origin task-system-refactor

# 重启服务
sudo systemctl start uniscribe-backend

# 检查服务状态
sudo systemctl status uniscribe-backend
```

### 3. Go 服务部署

Go 服务已经支持双模式，无需修改配置，直接部署：

```bash
# 停止服务
sudo systemctl stop uniscribe-service

# 更新代码
git pull origin task-system-refactor

# 重新编译
cd uniscribe-service
go build -o bin/uniscribe-service cmd/uniscribe-service/main.go

# 重启服务
sudo systemctl start uniscribe-service

# 检查服务状态
sudo systemctl status uniscribe-service
```

## 🚀 灰度策略

### 阶段1：保守验证（建议 1-2 天）

```bash
# 启用队列模式，但只有白名单用户使用
ENABLE_QUEUE_MODE=true
QUEUE_MODE_USER_WHITELIST=1001,1002  # 测试用户
```

**监控指标**：

- 白名单用户的任务处理成功率
- 队列消费延迟
- 错误日志

### 阶段2：小比例灰度（建议 2-3 天）

```bash
# 5% 用户使用队列模式
ENABLE_QUEUE_MODE=true
QUEUE_MODE_PERCENTAGE=5
```

**监控指标**：

- 两种模式的任务处理对比
- 系统资源使用情况
- 用户体验指标

### 阶段3：扩大灰度（建议 3-5 天）

```bash
# 30% 用户使用队列模式
ENABLE_QUEUE_MODE=true
QUEUE_MODE_PERCENTAGE=30
```

### 阶段4：全量切换

```bash
# 100% 用户使用队列模式
ENABLE_QUEUE_MODE=true
QUEUE_MODE_PERCENTAGE=100
```

## 📊 监控和告警

### 监控接口

```bash
# 队列统计
curl http://localhost:5000/queue/stats

# 队列健康检查
curl http://localhost:5000/queue/health
```

### 关键指标

1. **任务分布**：

   - HTTP 轮询模式任务数量
   - Redis 队列模式任务数量
   - 高优先级 vs 低优先级队列分布
   - 各状态任务分布

2. **处理性能**：

   - 付费用户任务处理延迟
   - 免费用户任务处理延迟
   - 任务成功率
   - 队列积压情况

3. **优先级效果**：

   - 高优先级队列消费速度
   - 低优先级队列等待时间
   - 付费用户体验提升

4. **系统健康**：
   - 错误率
   - 资源使用率
   - 服务可用性

### 告警规则

```bash
# 失败率过高
if failed_percentage > 10% then alert

# 任务积压
if pending_tasks > 100 then alert

# 队列积压
if queue_length > 50 then alert
```

## 🔧 故障处理

### 快速回滚

如果发现问题，可以立即回滚到 HTTP 轮询模式：

```bash
# 方法1：关闭队列模式
export ENABLE_QUEUE_MODE=false
sudo systemctl restart uniscribe-backend

# 方法2：设置为 0% 灰度
export QUEUE_MODE_PERCENTAGE=0
sudo systemctl restart uniscribe-backend
```

### 常见问题

1. **队列积压**：

   - 检查 Redis 连接
   - 检查 Go 服务队列消费状态
   - 增加消费者数量

2. **任务重复处理**：

   - 检查数据库 consumption_mode 字段
   - 确认任务查询逻辑正确

3. **性能下降**：
   - 监控 Redis 性能
   - 检查网络延迟
   - 优化队列配置

## 🧪 测试验证

### 功能测试

```bash
# 运行灰度系统测试
cd uniscribe-backend
python test_grayscale_system.py
```

### 手动验证

1. **创建任务**：

   - 上传文件创建转录任务
   - 检查任务的 consumption_mode 字段

2. **任务处理**：

   - 观察 Go 服务日志
   - 确认任务被正确消费

3. **监控检查**：
   - 访问监控接口
   - 检查统计数据

## 📈 成功标准

### 技术指标

- 任务处理成功率 > 99%
- 平均处理延迟 < 原有系统的 120%
- 系统错误率 < 1%

### 业务指标

- 用户体验无明显下降
- 系统稳定性保持
- 处理能力有所提升

## 🎉 完成后清理

当灰度完成并稳定运行后，可以考虑：

1. **移除 HTTP 轮询代码**（可选）
2. **优化数据库索引**
3. **更新监控配置**
4. **文档更新**

## 📞 联系方式

如果在部署过程中遇到问题，请：

1. 查看服务日志
2. 检查监控接口
3. 联系开发团队
