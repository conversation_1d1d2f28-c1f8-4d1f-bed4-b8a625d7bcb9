# 文件状态流程图

## 📋 概述

本文档详细描述了 TranscriptionFile 的完整状态流转过程，包括所有可能的状态转换路径和触发条件。

## 🎯 状态定义

### 基础状态
- `uploading = 1` - 文件上传中
- `uploaded = 2` - 文件上传完成，等待处理
- `processing = 3` - 转录处理中
- `partially_completed = 4` - 部分任务完成
- `completed = 5` - 全部任务完成
- `failed = 6` - 处理失败
- `completed_with_errors = 7` - 完成但有错误

### 媒体预处理状态
- `preprocessing = 8` - 媒体预处理中
- `preprocessing_failed = 9` - 媒体预处理失败

## 🔄 完整状态流程图

```mermaid
flowchart TD
    %% 文件上传阶段
    A[开始上传] --> B[uploading<br/>上传中]
    B --> C{上传是否成功?}
    C -->|成功| D[uploaded<br/>已上传]
    C -->|失败| E[failed<br/>上传失败]
    
    %% 转录请求阶段
    D --> F[用户点击转录]
    F --> G{需要媒体预处理?}
    
    %% 媒体预处理分支
    G -->|需要预处理| H[preprocessing<br/>媒体预处理中]
    G -->|无需预处理| I[processing<br/>转录处理中]
    
    %% 预处理结果
    H --> J{预处理是否成功?}
    J -->|成功| K[uploaded<br/>预处理完成]
    J -->|失败| L[preprocessing_failed<br/>预处理失败]
    
    %% 预处理完成后自动转录
    K --> M[自动创建转录任务]
    M --> I
    
    %% 预处理失败后重试
    L --> N[用户重试]
    N --> H
    
    %% 转录处理阶段
    I --> O{转录任务状态}
    
    %% 转录成功分支
    O -->|转录完成| P{是否启用说话人识别?}
    P -->|否| Q[completed<br/>全部完成]
    P -->|是| R{说话人识别状态}
    
    %% 说话人识别处理
    R -->|识别完成| S{是否需要对齐?}
    S -->|需要对齐| T{对齐状态}
    S -->|无需对齐| Q
    T -->|对齐完成| Q
    T -->|对齐失败| U[completed_with_errors<br/>完成但有错误]
    R -->|识别失败| U
    
    %% 转录失败分支
    O -->|转录失败| V[failed<br/>转录失败]
    
    %% 部分完成状态
    O -->|部分任务完成| W[partially_completed<br/>部分完成]
    W --> X{用户操作}
    X -->|重试失败任务| I
    X -->|接受部分结果| Q
    
    %% 失败后重试
    V --> Y[用户重试]
    Y --> I
    E --> Z[用户重新上传]
    Z --> A
    
    %% 样式定义
    classDef uploadStatus fill:#e3f2fd
    classDef preprocessStatus fill:#fff3e0
    classDef processStatus fill:#f3e5f5
    classDef successStatus fill:#e8f5e8
    classDef errorStatus fill:#ffebee
    classDef userAction fill:#f5f5f5,stroke:#666,stroke-dasharray: 5 5
    
    %% 应用样式
    class A,B,D uploadStatus
    class H,K,L,M preprocessStatus
    class I,O,P,R,S,T processStatus
    class Q successStatus
    class E,V,U errorStatus
    class F,N,X,Y,Z userAction
```

## 📊 状态转换表

### 主要转换路径

| 当前状态 | 触发条件 | 目标状态 | 说明 |
|---------|---------|---------|------|
| `uploading` | 上传成功 | `uploaded` | 文件上传完成 |
| `uploading` | 上传失败 | `failed` | 上传过程出错 |
| `uploaded` | 需要预处理 | `preprocessing` | 大文件或复杂格式 |
| `uploaded` | 无需预处理 | `processing` | 直接开始转录 |
| `preprocessing` | 预处理成功 | `uploaded` | 重新进入上传完成状态 |
| `preprocessing` | 预处理失败 | `preprocessing_failed` | ffmpeg 处理失败 |
| `preprocessing_failed` | 用户重试 | `preprocessing` | 重新尝试预处理 |
| `processing` | 转录完成 | `completed` | 所有任务成功 |
| `processing` | 转录失败 | `failed` | 关键任务失败 |
| `processing` | 部分完成 | `partially_completed` | 部分任务成功 |
| `partially_completed` | 重试成功 | `completed` | 失败任务重试成功 |
| `completed` | 后续任务失败 | `completed_with_errors` | 非关键任务失败 |

### 特殊转换

| 状态 | 特殊情况 | 处理方式 |
|------|---------|---------|
| `preprocessing` | 用户取消 | 保持当前状态，等待完成 |
| `processing` | 系统重启 | 自动恢复处理 |
| `failed` | 用户删除文件 | 标记为已删除 |
| 任意状态 | 管理员干预 | 可手动修改状态 |

## 🔍 状态检查逻辑

### 自动状态更新

文件状态由 `update_file_status()` 函数根据关联任务状态自动计算：

```python
def update_file_status(file_id):
    # 优先级顺序（从高到低）
    
    # 1. 媒体预处理失败 → preprocessing_failed
    if media_preprocessing_status == TaskStatus.failed.id:
        file.status = TranscriptionFileStatus.preprocessing_failed.id
    
    # 2. 媒体预处理进行中 → preprocessing  
    elif media_preprocessing_status == TaskStatus.processing.id:
        file.status = TranscriptionFileStatus.preprocessing.id
    
    # 3. 所有任务待处理 → uploaded
    elif all_pending:
        file.status = TranscriptionFileStatus.uploaded.id
    
    # 4. 关键任务失败 → failed
    elif critical_task_failed:
        file.status = TranscriptionFileStatus.failed.id
    
    # 5. 转录任务失败 → failed
    elif transcription_status == TaskStatus.failed.id:
        file.status = TranscriptionFileStatus.failed.id
    
    # 6. 转录任务处理中 → processing
    elif transcription_status == TaskStatus.processing.id:
        file.status = TranscriptionFileStatus.processing.id
    
    # 7. 根据完成情况确定最终状态
    # completed / partially_completed / completed_with_errors
```

## 🎯 关键决策点

### 1. 媒体预处理触发条件

```python
def should_preprocess(transcription_file):
    # 文件大小检查
    if file_size > 2GB:
        return True, "large_file"
    
    # 视频格式检查  
    if is_video_format(filename):
        return True, "video_format"
    
    # 复杂音频格式检查
    if is_complex_audio_format(filename):
        return True, "complex_audio_format"
    
    return False, None
```

### 2. 任务完成状态判断

```python
# 完成状态优先级
if all_completed and alignment_completed:
    return "completed"
elif all_finalized and has_successful_tasks:
    if has_failed_non_critical_tasks:
        return "completed_with_errors"
    else:
        return "completed"
elif has_successful_critical_tasks:
    return "partially_completed"
else:
    return "failed"
```

## 🚨 异常处理

### 状态不一致处理

1. **任务状态与文件状态不匹配**
   - 触发：定时检查或手动触发
   - 处理：重新计算文件状态

2. **长时间停留在中间状态**
   - 监控：超过预期时间的文件
   - 处理：检查任务状态，必要时重置

3. **并发状态更新冲突**
   - 预防：数据库事务保护
   - 处理：重试机制

## 📈 监控指标

### 状态分布统计

```sql
-- 各状态文件数量
SELECT 
    status,
    COUNT(*) as count,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM transcription_file WHERE is_deleted = FALSE) as percentage
FROM transcription_file 
WHERE is_deleted = FALSE 
GROUP BY status;
```

### 状态停留时间

```sql
-- 平均处理时间
SELECT 
    status,
    AVG(TIMESTAMPDIFF(MINUTE, created_time, updated_time)) as avg_minutes
FROM transcription_file 
WHERE is_deleted = FALSE 
GROUP BY status;
```

## 🔧 故障排除

### 常见问题

1. **文件卡在 `preprocessing` 状态**
   - 检查媒体预处理消费者是否运行
   - 查看预处理任务是否失败

2. **文件卡在 `processing` 状态**
   - 检查转录服务是否正常
   - 查看任务队列是否积压

3. **状态显示不正确**
   - 手动触发状态更新
   - 检查任务状态是否同步

### 调试命令

```bash
# 检查特定文件状态
python -c "
from models.transcription_file import TranscriptionFile
from controllers.task import update_file_status
file = TranscriptionFile.get_by_id(FILE_ID)
print(f'当前状态: {file.status}')
update_file_status(FILE_ID)
print(f'更新后状态: {file.status}')
"

# 查看文件关联的所有任务
python -c "
from models.task import Task
tasks = Task.get_all_by_file_id(FILE_ID)
for task in tasks:
    print(f'任务类型: {task.task_type}, 状态: {task.status}')
"
```

这个完整的状态流程图涵盖了所有可能的状态转换，包括新增的媒体预处理状态，可以作为开发和运维的重要参考文档。
