# 通用频率限制 (Rate Limit) 使用指南

本文档介绍如何使用新的通用频率限制功能，该功能从原有的 Resend 邮件发送频率限制中抽象而来，现在可以用于任何需要频率控制的场景。

## 功能特性

- **多种限制策略**: 支持滑动窗口和固定窗口两种策略
- **装饰器支持**: 简单易用的装饰器模式
- **多种后端**: 支持 Redis 和内存两种存储后端
- **灵活配置**: 支持自定义标识符、等待模式等
- **异常处理**: 可选择等待或抛出异常的处理方式

## 快速开始

### 基本装饰器使用

```python
from libs.rate_limit import rate_limit

@rate_limit(max_requests=5, window_size=60.0)
def api_endpoint(data):
    """每分钟最多5次请求"""
    return process_data(data)
```

### 不同策略

```python
# 滑动窗口（默认）
@rate_limit(max_requests=10, window_size=60.0, strategy="sliding_window")
def sliding_window_api(data):
    return data

# 固定窗口
@rate_limit(max_requests=100, window_size=3600.0, strategy="fixed_window")
def fixed_window_api(data):
    return data
```

## 详细配置

### 装饰器参数

- `max_requests`: 最大请求数
- `window_size`: 时间窗口大小（秒）
- `strategy`: 限制策略 ("sliding_window" 或 "fixed_window")
- `key_prefix`: 缓存键前缀，用于区分不同的限制器
- `backend`: 存储后端，默认使用 Redis
- `identifier_func`: 自定义标识符函数
- `wait`: 是否等待，False 时超限抛出异常

### 自定义标识符

```python
def get_user_id(*args, **kwargs):
    return kwargs.get('user_id', 'anonymous')

@rate_limit(
    max_requests=10,
    window_size=60.0,
    identifier_func=get_user_id,
    key_prefix="user_api"
)
def user_specific_api(data, user_id):
    """每个用户独立的频率限制"""
    return f"User {user_id}: {data}"
```

### 异常模式

```python
from libs.rate_limit import RateLimitExceeded

@rate_limit(max_requests=5, window_size=60.0, wait=False)
def strict_api(data):
    """超限时抛出异常而不是等待"""
    return data

try:
    result = strict_api("test")
except RateLimitExceeded as e:
    print(f"Rate limit exceeded: {e}")
```

## 直接使用 RateLimiter 类

```python
from libs.rate_limit import RateLimiter, SlidingWindowStrategy, MemoryRateLimitBackend

# 创建策略和后端
strategy = SlidingWindowStrategy(max_requests=5, window_size=60.0)
backend = MemoryRateLimitBackend()
limiter = RateLimiter(strategy, backend, "my_service")

# 检查是否允许
allowed, wait_time = limiter.is_allowed("user123")
if allowed:
    # 执行操作
    pass
else:
    print(f"Rate limited, wait {wait_time} seconds")

# 或者自动等待
limiter.wait_if_needed("user123")
```

## 不同后端

### Redis 后端

```python
from libs.rate_limit import RedisRateLimitBackend

backend = RedisRateLimitBackend(prefix="my_app")
```

### 内存后端（推荐用于单机部署）

```python
from libs.rate_limit import MemoryRateLimitBackend

backend = MemoryRateLimitBackend()
```

**注意**：在当前项目中，邮件发送和邮箱验证服务直接使用内存后端，因为：
- 单机部署场景下内存后端足够使用
- 避免 Redis 依赖和连接问题
- 性能更好，延迟更低
- 简化部署和维护

## 实际应用场景

### API 服务

```python
class APIService:
    @rate_limit(max_requests=1000, window_size=3600.0, key_prefix="api_general")
    def general_endpoint(self, data):
        return self.process(data)
    
    @rate_limit(max_requests=10, window_size=60.0, key_prefix="api_upload")
    def upload_endpoint(self, file_data):
        return self.upload(file_data)
```

### 基于 IP 的限制

```python
def get_client_ip(*args, **kwargs):
    return kwargs.get('client_ip', '127.0.0.1')

@rate_limit(
    max_requests=100,
    window_size=3600.0,
    identifier_func=get_client_ip,
    key_prefix="ip_limit"
)
def handle_request(endpoint, client_ip):
    return f"Handled {endpoint} from {client_ip}"
```

### 邮件发送限制

```python
# 使用固定窗口策略和内存后端，符合 Resend API 要求
@rate_limit(
    max_requests=2,
    window_size=1.0,
    strategy="fixed_window",
    key_prefix="email_send",
    backend=MemoryRateLimitBackend()
)
def send_email(params):
    """每秒最多发送2封邮件，符合 Resend API 限制"""
    return email_service.send(params)
```

### 邮箱验证限制

```python
# 使用固定窗口策略和内存后端，符合 Usercheck API 要求
@rate_limit(
    max_requests=1,
    window_size=1.0,
    strategy="fixed_window",
    key_prefix="usercheck_api",
    backend=MemoryRateLimitBackend()
)
def check_domain(self, domain: str):
    """每秒最多1次域名检查，符合 Usercheck API 限制"""
    return self._check_domain_raw(domain)
```

## 策略对比

### 滑动窗口 vs 固定窗口

**滑动窗口 (Sliding Window)**:
- 更平滑的限制
- 任意时间点的过去N秒内的请求数
- 适合需要精确控制的场景

**固定窗口 (Fixed Window)**:
- 更简单的实现
- 按固定时间段重置计数
- 可能出现边界突发请求
- 适合粗粒度控制

## 性能考虑

- Redis 后端适合分布式环境
- 内存后端适合单机测试
- 滑动窗口比固定窗口消耗更多存储
- 合理设置 TTL 避免内存泄漏

## 监控和调试

```python
import logging

# 启用日志查看限制情况
logging.basicConfig(level=logging.INFO)

@rate_limit(max_requests=5, window_size=60.0)
def monitored_function(data):
    # 当触发限制时会记录日志
    return data
```

## 测试

```python
# 使用内存后端进行测试
from libs.rate_limit import MemoryRateLimitBackend

@rate_limit(
    max_requests=2,
    window_size=1.0,
    backend=MemoryRateLimitBackend()
)
def test_function(data):
    return data

# 测试限制行为
test_function("first")   # 成功
test_function("second")  # 成功
test_function("third")   # 会等待或抛出异常
```

## 迁移指南

### 从旧的 ResendRateLimiter 迁移

旧代码：
```python
from libs.send_email import ResendRateLimiter

limiter = ResendRateLimiter()
limiter.wait_if_needed()
resend.Emails.send(params)
```

新代码：
```python
from libs.rate_limit import rate_limit

@rate_limit(
    max_requests=2,
    window_size=1.0,
    strategy="fixed_window",  # 符合 Resend API 要求
    key_prefix="resend_api"
)
def send_email_with_limit(params):
    return resend.Emails.send(params)
```

## 最佳实践

1. **选择合适的策略**:
   - 大多数情况下使用滑动窗口
   - 外部API有固定窗口限制时使用固定窗口（如邮件发送）
2. **设置合理的前缀**: 避免不同服务间的键冲突
3. **监控限制情况**: 通过日志了解限制触发频率
4. **测试边界情况**: 验证限制在边界条件下的行为
5. **考虑用户体验**: 在等待和异常间选择合适的处理方式
6. **遵循外部服务限制**:
   - Resend API: 每秒2次请求，使用固定窗口
   - Usercheck API: 每秒1次请求，使用固定窗口
   - 其他API: 根据具体文档选择合适策略

## 故障排除

### 常见问题

1. **Redis 连接失败**: 检查 Redis 配置和网络连接
2. **限制过于严格**: 调整 max_requests 和 window_size 参数
3. **不同实例间不同步**: 确保使用相同的 Redis 实例
4. **内存泄漏**: 检查 TTL 设置是否合理

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('libs.rate_limit').setLevel(logging.DEBUG)

# 检查当前限制状态
limiter = RateLimiter(strategy, backend, "debug")
allowed, wait_time = limiter.is_allowed("test")
print(f"Allowed: {allowed}, Wait time: {wait_time}")
```
