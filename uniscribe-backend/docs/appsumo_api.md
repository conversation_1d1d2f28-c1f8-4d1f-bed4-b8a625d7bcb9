# AppSumo API 文档

本文档描述了 AppSumo 集成的 API 端点。

## OAuth 流程

### 1. 用户授权

当用户从 AppSumo 购买页面点击"激活产品"按钮时，AppSumo 会将用户重定向到我们的 OAuth 授权页面，附带 `license_key` 参数。

前端需要处理这个重定向，并引导用户完成登录或注册流程。

### 2. 激活 License

**端点**: `POST /auth/appsumo/activate`

**请求头**:
```
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "code": "授权码"
}
```

**响应**:
```json
{
  "success": true,
  "message": "License activated successfully"
}
```

## Webhook 事件

AppSumo 会通过 webhook 发送各种事件通知，包括购买、激活、升级、降级和停用等。

**端点**: `POST /webhook/appsumo`

**请求头**:
```
X-AppSumo-Signature: <signature>
X-AppSumo-Timestamp: <timestamp>
```

**请求体**:
根据事件类型不同，请求体会有所不同。详见 [AppSumo 文档](https://appsumo.com/partners/api-docs)。

**响应**:
```json
{
  "success": true,
  "event": "event_type",
  "message": "Event processed successfully"
}
```

## 实现说明

1. `/auth/appsumo/activate` 端点使用 `auth_required` 装饰器，要求用户先登录才能调用。
2. 该端点通过 `g.user_id` 获取当前登录用户的 ID，无需在请求中提供用户信息。
3. 这种设计使得前端可以在用户已登录的情况下，直接激活 AppSumo license，无需重新输入用户信息。
4. 旧的 `/oauth/appsumo/login` 端点已被移除，所有 AppSumo 激活操作都应使用新的 `/auth/appsumo/activate` 端点。
