# AppSumo 集成测试指南

本文档介绍如何使用 AppSumo 测试工具来测试 AppSumo 集成功能。

## 前提条件

1. 确保已经完成 AppSumo 集成的开发和部署
2. 确保已经配置了 AppSumo 相关的环境变量：
   - `APPSUMO_API_KEY`: AppSumo API 密钥，用于签名验证
   - `APPSUMO_WEBHOOK_URL`: AppSumo webhook URL
     - 在本地环境中，默认为 `http://localhost:8000/webhook/appsumo`
     - 在 Docker 环境中，默认为 `http://web:8000/webhook/appsumo`

## 测试工具

我们提供了一个命令行工具 `appsumo-test`，用于模拟 AppSumo 发送的各种事件。

### 发送单个事件

使用 `send-event` 子命令发送单个事件：

```bash
flask admin appsumo-test send-event --event=purchase --tier=1
```

参数说明：

- `--event`: 事件类型，可选值为 `purchase`、`activate`、`upgrade`、`downgrade`、`deactivate`
- `--license-key`: License Key，如果不提供则自动生成
- `--prev-license-key`: 之前的 License Key（仅用于 `upgrade`/`downgrade` 事件）
- `--tier`: 产品等级，1 或 2
- `--test`: 是否为测试事件
- `--url`: Webhook URL，默认使用配置中的 URL
- `--api-key`: AppSumo API 密钥，默认使用配置中的 API 密钥

### 模拟完整流程

使用 `simulate-flow` 子命令模拟完整的 AppSumo 流程：

```bash
flask admin appsumo-test simulate-flow --tier=1
```

这个命令会依次发送以下事件：

1. `purchase`: 购买事件
2. `activate`: 激活事件
3. `upgrade`/`downgrade`: 升级/降级事件（如果 `tier=1` 则升级到 `tier=2`，否则降级到 `tier=1`）
4. `deactivate`: 停用事件

参数说明：

- `--tier`: 初始产品等级，1 或 2
- `--test`: 是否为测试事件
- `--url`: Webhook URL，默认使用配置中的 URL
- `--api-key`: AppSumo API 密钥，默认使用配置中的 API 密钥

## 测试场景

### 1. 测试购买和激活

```bash
# 生成一个 license key
LICENSE_KEY=$(uuidgen)

# 发送购买事件
flask admin appsumo-test send-event --event=purchase --license-key=$LICENSE_KEY --tier=1 --api-key=your_api_key_here

# 发送激活事件
flask admin appsumo-test send-event --event=activate --license-key=$LICENSE_KEY --tier=1 --api-key=your_api_key_here
```

### 2. 测试升级

```bash
# 生成两个 license key
LICENSE_KEY1=$(uuidgen)
LICENSE_KEY2=$(uuidgen)

# 发送购买和激活事件
flask admin appsumo-test send-event --event=purchase --license-key=$LICENSE_KEY1 --tier=1 --api-key=your_api_key_here
flask admin appsumo-test send-event --event=activate --license-key=$LICENSE_KEY1 --tier=1 --api-key=your_api_key_here

# 发送升级事件
flask admin appsumo-test send-event --event=upgrade --license-key=$LICENSE_KEY2 --prev-license-key=$LICENSE_KEY1 --tier=2 --api-key=your_api_key_here
```

### 3. 测试降级

```bash
# 生成两个 license key
LICENSE_KEY1=$(uuidgen)
LICENSE_KEY2=$(uuidgen)

# 发送购买和激活事件
flask admin appsumo-test send-event --event=purchase --license-key=$LICENSE_KEY1 --tier=2 --api-key=your_api_key_here
flask admin appsumo-test send-event --event=activate --license-key=$LICENSE_KEY1 --tier=2 --api-key=your_api_key_here

# 发送降级事件
flask admin appsumo-test send-event --event=downgrade --license-key=$LICENSE_KEY2 --prev-license-key=$LICENSE_KEY1 --tier=1 --api-key=your_api_key_here
```

### 4. 测试停用

```bash
# 生成一个 license key
LICENSE_KEY=$(uuidgen)

# 发送购买和激活事件
flask admin appsumo-test send-event --event=purchase --license-key=$LICENSE_KEY --tier=1 --api-key=your_api_key_here
flask admin appsumo-test send-event --event=activate --license-key=$LICENSE_KEY --tier=1 --api-key=your_api_key_here

# 发送停用事件
flask admin appsumo-test send-event --event=deactivate --license-key=$LICENSE_KEY --tier=1 --api-key=your_api_key_here
```

### 5. 测试完整流程

```bash
# 模拟从 tier 1 开始的完整流程
flask admin appsumo-test simulate-flow --tier=1 --api-key=your_api_key_here

# 模拟从 tier 2 开始的完整流程
flask admin appsumo-test simulate-flow --tier=2 --api-key=your_api_key_here

# 模拟测试事件
flask admin appsumo-test simulate-flow --tier=1 --test --api-key=your_api_key_here
```

## 验证测试结果

1. 检查数据库中的 `appsumo_license` 表，确认 license 记录已正确创建和更新
2. 检查数据库中的 `purchases` 表，确认购买记录已正确创建
3. 检查数据库中的 `entitlements` 表，确认权益记录已正确创建和更新
4. 检查用户的权益是否正确反映在 UI 中

## 请求和事件数据格式

### 完整请求格式

AppSumo 发送的完整请求格式如下：

```json
{
  "header": "{\"content-length\":\"\",\"content-type\":\"application/json; charset=UTF-8\",\"date\":\"Wed, 23 Apr 2025 15:48:45 GMT\",\"x-appsumo-signature\":\"****************************************************************\",\"x-appsumo-timestamp\":\"1745423324156\"}",
  "body": "{\"license_key\":\"00000000-aaaa-1111-bbbb-abcdef012345\",\"event\":\"activate\",\"event_timestamp\":1745423324156,\"created_at\":1745423324156,\"license_status\":\"active\",\"tier\":1,\"test\":true,\"extra\":{\"reason\":\"Activated by customer\"}}",
  "method": "POST",
  "url": "https://dev.uniscribe.co/webhook/appsumo",
  "event": "activate",
  "timestamp": **********
}
```

测试工具会生成类似的请求格式，并在控制台输出。

### 事件数据格式

测试工具生成的事件数据与 AppSumo 实际发送的事件数据一致，包括：

1. **Purchase 事件**
   ```json
   {
     "license_key": "3794577c-3dbc-11ec-9bbc-0242ac130002",
     "event": "purchase",
     "license_status": "inactive",
     "event_timestamp": 1318781876406,
     "created_at": **********,
     "test": false
   }
   ```

2. **Activate 事件**
   ```json
   {
     "license_key": "3794577c-3dbc-11ec-9bbc-0242ac130002",
     "event": "activate",
     "license_status": "inactive",
     "event_timestamp": 1318781876406,
     "created_at": **********,
     "tier": 1,
     "test": false,
     "extra": {
       "reason": "Purchased by the customer"
     }
   }
   ```

3. **Upgrade 事件**
   ```json
   {
     "license_key": "c86ad3d7-3942-4d11-8814-b0bd81971691",
     "prev_license_key": "3794577c-3dbc-11ec-9bbc-0242ac130002",
     "event": "upgrade",
     "event_timestamp": 1671586387628,
     "created_at": 1671586387624,
     "license_status": "inactive",
     "tier": 2,
     "test": false,
     "extra": {
       "reason": "Upgraded by the customer"
     }
   }
   ```

4. **Downgrade 事件**
   ```json
   {
     "license_key": "c8e57fa3-ea5b-4c39-a2bf-74f7f51d01b0",
     "prev_license_key": "c86ad3d7-3942-4d11-8814-b0bd81971691",
     "event": "downgrade",
     "event_timestamp": 1671586699435,
     "created_at": 1671586699431,
     "license_status": "inactive",
     "tier": 1,
     "test": false,
     "extra": {
       "reason": "Downgraded by the customer"
     }
   }
   ```

5. **Deactivate 事件**
   ```json
   {
     "license_key": "c8e57fa3-ea5b-4c39-a2bf-74f7f51d01b0",
     "event": "deactivate",
     "license_status": "active",
     "event_timestamp": 1671586699927,
     "created_at": 1671586699922,
     "test": false,
     "extra": {
       "reason": "Refunded by the user"
     }
   }
   ```

## 故障排除

### 签名验证问题

如果您遇到签名验证失败的问题，请检查以下几点：

1. **确保 API 密钥正确**：
   - 检查 `APPSUMO_API_KEY` 环境变量是否正确设置
   - 使用 `--api-key` 选项直接指定 API 密钥
   - 确保在 AppSumo 合作伙伴门户中使用的是正确的 API 密钥

2. **检查日志输出**：
   - 测试工具会输出签名计算的详细信息，包括 API 密钥、时间戳、消息和计算的签名
   - 服务器日志中也会输出签名验证的详细信息
   - 比较测试工具计算的签名和服务器计算的签名，确保它们一致

3. **确保消息格式正确**：
   - 消息格式应为 `timestamp + payload`
   - 确保没有额外的空格或换行符

4. **跳过签名验证**：
   - 在测试环境中，您可以设置 `APPSUMO_SKIP_SIGNATURE_VERIFICATION=true` 环境变量来跳过签名验证
   - 这在开发和测试过程中非常有用，但不建议在生产环境中使用

### 其他问题

1. 如果 webhook URL 无法访问，请检查 `APPSUMO_WEBHOOK_URL` 是否正确配置
2. 如果事件处理失败，请检查服务器日志以获取详细信息

### Docker 环境中的注意事项

在 Docker 环境中，容器内部的 `localhost` 指的是容器自身，而不是其他容器。因此，在 Docker 环境中使用测试工具时，需要使用服务名称而不是 `localhost`。

如果您在 Docker 容器内运行测试工具，并且需要访问另一个容器中的 Flask 应用，请使用服务名称（如 `web`）而不是 `localhost`：

```bash
# 在 Docker 容器内使用服务名称
 flask admin appsumo-test send-event --event=purchase --tier=1 --url=http://web:8000/webhook/appsumo --api-key=your_api_key_here
```

或者，您可以使用默认的 URL，因为我们已经将其设置为 `http://web:8000/webhook/appsumo`：

```bash
# 使用默认 URL 和 API 密钥
flask admin appsumo-test send-event --event=purchase --tier=1

# 指定 API 密钥
flask admin appsumo-test send-event --event=purchase --tier=1 --api-key=your_api_key_here
```
