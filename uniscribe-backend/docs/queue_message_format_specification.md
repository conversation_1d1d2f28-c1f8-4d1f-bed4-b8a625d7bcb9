# Redis Streams 队列消息格式规范

## 🎯 设计目标

1. **统一格式**: 所有任务类型使用统一的消息格式
2. **向后兼容**: 保持与现有 Go 消费者的兼容性
3. **扩展性**: 支持新任务类型的任意字段扩展
4. **简单性**: 所有字段都在外层，解析简单直接

## 🏗️ 消息格式设计

### 单层结构设计

```json
{
  // === 核心字段（必需，不可删减） ===
  "task_id": "7347436261524770816",
  "task_type": "10",
  "file_id": "7347436261206003712",
  "priority": "1",
  "created_time": "2025-07-06T01:28:34",

  // === 现有字段（兼容 Go 消费者） ===
  "file_url": "https://example.com/file.mp3",
  "transcription_file_id": "7347436261206003712",
  "transcription_text": "",
  "file_duration": "730",
  "language_code": "en",
  "language": "English",
  "transcription_type": "transcript",
  "requested_service_provider": "fal-ai/whisper",

  // === 扩展字段（新增，Go 消费者可忽略） ===
  "user_id": "7335662845926969344",
  "youtube_url": "https://www.youtube.com/watch?v=KakaBAr8vgA",
  "title": "Elon Musk: Nvidia's GPUs are 'better than what we make'",
  "enable_speaker_diarization": "false",
  "folder_id": "",
  "preprocessing_type": "youtube"
}
```

### 字段分类说明

#### 1. 核心字段（必需，不可删减）

- `task_id`: 任务ID
- `task_type`: 任务类型
- `file_id`: 文件ID
- `priority`: 优先级
- `created_time`: 创建时间

#### 2. 现有字段（兼容 Go 消费者）

- `file_url`: 文件URL
- `transcription_file_id`: 转录文件ID（别名）
- `transcription_text`: 转录文本
- `file_duration`: 文件时长
- `language_code`: 语言代码
- `language`: 语言名称
- `transcription_type`: 转录类型
- `requested_service_provider`: 请求的服务提供商

#### 3. 扩展字段（新增，Go 消费者可忽略）

- `user_id`: 用户ID
- `youtube_url`: YouTube URL
- `title`: 视频标题
- `enable_speaker_diarization`: 是否启用说话人识别
- `folder_id`: 文件夹ID
- `preprocessing_type`: 预处理类型

**注意**:

- 视频时长统一使用 `file_duration` 字段，不再使用单独的 `duration` 字段。
- `youtube_download_task_id` 字段已移除，因为YouTube任务现在集成了ffmpeg预处理，不再需要单独的下载任务。

## 🔄 消费者解析策略

### Go 服务（现有消费者）

```go
func (c *RedisStreamConsumer) parseMessage(message redis.XMessage) (handlers.Task, error) {
    var task handlers.Task

    // 继续使用现有的字段解析逻辑，完全不变
    if idStr, ok := message.Values["task_id"].(string); ok {
        if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
            task.TaskID = id
        }
    }

    if typeStr, ok := message.Values["task_type"].(string); ok {
        if taskType, err := strconv.Atoi(typeStr); err == nil {
            task.TaskType = taskType
        }
    }

    // 其他现有字段解析...
    // 新增字段会被自动忽略，不影响现有逻辑

    return task, nil
}
```

### Python 服务（新消费者）

```python
def _parse_task_data(self, fields):
    """简化消息解析：直接从外层字段读取"""

    # 直接从字段解析，所有值都是字符串，需要转换类型
    task_data = {
        # 核心字段
        'id': int(fields.get('task_id', 0)),
        'task_type': int(fields.get('task_type', 0)),
        'file_id': int(fields.get('file_id', 0)),
        'priority': int(fields.get('priority', 1)),
        'created_time': fields.get('created_time', ''),

        # 现有字段
        'file_url': fields.get('file_url', ''),
        'transcription_text': fields.get('transcription_text', ''),
        'file_duration': float(fields.get('file_duration', 0)),
        'language_code': fields.get('language_code', ''),
        'transcription_type': fields.get('transcription_type', ''),

        # 扩展字段（新增）
        'user_id': int(fields.get('user_id', 0)),
        'youtube_url': fields.get('youtube_url', ''),
        'title': fields.get('title', ''),
        'duration': int(fields.get('duration', 0)),
        'enable_speaker_diarization': fields.get('enable_speaker_diarization', 'false').lower() == 'true',
        # ... 其他字段
    }

    return task_data
```

## 📋 实施指南

### 1. 生产者（TaskQueueService）

- ✅ 已实现单层结构
- ✅ 所有字段都在外层，简单直接
- ✅ 去掉重复字段，统一使用 file_duration

### 2. 现有消费者（Go 服务）

- ✅ 无需修改，继续使用现有解析逻辑
- ✅ 完全向后兼容，新字段自动忽略
- ✅ 如需新字段，直接从外层读取即可

### 3. 新消费者（Python 媒体预处理）

- ✅ 已实现简化解析策略
- ✅ 直接从外层字段读取，无需复杂解析
- ✅ 支持任意扩展字段

## 🎯 最佳实践

### 1. 新任务类型开发

```python
# 创建任务时，确保包含所有必要字段
task_data = {
    'id': task_id,
    'task_type': TaskType.new_task.id,
    'file_id': file_id,
    'priority': priority,
    'created_time': datetime.now().isoformat(),

    # 任务特有字段
    'custom_field_1': value1,
    'custom_field_2': value2,
}

# 入队
enqueue_task('new_task_type', task_data)
```

### 2. 消费者开发

```python
def _process_new_task(self, task_id, fields):
    # 使用规范化解析
    task_data = self._parse_task_data(fields)

    # 提取字段
    custom_field_1 = task_data.get('custom_field_1')
    custom_field_2 = task_data.get('custom_field_2')

    # 处理逻辑...
```

## 🔍 监控和调试

### 1. 日志记录

- 记录字段解析过程
- 记录缺失字段的警告
- 记录类型转换错误

### 2. 兼容性检查

- 定期检查 Go 消费者是否正常工作
- 监控新字段的使用情况
- 确保字段类型转换正确

## 📈 实施历程

### 阶段 1: 单层结构设计 ✅

- 采用所有字段都在外层的简单设计
- 保持完全向后兼容

### 阶段 2: 去掉重复字段 ✅

- 统一使用 file_duration，去掉 duration
- 优化消息格式，减少冗余

### 阶段 3: 消费者优化 ✅

- Python 媒体预处理消费者直接读取外层字段
- 简化解析逻辑，提高性能

### 阶段 4: 文档和测试完善 ✅

- 更新规范文档
- 完善测试验证

## ✅ 总结

这个规范化方案实现了：

1. **统一性**: 所有任务类型使用相同的消息格式
2. **兼容性**: 现有 Go 消费者无需修改
3. **扩展性**: 新任务类型可以添加任意字段
4. **可维护性**: 消费者解析逻辑标准化
5. **渐进性**: 支持逐步迁移，降低风险

通过这个方案，我们解决了不同任务类型字段差异化的问题，同时保持了生产环境的稳定性。
