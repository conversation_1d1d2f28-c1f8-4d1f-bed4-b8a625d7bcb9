# 批量导出转录文件 API 文档

## 概述

批量导出功能允许用户一次性选择多个转录文件并打包导出，支持与单个文件导出相同的所有格式和参数。

## API 端点

### 批量导出转录文件

**POST** `/export/batch`

#### 请求头

```
Content-Type: application/json
Authorization: Bearer <token>
```

#### 请求参数

| 参数名          | 类型    | 必需 | 默认值 | 说明                                              |
| --------------- | ------- | ---- | ------ | ------------------------------------------------- |
| fileType        | string  | 是   | -      | 导出文件格式，支持: pdf, docx, txt, srt, vtt, csv |
| fileIds         | array   | 是   | -      | 要导出的转录文件 ID 数组（字符串格式）            |
| showSpeakerName | boolean | 否   | true   | 是否显示说话人姓名                                |
| showTimestamps  | boolean | 否   | true   | 是否显示时间戳                                    |

#### 请求示例

```json
{
  "fileType": "txt",
  "fileIds": ["123", "456", "789"],
  "showSpeakerName": true,
  "showTimestamps": true
}
```

#### 响应

##### 成功响应 (200)

**单个文件时：**

- 直接返回该文件的导出内容
- Content-Type: 根据文件类型设置
- Content-Disposition: attachment; filename="filename.ext"

**多个文件时：**

- 返回包含所有文件的 ZIP 压缩包
- Content-Type: application/zip
- Content-Disposition: attachment; filename="transcriptions*{count}files*{timestamp}.zip"
- 文件名格式：`transcriptions_3files_20241226_143052.zip`（3 个文件，2024 年 12 月 26 日 14:30:52 导出）

**响应头信息：**

- `X-Export-Total`: 请求导出的文件总数
- `X-Export-Successful`: 成功导出的文件数量
- `X-Export-Failed`: 导出失败的文件数量
- `X-Export-Has-Failures`: 是否有失败的文件（"true" 或不存在）
- `X-Export-Errors`: 失败文件的错误信息（JSON 格式，限制 1000 字符）

##### 错误响应

**400 Bad Request**

```json
{
  "message": "Invalid file type"
}
```

**400 Bad Request**

```json
{
  "message": "fileIds cannot be empty"
}
```

**400 Bad Request**

```json
{
  "message": "Cannot export more than 50 files at once"
}
```

**404 Not Found**

```json
{
  "message": "No exportable transcription files found"
}
```

**注意：部分失败处理**

- 如果部分文件无法导出，API 仍会返回 200 状态码
- 成功的文件会被包含在 ZIP 中
- 失败信息通过响应头传递
- 前端应检查 `X-Export-Has-Failures` 头来处理部分失败情况

**500 Internal Server Error**

```json
{
  "message": "An error occurred during batch file export"
}
```

## 功能特性

### 1. 智能文件处理

- 自动过滤无效的文件 ID
- 验证文件所有权
- 跳过没有转录结果的文件
- 处理不足分钟数的文件（添加升级提示）

### 2. 灵活的导出方式

- **单个文件**：直接返回文件内容
- **多个文件**：自动打包为 ZIP 文件

### 3. 健壮的错误处理

- **部分成功策略**：能导出的文件正常导出，无法导出的文件记录错误
- **详细错误信息**：为每个失败的文件提供具体的错误原因
- **透明的结果反馈**：通过响应头传递导出统计信息
- **优雅降级**：部分文件失败不影响其他文件的导出

### 4. 安全限制

- 最多支持 50 个文件的批量导出
- 严格的用户权限验证
- 文件所有权检查

### 5. 支持的文件格式

- **PDF**: 多语言支持，完美字体渲染
- **DOCX**: Microsoft Word 文档格式
- **TXT**: 纯文本格式
- **SRT**: 字幕文件格式
- **VTT**: WebVTT 字幕格式
- **CSV**: 表格格式，包含时间戳和说话人信息

## 使用场景

1. **批量备份**：用户可以选择多个重要的转录文件进行批量导出备份
2. **项目整理**：将同一项目的多个转录文件打包导出
3. **分享协作**：将相关的转录文件打包发送给团队成员
4. **格式转换**：批量将转录文件转换为特定格式

## 与单个导出的兼容性

批量导出 API 与现有的单个文件导出 API 完全兼容：

- 使用相同的导出参数
- 支持相同的文件格式
- 应用相同的权限检查
- 处理相同的特殊情况（如不足分钟数）

## 错误处理示例

### 响应头解析

```javascript
const handleBatchExportResponse = async (response) => {
  const total = parseInt(response.headers.get("X-Export-Total") || "0");
  const successful = parseInt(
    response.headers.get("X-Export-Successful") || "0"
  );
  const failed = parseInt(response.headers.get("X-Export-Failed") || "0");
  const hasFailures = response.headers.get("X-Export-Has-Failures") === "true";

  if (hasFailures) {
    const errors = JSON.parse(response.headers.get("X-Export-Errors") || "[]");
    showPartialSuccessMessage(successful, failed, errors);
  } else {
    showSuccessMessage(successful);
  }

  // 下载文件
  const blob = await response.blob();
  downloadFile(blob, getFilenameFromHeaders(response.headers));
};
```

### 用户提示处理

```javascript
const showPartialSuccessMessage = (successful, failed, errors) => {
  const message = `
    导出完成：
    ✅ 成功：${successful} 个文件
    ❌ 失败：${failed} 个文件

    失败原因：
    ${errors.map((err) => `• 文件 ${err.fileId}: ${err.error}`).join("\n")}
  `;

  showNotification(message, "warning");
};
```

### 常见错误类型

| 错误信息                                        | 原因               | 解决方案                     |
| ----------------------------------------------- | ------------------ | ---------------------------- |
| `Invalid file ID format`                        | 文件 ID 格式不正确 | 检查传入的 ID 是否为有效数字 |
| `Transcription file not found or access denied` | 文件不存在或无权限 | 确认文件存在且用户有访问权限 |
| `Task result not found`                         | 转录结果缺失       | 等待转录完成或重新转录       |
| `Export failed during ZIP creation`             | 导出过程异常       | 检查文件内容或重试           |

## 前端集成建议

1. **UI 设计**：在文件列表中添加批量选择功能
2. **进度提示**：对于大量文件的导出，显示处理进度
3. **错误处理**：优雅处理部分文件导出失败的情况
4. **文件预览**：允许用户在导出前预览选中的文件列表
5. **结果反馈**：根据响应头显示详细的导出结果统计

## 性能考虑

- 限制单次导出文件数量（最多 50 个）
- ZIP 压缩减少传输大小
- 内存优化的流式处理
- 适当的超时设置
- 批量查询优化（减少数据库查询次数）
- 错误处理不影响整体性能
- 部分失败时仍能快速返回成功的文件
