# 手动触发转录任务

本文档介绍如何使用命令行工具手动触发转录任务。

## 背景

在某些情况下，您可能需要手动触发转录任务，例如：

- 当自动触发的任务失败时
- 当您需要重新处理某个文件时
- 当您需要测试转录功能时

## 使用方法

使用 Flask CLI 命令：

```bash
# 在项目根目录下执行
flask admin trigger-transcription <transcription_file_id>
```

示例：

```bash
flask admin trigger-transcription 123456
```

## 输出说明

命令执行后，将输出以下信息：

- 转录文件 ID 和用户 ID
- 创建的任务 ID 和状态
- 任务详情，包括文件 ID、任务类型和请求的服务提供商
- 如果有错误，将显示错误信息

## 注意事项

- 该命令只会创建转录任务，不会创建其他类型的任务（如摘要、大纲等）
- 如果文件不存在或用户无权访问该文件，命令将失败
- 如果用户没有足够的转录配额，任务将创建但状态为失败
