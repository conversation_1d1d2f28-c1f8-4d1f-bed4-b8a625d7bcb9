# API Key Management API Documentation

## Overview

This document describes the API endpoints for managing API keys in the UniScribe system. These endpoints allow users to create, view, update, and delete their API keys for accessing the UniScribe OpenAPI.

## Access Requirements

- API key management requires an active subscription or LTD plan
- Free users and one-time purchase users cannot access these endpoints
- Returns `403 Forbidden` if user doesn't have API access

## Endpoints

### 1. List API Keys

**Endpoint**: `GET /api-keys`

**Description**: Retrieve all API keys for the authenticated user

**Request**:

```http
GET /api-keys
Authorization: Bearer <token>
```

**Response**:

```json
{
  "success": true,
  "data": {
    "apiKeys": [
      {
        "id": "ak_1234567890",
        "name": "Production API Key",
        "keyPreview": "us_****************************abcd",
        "isActive": true,
        "createdTime": "2024-01-15T10:30:00Z",
        "lastUsedTime": "2024-01-16T14:22:00Z",
        "expiresAt": null,
        "rateLimitPerMinute": 10,
        "rateLimitPerDay": 1000
      },
      {
        "id": "ak_0987654321",
        "name": "Development Key",
        "keyPreview": "us_****************************xyz9",
        "isActive": true,
        "createdTime": "2024-01-10T09:15:00Z",
        "lastUsedTime": null,
        "expiresAt": "2024-07-10T09:15:00Z",
        "rateLimitPerMinute": 10,
        "rateLimitPerDay": 1000
      }
    ]
  },
  "message": "Success",
  "timestamp": "2024-01-16T15:00:00Z"
}
```

**Response Fields**:

- `id`: Unique API key identifier
- `name`: User-friendly name for the key
- `keyPreview`: Masked API key showing only first 3 and last 4 characters
- `isActive`: Whether the key is active
- `createdTime`: When the key was created (ISO 8601)
- `lastUsedTime`: When the key was last used (ISO 8601, null if never used)
- `expiresAt`: Expiration date (ISO 8601, null if no expiration)
- `rateLimitPerMinute`: Requests per minute limit
- `rateLimitPerDay`: Requests per day limit

### 2. Create API Key

**Endpoint**: `POST /api-keys`

**Description**: Create a new API key

**Request**:

```http
POST /api-keys
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "My New API Key",
  "expiresDays": 365
}
```

**Request Parameters**:

- `name` (string, required): User-friendly name for the API key (1-100 characters)
- `expiresDays` (integer, optional): Number of days until expiration (1-3650, null for no expiration)

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "ak_1234567890",
    "name": "My New API Key",
    "apiKey": "us_1234567890abcdef1234567890abcdef1234567890abcdef",
    "keyPreview": "us_****************************cdef",
    "isActive": true,
    "createdTime": "2024-01-16T15:00:00Z",
    "lastUsedTime": null,
    "expiresAt": "2025-01-16T15:00:00Z",
    "rateLimitPerMinute": 10,
    "rateLimitPerDay": 1000
  },
  "message": "API key created successfully",
  "timestamp": "2024-01-16T15:00:00Z"
}
```

**Important Notes**:

- The full `apiKey` is only returned once during creation
- Store the API key securely - it cannot be retrieved again
- If you lose the API key, you must reset it to get a new one
- Maximum 5 active API keys per user

### 3. Update API Key

**Endpoint**: `PUT /api-keys/{id}`

**Description**: Update an existing API key (name only)

**Request**:

```http
PUT /api-keys/ak_1234567890
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Updated API Key Name"
}
```

**Request Parameters**:

- `name` (string, required): New name for the API key (1-100 characters)

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "ak_1234567890",
    "name": "Updated API Key Name",
    "keyPreview": "us_****************************abcd",
    "isActive": true,
    "createdTime": "2024-01-15T10:30:00Z",
    "lastUsedTime": "2024-01-16T14:22:00Z",
    "expiresAt": null,
    "rateLimitPerMinute": 10,
    "rateLimitPerDay": 1000
  },
  "message": "API key updated successfully",
  "timestamp": "2024-01-16T15:00:00Z"
}
```

### 4. Reset API Key

**Endpoint**: `POST /api-keys/{id}/reset`

**Description**: Reset an existing API key (generates new key value)

**Request**:

```http
POST /api-keys/ak_1234567890/reset
Authorization: Bearer <token>
```

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "ak_1234567890",
    "name": "My API Key",
    "apiKey": "us_newkey567890abcdef1234567890abcdef1234567890ab",
    "keyPreview": "us_****************************90ab",
    "isActive": true,
    "createdTime": "2024-01-15T10:30:00Z",
    "lastUsedTime": null,
    "expiresAt": null,
    "rateLimitPerMinute": 10,
    "rateLimitPerDay": 1000
  },
  "message": "API key reset successfully",
  "timestamp": "2024-01-16T15:00:00Z"
}
```

**Important Notes**:

- The full `apiKey` is only shown once during reset
- Old API key becomes invalid immediately
- All existing integrations using the old key will fail
- `lastUsedTime` is reset to null

### 5. Delete API Key

**Endpoint**: `DELETE /api-keys/{id}`

**Description**: Deactivate an API key (soft delete)

**Request**:

```http
DELETE /api-keys/ak_1234567890
Authorization: Bearer <token>
```

**Response**:

```json
{
  "success": true,
  "message": "API key deleted successfully",
  "timestamp": "2024-01-16T15:00:00Z"
}
```

**Notes**:

- API keys are soft-deleted (marked as inactive)
- Deleted keys cannot be reactivated
- All existing API requests using the deleted key will fail immediately

## Error Responses

### Common Error Format

```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": 40001,
    "details": "Additional error details (optional)"
  },
  "timestamp": "2024-01-16T15:00:00Z"
}
```

### Error Codes

| HTTP Status | Error Code | Description                                           |
| ----------- | ---------- | ----------------------------------------------------- |
| 401         | 40101      | Authentication required                               |
| 403         | 40301      | API access denied (requires subscription or LTD plan) |
| 404         | 40401      | API key not found                                     |
| 400         | 40001      | Invalid request parameters                            |
| 400         | 40002      | API key name too long (max 100 characters)            |
| 400         | 40003      | Invalid expiresDays (must be 1-3650)                  |
| 409         | 40901      | Maximum API keys limit reached (5 keys)               |
| 409         | 40902      | API key name already exists                           |
| 500         | 50001      | Internal server error                                 |

### Example Error Responses

**API Access Denied**:

```json
{
  "success": false,
  "error": {
    "message": "API access requires active subscription or LTD plan",
    "code": 40301
  },
  "timestamp": "2024-01-16T15:00:00Z"
}
```

**Maximum Keys Limit**:

```json
{
  "success": false,
  "error": {
    "message": "Maximum number of API keys reached (5)",
    "code": 40901,
    "details": "Delete an existing API key before creating a new one"
  },
  "timestamp": "2024-01-16T15:00:00Z"
}
```

**API Key Not Found**:

```json
{
  "success": false,
  "error": {
    "message": "API key not found",
    "code": 40401
  },
  "timestamp": "2024-01-16T15:00:00Z"
}
```

## Rate Limiting

- All API key management endpoints are subject to standard user rate limits
- No additional rate limiting specific to API key management

## Security Considerations

1. **Key Storage**: API keys are stored as SHA256 hashes in the database
2. **Key Display**: Full keys are only shown once during creation and reset
3. **No Key Retrieval**: Lost keys cannot be retrieved, only reset with new values
4. **Access Control**: Only the key owner can manage their keys
5. **Audit Trail**: All key operations are logged for security monitoring
6. **Expiration**: Keys can have optional expiration dates for enhanced security
7. **Immediate Invalidation**: Reset or deleted keys become invalid immediately

## Frontend Integration Notes

### Key Management UI Flow

1. **List View**: Display all keys with masked values and metadata
2. **Create Flow**:
   - Show creation form with name and optional expiration
   - Display full key once with copy functionality
   - Show warning about key storage and inability to retrieve later
3. **Update Flow**: Allow renaming keys
4. **Reset Flow**: Show confirmation dialog and display new key once
5. **Delete Flow**: Show confirmation dialog before deletion

### Recommended UI Components

- **API Key Card**: Display key info with actions (rename, delete)
- **Create Key Modal**: Form for new key creation
- **Key Display Modal**: Show full key once during creation
- **Confirmation Dialog**: For key deletion
- **Copy Button**: For easy key copying

### State Management

- Cache API keys list after fetching
- Update local state after CRUD operations
- Handle loading and error states appropriately
- Clear sensitive data from memory after use

## Testing

### Test Scenarios

1. **Authentication**: Test with valid/invalid tokens
2. **Authorization**: Test with different user plan types
3. **CRUD Operations**: Test all endpoints with valid/invalid data
4. **Limits**: Test maximum keys limit (5 keys)
5. **Validation**: Test input validation for all fields
6. **Error Handling**: Test all error scenarios

### Sample Test Data

```javascript
// Valid API key creation
{
  "name": "Test API Key",
  "expiresDays": 30
}

// Invalid requests
{
  "name": "", // Empty name
  "expiresDays": -1 // Invalid expiration
}
```

This API follows RESTful conventions and integrates seamlessly with the existing UniScribe authentication and authorization system.
