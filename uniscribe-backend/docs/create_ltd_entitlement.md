# Create LTD Entitlement CLI Command

## Overview

The `create-ltd-entitlement` command allows administrators to create LTD (Lifetime Deal) entitlements for specified users. This command is designed for granting LTD benefits or handling private LTD purchases outside of the normal AppSumo flow.

## Usage

```bash
flask admin create-ltd-entitlement --user-id=<USER_ID> --tier=<TIER> [--dry-run]
```

### Parameters

- `--user-id` (required): The ID of the user to create the LTD entitlement for
- `--tier` (required): The LTD tier to grant. Options:
  - `1`: Tier 1 (1200 credits/month)
  - `2`: Tier 2 (6000 credits/month)
- `--dry-run` (optional): Show what would happen without making any changes

### Examples

```bash
# Create Tier 1 LTD entitlement for user 123456
flask admin create-ltd-entitlement --user-id=123456 --tier=1

# Create Tier 2 LTD entitlement for user 789012
flask admin create-ltd-entitlement --user-id=789012 --tier=2

# Dry run to see what would happen
flask admin create-ltd-entitlement --user-id=123456 --tier=1 --dry-run
```

## What the Command Does

1. **Validates the user exists** in the database
2. **Finds the appropriate LTD plan** based on the specified tier
3. **Checks for existing LTD entitlements** and warns if any exist
4. **Shows a summary** of what will be created
5. **Asks for confirmation** (y/N) before proceeding
6. **Creates database records** in a transaction:
   - Creates a `Purchase` record with a generated `stripe_payment_id`
   - Creates an `Entitlement` record using `EntitlementService.create_from_ltd_purchase`

## Generated Data

### Purchase Record
- **ID**: Generated using `id_generator.get_id()`
- **user_id**: The specified user ID
- **plan_id**: The ID of the LTD plan for the specified tier
- **quantity**: 1
- **stripe_payment_id**: Generated in format `admin_ltd_gift_{timestamp}_{user_id}`

### Entitlement Record
- **ID**: Generated using `id_generator.get_id()`
- **user_id**: The specified user ID
- **total_credits**: Based on the plan (1200 for Tier 1, 6000 for Tier 2)
- **consumed_credits**: 0
- **valid_from**: Current timestamp
- **valid_until**: One month from current timestamp
- **source_type**: `EntitlementSource.LTD`
- **source_id**: The Purchase record ID
- **is_recurring**: `True` (LTD entitlements auto-renew monthly)

## LTD Plan Details

### Tier 1
- **Credits**: 1200 per month
- **Plan Name**: appsumo_tier_1
- **Tier**: Tier 1
- **Description**: AppSumo Lifetime Deal - Tier 1 (1200 minutes/month)

### Tier 2
- **Credits**: 6000 per month
- **Plan Name**: appsumo_tier_2
- **Tier**: Tier 2
- **Description**: AppSumo Lifetime Deal - Tier 2 (6000 minutes/month)

## Safety Features

1. **User Validation**: Ensures the user exists before proceeding
2. **Plan Validation**: Ensures the LTD plan exists and is active
3. **Existing Entitlement Warning**: Shows if the user already has LTD entitlements
4. **Confirmation Prompt**: Requires explicit confirmation (y/N) before creating records
5. **Dry Run Mode**: Allows testing without making changes
6. **Database Transaction**: All database operations are wrapped in a transaction
7. **Comprehensive Logging**: All operations are logged for audit purposes

## Error Handling

The command handles various error scenarios:
- User not found
- LTD plan not found for specified tier
- Database transaction failures
- Invalid input parameters

## Output Example

```
📋 User Info:
   ID: 123456
   Email: <EMAIL>
   Name: John Doe
   Current Plan: Free

📦 LTD Plan Info:
   ID: 9
   Name: appsumo_tier_1
   Tier: Tier 1
   Credits: 1200 per month
   Description: AppSumo Lifetime Deal - Tier 1 (1200 minutes/month)

💳 Purchase Info:
   Stripe Payment ID: admin_ltd_gift_1703123456_123456
   Plan ID: 9
   Quantity: 1

📋 Summary:
   User: <EMAIL> (ID: 123456)
   LTD Tier: 1 (1200 credits/month)
   Plan: appsumo_tier_1
   Is AppSumo: False

⚠️  This will create a new LTD entitlement for the user.
Do you want to proceed? [y/N]: y

✅ Purchase created: ID=987654321
✅ Entitlement created: ID=876543210
   Credits: 1200
   Valid until: 2024-01-15 10:30:45
   Is recurring: True

✅ Successfully created LTD entitlement <NAME_EMAIL>
```

## Related Files

- **Command Implementation**: `cli/create_ltd_entitlement.py`
- **Command Registration**: `cli/commands.py`
- **Models Used**:
  - `models/user.py`
  - `models/plan.py`
  - `models/purchase.py`
  - `models/entitlement.py`
- **Services Used**:
  - `services/entitlement_service.py`

## Prerequisites

- LTD plans must exist in the database (created via `add-ltd-plan-type` migration)
- Redis must be running (required for Flask app initialization)
- Database must be accessible
- User must have admin privileges to run the command
