# 批量导出 CORS 问题修复文档

## 问题描述

在线上环境下，当用户选择多个文件并进行批量导出后，前端显示"0 个文件被成功导出"的 toast 提示，但实际上所有文件都导出成功了。同时发现在不同浏览器中，有的可以看到响应头的值，有的看不到。而在开发环境下没有这个问题。

**补充问题**：当使用批量导出接口导出单个文件时，缺少统计响应头（X-Export-Total 等），导致前端无法正确显示导出结果。

## 根本原因

这个问题有两个方面：

### 1. CORS 配置不完整

**CORS（跨域资源共享）配置不完整**导致的：

1. **后端设置了自定义响应头**：

   - `X-Export-Total`: 请求导出的文件总数
   - `X-Export-Successful`: 成功导出的文件数量
   - `X-Export-Failed`: 导出失败的文件数量
   - `X-Export-Has-Failures`: 是否有失败的文件
   - `X-Export-Errors`: 失败文件的错误信息

2. **前端无法读取这些响应头**：

   - 在开发环境下，CORS 限制较松，所以能正常读取
   - 在线上环境下，由于 CORS 配置问题，前端 JavaScript 无法访问这些自定义响应头
   - 不同浏览器对 CORS 的处理可能略有不同

3. **原有 CORS 配置缺失**：
   - 全局 CORS 配置没有设置 `expose_headers`
   - 响应级别手动设置导致重复

### 2. 单文件批量导出缺少统计响应头

当使用批量导出接口导出单个文件时，代码直接返回单个文件响应，但没有添加批量导出的统计响应头，导致前端无法获取导出统计信息。

## 解决方案

### 1. 修改全局 CORS 配置

在 `config_modules/cors.py` 中添加 `expose_headers` 配置：

```python
def setup_cors(app):
    """设置 CORS 配置"""
    # 定义需要暴露给前端的响应头
    exposed_headers = [
        "Content-Disposition",
        "X-Export-Total",
        "X-Export-Successful",
        "X-Export-Failed",
        "X-Export-Has-Failures",
        "X-Export-Errors"
    ]

    if is_development_env():
        CORS(
            app,
            supports_credentials=True,
            allow_headers="*",
            origins="*",
            expose_headers=exposed_headers
        )
    else:
        CORS(
            app,
            origins=[...],
            supports_credentials=True,
            expose_headers=exposed_headers,
        )
```

### 2. 移除重复的响应头设置

移除 `controllers/export.py` 中 `create_download_response` 函数的手动 `Access-Control-Expose-Headers` 设置，避免与全局 CORS 配置重复：

```python
# 移除这行代码，避免重复
# response.headers["Access-Control-Expose-Headers"] = "..."

# 只保留Content-Disposition设置
response.headers["Content-Disposition"] = (
    f"attachment; filename*=UTF-8''{filename_encoded}"
)
```

### 3. 修复单文件批量导出的统计响应头

在 `export_batch_text` 函数中，确保单个文件的批量导出也包含统计响应头：

```python
# 如果只有一个文件，直接返回单个文件（但仍需要添加批量导出的统计响应头）
if len(valid_files) == 1:
    transcription_file, task_result = valid_files[0]
    filename = f"{transcription_file.filename}.{file_type}"

    # 处理不足分钟数的情况
    segments = process_insufficient_minutes(transcription_file, task_result.segments)

    file_content = exporter.export(segments, show_speaker_name, show_timestamps)
    response = create_download_response(file_content, filename)

    # 添加批量导出统计信息到响应头（即使只有一个文件）
    response.headers["X-Export-Total"] = str(len(file_ids))
    response.headers["X-Export-Successful"] = str(export_result["successful"])
    response.headers["X-Export-Failed"] = str(export_result["failed"])

    # 如果有失败的文件，添加到响应头
    if export_result["failed"] > 0:
        response.headers["X-Export-Has-Failures"] = "true"
        import json
        error_summary = [{"fileId": err["fileId"], "error": err["error"]} for err in export_result["errors"]]
        response.headers["X-Export-Errors"] = json.dumps(error_summary)[:1000]

    return response
```

## 技术细节

### CORS 响应头暴露机制

默认情况下，浏览器只允许前端 JavaScript 访问以下响应头：

- Cache-Control
- Content-Language
- Content-Type
- Expires
- Last-Modified
- Pragma

要访问自定义响应头，必须通过 `Access-Control-Expose-Headers` 明确声明。

### 统一 CORS 配置策略

我们采用了**统一的全局 CORS 配置策略**：

1. **全局 CORS 配置**：在 Flask-CORS 级别统一设置 `expose_headers`
2. **移除响应级别设置**：避免与全局配置冲突和重复

这种方式的优势：

- ✅ 避免响应头重复
- ✅ 集中管理 CORS 配置
- ✅ 确保所有端点一致性
- ✅ 符合 Flask-CORS 最佳实践

## 验证方法

### 1. 开发环境测试

```bash
cd uniscribe-backend
python -m pytest tests/test_batch_export_cors.py -v
```

### 2. 浏览器开发者工具检查

1. 打开浏览器开发者工具
2. 进行批量导出操作
3. 在 Network 标签页中查看响应头
4. 确认能看到所有自定义响应头

### 3. 前端 JavaScript 验证

```javascript
// 在批量导出的响应处理中添加调试代码
const handleBatchExportResponse = async (response) => {
  console.log("Available headers:", [...response.headers.keys()]);

  const total = response.headers.get("X-Export-Total");
  const successful = response.headers.get("X-Export-Successful");

  console.log("Total:", total, "Successful:", successful);

  // 如果能正确读取到值，说明CORS配置生效
};
```

## 部署注意事项

1. **重启应用**：修改 CORS 配置后需要重启 Flask 应用
2. **清除缓存**：建议清除浏览器缓存和 CDN 缓存
3. **监控日志**：部署后监控应用日志，确认没有 CORS 相关错误

## 相关文件

- `config_modules/cors.py` - 全局 CORS 配置
- `controllers/export.py` - 导出功能和响应头设置
- `tests/test_batch_export_cors.py` - CORS 配置测试
- `docs/batch_export_api.md` - 批量导出 API 文档

## 部署检查清单

在部署到线上环境之前，请确认以下事项：

### 1. 代码修改确认

- [ ] `config_modules/cors.py` 已添加 `expose_headers` 配置
- [ ] `controllers/export.py` 已更新 `Access-Control-Expose-Headers`
- [ ] 开发环境和生产环境都包含相同的配置

### 2. 测试验证

- [ ] 运行 `python3 tests/test_batch_export_cors.py` 通过
- [ ] 在开发环境测试批量导出功能正常
- [ ] 浏览器开发者工具能看到所有响应头

### 3. 部署步骤

1. 提交代码到版本控制系统
2. 部署到测试环境进行验证
3. 部署到生产环境
4. 重启 Flask 应用服务
5. 清除 CDN 和浏览器缓存

### 4. 部署后验证

- [ ] 在生产环境测试批量导出功能
- [ ] 确认 toast 提示显示正确的文件数量
- [ ] 检查不同浏览器的兼容性
- [ ] 监控应用日志，确认无 CORS 错误

## 预期效果

修复后，用户在线上环境进行批量导出时：

1. 前端能正确读取响应头中的导出统计信息
2. Toast 提示显示正确的成功导出文件数量
3. 所有浏览器都能一致地显示响应头信息
4. 开发环境和生产环境行为保持一致

## 故障排除

如果部署后仍有问题，请检查：

1. **应用是否重启**：CORS 配置修改需要重启 Flask 应用
2. **CDN 缓存**：清除 CDN 缓存，确保新的响应头配置生效
3. **浏览器缓存**：清除浏览器缓存和硬刷新页面
4. **网络代理**：检查是否有代理服务器过滤了响应头
5. **日志检查**：查看应用日志是否有 CORS 相关错误信息
