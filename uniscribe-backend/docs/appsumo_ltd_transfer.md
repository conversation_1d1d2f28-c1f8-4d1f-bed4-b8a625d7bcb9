# AppSumo LTD 权益转移命令

## 概述

`transfer-appsumo-ltd` 命令用于将一个用户的 AppSumo LTD 权益转移到另一个用户。这通常用于处理用户激活错账号的情况。

## 功能

该命令会执行以下操作：

1. **数据转移**：将以下数据从源用户转移到目标用户
   - `appsumo_license` 表中的记录
   - `appsumo_oauth_token` 表中的记录
   - `purchase` 表中的 AppSumo 相关记录
   - `entitlement` 表中的 LTD 权益记录

2. **恢复免费权益**：为源用户创建新的免费权益
   - 有效期：30天（从创建时间开始）
   - 自动续期：是（`is_recurring=1`）

## 使用方法

### 基本语法

```bash
flask admin transfer-appsumo-ltd --from-user-id=<源用户ID> --to-user-id=<目标用户ID> [选项]
```

### 参数说明

- `--from-user-id`：源用户ID（必需）- 当前拥有 AppSumo LTD 权益的用户
- `--to-user-id`：目标用户ID（必需）- 将要接收 AppSumo LTD 权益的用户
- `--dry-run`：预览模式，只显示将要执行的操作，不实际修改数据
- `--force`：跳过确认提示，直接执行

### 使用示例

#### 1. 预览模式（推荐先执行）

```bash
flask admin transfer-appsumo-ltd --from-user-id=123456 --to-user-id=789012 --dry-run
```

这会显示：
- 两个用户的当前状态
- 将要执行的操作列表
- 不会实际修改任何数据

#### 2. 正式执行

```bash
flask admin transfer-appsumo-ltd --from-user-id=123456 --to-user-id=789012
```

这会：
- 显示两个用户的当前状态
- 显示将要执行的操作
- 要求确认后执行转移
- 显示转移后的状态

#### 3. 强制执行（跳过确认）

```bash
flask admin transfer-appsumo-ltd --from-user-id=123456 --to-user-id=789012 --force
```

## 安全检查

命令执行前会进行以下检查：

1. **用户存在性检查**：验证源用户和目标用户都存在
2. **数据完整性检查**：确认源用户确实有 AppSumo 相关数据
3. **冲突检查**：如果目标用户已有 AppSumo 数据，会发出警告
4. **确认机制**：除非使用 `--force`，否则会要求用户确认

## 输出信息

命令会显示详细的用户状态信息，包括：

- 用户基本信息（ID、邮箱、姓名、注册时间）
- AppSumo License 信息
- AppSumo OAuth Token 信息
- AppSumo Purchase 记录
- 当前有效权益

## 注意事项

1. **数据库事务**：所有操作都在数据库事务中执行，确保数据一致性
2. **日志记录**：所有操作都会记录详细日志
3. **错误处理**：如果任何步骤失败，整个事务会回滚
4. **权限要求**：需要管理员权限才能执行此命令

## 故障排除

### 常见错误

1. **用户不存在**
   ```
   ❌ 源 用户 123456 不存在
   ```
   解决方案：检查用户ID是否正确

2. **源用户无 AppSumo 数据**
   ```
   ❌ 源用户 123456 没有 AppSumo 相关数据
   ```
   解决方案：确认该用户确实有 AppSumo LTD 权益

3. **目标用户已有数据冲突**
   ```
   ⚠️  目标用户 789012 已有 AppSumo 相关数据，可能会产生冲突
   ```
   解决方案：检查目标用户状态，确认是否继续

### 日志查看

命令执行时会输出详细日志，可以通过以下方式查看：

```bash
# 查看应用日志
tail -f logs/app.log

# 或者直接在命令输出中查看
```

## 相关文件

- 命令实现：`scripts/transfer_appsumo_ltd.py`
- 命令注册：`cli/commands.py`
- 相关模型：
  - `models/appsumo_license.py`
  - `models/appsumo_oauth_token.py`
  - `models/purchase.py`
  - `models/entitlement.py`
