# 批量删除转录文件功能实现文档

## 功能概述

实现了批量删除转录文件的功能，用户可以在前端一次性选中多个转录文件进行删除，提高操作效率。

## 实现的功能

1. **批量删除**：支持一次删除最多100个转录文件
2. **权限验证**：严格验证每个文件的所有权和删除权限
3. **状态检查**：检查文件状态是否允许删除
4. **详细反馈**：返回删除结果统计和错误详情
5. **部分成功处理**：支持部分文件删除成功的场景
6. **事务性操作**：确保数据一致性

## 新增的文件和修改

### 1. `resources/transcription.py`
新增了 `BatchDeleteTranscriptionResource` 类：

```python
class BatchDeleteTranscriptionResource(Resource):
    """批量删除转录文件资源"""
    
    @auth_required
    def delete(self):
        # 批量删除逻辑
        pass
```

### 2. `routes.py`
注册了新的批量删除路由：

```python
# 批量删除转录文件
api.add_resource(
    BatchDeleteTranscriptionResource,
    "/transcriptions/batch-delete",
    endpoint="batch_delete_transcription",
    methods=["DELETE"],
)
```

## API 使用说明

### 批量删除 API

**端点**: `DELETE /transcriptions/batch-delete`

**请求参数**:
```json
{
  "transcriptionIds": ["7340683505690612000", "7340716745725972000", "123"]
}
```

> **重要说明**：由于转录记录ID是64位大整数，超过了JavaScript的安全整数范围，建议前端使用字符串格式传输ID，后端会自动处理字符串到整数的转换。

**成功响应** (200):
```json
{
  "total": 3,
  "deleted": 2,
  "failed": 1,
  "errors": [
    {
      "transcriptionId": "7340683505690612000",
      "error": "Transcription not found"
    }
  ]
}
```

**错误响应** (400):
```json
{
  "message": "transcriptionIds cannot be empty"
}
```

## 参数说明

- `transcriptionIds` (必需):
  - 类型: `array<string|number>`
  - 说明: 要删除的转录记录ID列表
  - 限制: 最多100个转录记录ID
  - 验证: 所有ID必须是有效的整数（支持字符串格式的大整数）
  - 推荐: 使用字符串格式避免JavaScript大整数精度丢失问题

## 响应字段说明

- `total`: 总共尝试删除的转录记录数量
- `deleted`: 成功删除的转录记录数量
- `failed`: 删除失败的转录记录数量
- `errors`: 失败转录记录的详细错误信息
  - `transcriptionId`: 失败的转录记录ID（字符串格式，避免大整数精度问题）
  - `error`: 失败原因

## 错误处理

### 400 Bad Request
- 空的文件ID列表
- 超过100个文件的限制
- 包含无效的文件ID
- 缺少必需的参数

### 403 Forbidden
- 文件不属于当前用户
- 文件状态不允许删除

### 404 Not Found
- 文件不存在

### 500 Internal Server Error
- 数据库提交失败

## 安全特性

1. **权限验证**: 严格验证每个文件的所有权
2. **状态检查**: 只允许删除特定状态的文件
3. **批量限制**: 限制单次操作的文件数量
4. **详细日志**: 记录所有删除操作和错误

## 允许删除的文件状态

- `uploading`: 上传中
- `uploaded`: 上传完成
- `completed`: 处理完成
- `failed`: 处理失败
- `completed_with_errors`: 完成但有错误

## 前端集成建议

### 1. 用户界面
```javascript
// 批量选择组件
const [selectedFiles, setSelectedFiles] = useState([]);

// 全选/取消全选
const handleSelectAll = (checked) => {
  if (checked) {
    setSelectedFiles(files.map(f => f.id));
  } else {
    setSelectedFiles([]);
  }
};

// 批量删除按钮
<button 
  disabled={selectedFiles.length === 0}
  onClick={handleBatchDelete}
>
  删除选中的 {selectedFiles.length} 个文件
</button>
```

### 2. API调用
```javascript
const batchDeleteFiles = async (fileIds) => {
  try {
    const response = await fetch('/transcriptions/batch-delete', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ fileIds })
    });
    
    const result = await response.json();
    
    if (response.ok) {
      // 处理成功结果
      showSuccess(`成功删除 ${result.deleted} 个文件`);
      
      if (result.failed > 0) {
        showWarning(`${result.failed} 个文件删除失败`);
      }
      
      // 刷新文件列表
      refreshFileList();
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError('删除失败，请重试');
  }
};
```

### 3. 用户体验优化
- 显示删除进度
- 确认对话框
- 批量操作结果提示
- 支持撤销操作（如果需要）

## 性能考虑

1. **批量查询**: 使用 `IN` 查询一次获取所有文件
2. **事务处理**: 在单个事务中处理所有删除操作
3. **内存优化**: 限制批量操作的数量
4. **错误处理**: 快速失败，避免不必要的处理

## 测试建议

1. **功能测试**: 验证正常的批量删除流程
2. **权限测试**: 测试删除其他用户文件的情况
3. **边界测试**: 测试空列表、超大列表等边界情况
4. **错误测试**: 测试各种错误场景的处理
5. **性能测试**: 测试大批量删除的性能

## 后续优化建议

1. **异步处理**: 对于大批量删除，可以考虑异步处理
2. **软删除恢复**: 提供恢复已删除文件的功能
3. **删除日志**: 记录详细的删除操作日志
4. **批量操作扩展**: 支持其他批量操作（如移动、复制等）
