# 双连接池设计文档

## 背景

我们发现在处理 YouTube 转录和常规 S3/R2 操作时存在资源竞争问题。YouTube 转录需要上传大文件到 R2 存储，这些操作需要较长的超时时间和更多的重试次数。而常规操作（如 `head_object` 请求）则需要更快的响应时间和更少的重试。

当这两种类型的操作共享同一个连接池时，可能导致以下问题：
1. 连接池资源竞争，导致常规操作等待
2. 超时设置不合理，对某些操作过长，对另一些操作过短
3. 重试策略不适合所有类型的操作

## 解决方案

我们实现了双连接池设计，为不同类型的操作提供专用的连接池和配置：

1. **常规操作连接池**：
   - 适用于轻量级操作，如 `head_object`、`generate_presigned_url` 等
   - 较短的超时时间，更少的重试次数
   - 更大的连接池大小，以处理高并发请求

2. **上传专用连接池**：
   - 适用于大文件上传，如 YouTube 转录文件
   - 较长的超时时间，更多的重试次数
   - 较小的连接池大小，因为这类操作通常并发量较低

## 实现细节

### S3Storage 类

在 `S3Storage` 类中，我们创建了两个不同的 boto3 客户端，每个客户端有自己的连接池和配置：

```python
# 常规操作的 botocore 配置
self.regular_boto_config = Config(
    signature_version="s3v4",
    retries={
        "total_max_attempts": 3,  # 总共尝试3次（1次初始 + 2次重试）
        "mode": "standard",  # 使用标准重试模式
    },
    connect_timeout=5,  # 连接超时5秒
    read_timeout=10,  # 读取超时10秒
    max_pool_connections=15,  # 连接池大小
    proxies={"http": None, "https": None},  # 明确禁用代理
)

# 上传操作的 botocore 配置
self.upload_boto_config = Config(
    signature_version="s3v4",
    retries={
        "total_max_attempts": 5,  # 总共尝试5次（1次初始 + 4次重试）
        "mode": "standard",  # 使用标准重试模式
    },
    connect_timeout=10,  # 连接超时10秒
    read_timeout=60,  # 读取超时60秒
    max_pool_connections=5,  # 连接池大小
    proxies={"http": None, "https": None},  # 明确禁用代理
)

# 创建常规 S3 客户端（默认客户端）
self.client = self.session.client(
    "s3",
    **config.get_client_kwargs(),
    config=self.regular_boto_config,
)

# 创建上传专用 S3 客户端
self.upload_client = self.session.client(
    "s3",
    **config.get_client_kwargs(),
    config=self.upload_boto_config,
)
```

这两个客户端在内部维护各自独立的连接池，不会相互干扰。

### 上传方法

我们修改了 `upload_file` 方法，使其使用上传专用客户端：

```python
def upload_file(self, key, file_path):
    """
    上传文件到存储服务

    使用上传专用客户端，适用于大文件上传（如 YouTube 转录文件）

    :param key: 文件的键（路径）
    :param file_path: 本地文件路径
    """
    # 使用上传专用客户端，适合大文件上传
    self.upload_client.upload_file(file_path, self.config.get_bucket_name(), key)
```

### YouTube 转录

在 `YoutubeTranscriber` 类的 `upload_to_r2` 方法中，我们直接使用 `storage.upload_file` 方法，它会自动使用上传专用客户端：

```python
# 使用 storage 的 upload_file 方法，它会自动使用上传专用客户端
logger.info("Uploading YouTube file using storage.upload_file method")
self.storage.upload_file(self.tf.file_key, self.filepath)
logger.info(f"Successfully uploaded file to R2: {self.tf.file_key}")
```

这种方式更加简洁，不需要手动判断是否使用上传专用客户端，而是依赖 `S3Storage` 类中的 `upload_file` 方法来处理这个逻辑。

## 连接池配置说明

### 常规操作连接池

- **max_pool_connections=15**：
  - 较大的连接池，可以处理更多并发请求
  - 适合处理大量的轻量级操作

- **connect_timeout=5, read_timeout=10**：
  - 较短的超时时间，确保请求快速失败
  - 适合需要快速响应的操作

- **total_max_attempts=3**：
  - 较少的重试次数，避免长时间等待
  - 适合轻量级操作，失败后可以快速重试

- **s3={"addressing_style": "virtual"}**：
  - 使用虚拟寻址样式，提高性能和兼容性
  - 适用于 Cloudflare R2 等 S3 兼容存储服务

### 上传专用连接池

- **max_pool_connections=5**：
  - 较小的连接池，因为上传操作通常并发量较低
  - 每个连接可以处理更大的数据量

- **connect_timeout=10, read_timeout=60**：
  - 较长的超时时间，适合大文件上传
  - 允许操作有足够的时间完成

- **total_max_attempts=5**：
  - 更多的重试次数，增加上传成功的机会
  - 适合重要的上传操作

- **s3={"addressing_style": "virtual"}**：
  - 使用虚拟寻址样式，提高性能和兼容性
  - 适用于 Cloudflare R2 等 S3 兼容存储服务

## 优势

1. **资源隔离**：
   - 不同类型的操作使用不同的连接池，避免资源竞争
   - 大文件上传不会影响常规操作的响应时间

2. **针对性优化**：
   - 每种操作类型都有适合的超时和重试设置
   - 提高整体系统的稳定性和性能

3. **更好的错误处理**：
   - 不同类型的操作可以有不同的错误处理策略
   - 提高系统的可靠性

## 注意事项

1. **内存使用**：
   - 两个连接池会消耗更多内存
   - 但总连接数（15+5=20）与之前相比没有显著增加

2. **代码复杂性**：
   - 需要在适当的地方选择正确的客户端
   - 添加了兼容性检查，以支持没有专用上传客户端的情况

3. **监控**：
   - 应该监控两个连接池的使用情况
   - 根据实际使用情况调整连接池大小和超时设置
