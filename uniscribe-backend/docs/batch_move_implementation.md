# 批量移动转录记录到文件夹功能实现文档

## 功能概述

实现了批量移动转录记录到指定文件夹的功能，用户可以在前端一次性选中多个转录文件并移动到指定文件夹，提高操作效率。

**重要更新**：已删除原有的单个移动API (`PATCH /transcriptions/<int:transcription_id>/folder`)，现在前端的单个移动和批量移动操作都统一使用批量移动API。

## 实现的功能

1. **统一的移动API**：单个和批量移动都使用同一个API端点
2. **批量移动**：支持一次移动最多100个转录文件到指定文件夹
3. **单个移动**：支持移动单个转录文件（通过传递包含一个ID的数组）
4. **权限验证**：严格验证每个文件的所有权和目标文件夹的访问权限
5. **文件夹验证**：验证目标文件夹的存在性和所有权
6. **详细反馈**：返回移动结果统计和错误详情
7. **部分成功处理**：支持部分文件移动成功的场景
8. **事务性操作**：确保数据一致性
9. **支持移动到未分类**：支持将文件移动到未分类状态（folderId为null）

## 新增的文件和修改

### 1. `controllers/folder.py`
- **新增**：`batch_move_transcriptions_to_folder` 函数
- **删除**：`move_transcription_to_folder` 函数（已废弃）

```python
def batch_move_transcriptions_to_folder(user_id, transcription_ids, folder_id):
    """批量将转录记录移动到指定文件夹"""
    # 批量移动逻辑，支持单个和批量操作
    pass
```

### 2. `resources/folder.py`
- **新增**：`BatchTranscriptionFolderResource` 类
- **删除**：`TranscriptionFolderResource` 类（已废弃）

```python
class BatchTranscriptionFolderResource(Resource):
    """批量移动转录记录到文件夹资源"""

    @auth_required
    def patch(self):
        # 统一的移动逻辑，支持单个和批量操作
        pass
```

### 3. `routes.py`
- **新增**：批量移动路由
- **删除**：单个移动路由（已废弃）

```python
# 批量移动转录记录到文件夹（支持单个和批量）
api.add_resource(
    BatchTranscriptionFolderResource,
    "/transcriptions/batch-folder",
    endpoint="batch_transcription_folder",
    methods=["PATCH"],
)
```

### 4. 已删除的API端点
- ~~`PATCH /transcriptions/<int:transcription_id>/folder`~~ （已删除）

## API 使用说明

### 批量移动 API

**端点**: `PATCH /transcriptions/batch-folder`

**批量移动请求参数**:
```json
{
  "transcriptionIds": ["7340683505690612000", "7340716745725972000", "123"],
  "folderId": 456
}
```

**单个移动请求参数**:
```json
{
  "transcriptionIds": ["7340683505690612000"],
  "folderId": 456
}
```

**移动到未分类**:
```json
{
  "transcriptionIds": ["7340683505690612000", "7340716745725972000"],
  "folderId": null
}
```

> **重要说明**：由于转录记录ID是64位大整数，超过了JavaScript的安全整数范围，建议前端使用字符串格式传输ID，后端会自动处理字符串到整数的转换。

**成功响应** (200):
```json
{
  "total": 3,
  "moved": 2,
  "failed": 1,
  "errors": [
    {
      "transcriptionId": "7340683505690612000",
      "error": "Transcription not found"
    }
  ]
}
```

**错误响应** (400):
```json
{
  "message": "transcriptionIds cannot be empty"
}
```

**错误响应** (404):
```json
{
  "message": "Folder not found"
}
```

## 参数说明

- `transcriptionIds` (必需):
  - 类型: `array<string|number>`
  - 说明: 要移动的转录记录ID列表
  - 限制: 最多100个转录记录ID
  - 验证: 所有ID必须是有效的整数（支持字符串格式的大整数）
  - 推荐: 使用字符串格式避免JavaScript大整数精度丢失问题

- `folderId` (可选):
  - 类型: `number|null`
  - 说明: 目标文件夹ID，null表示移动到未分类
  - 验证: 如果不为null，必须是用户拥有的有效文件夹ID

## 响应字段说明

- `total`: 请求移动的转录记录总数
- `moved`: 成功移动的转录记录数量
- `failed`: 移动失败的转录记录数量
- `errors`: 失败记录的详细错误信息数组
  - `transcriptionId`: 失败的转录记录ID（字符串格式）
  - `error`: 具体的错误信息

## 错误处理

### 常见错误类型

1. **转录记录不存在**: `"Transcription not found"`
2. **权限不足**: `"Unauthorized"`
3. **文件夹不存在**: `"Folder not found"`
4. **ID格式错误**: `"Invalid transcription ID format"`
5. **参数验证错误**: 
   - `"transcriptionIds cannot be empty"`
   - `"Cannot move more than 100 transcriptions at once"`

### 部分成功处理

API支持部分成功的场景：
- 如果某些转录记录移动失败，其他成功的记录仍会被移动
- 返回详细的统计信息和错误列表
- 前端可以根据返回结果向用户展示具体的成功和失败情况

## 安全考虑

1. **权限验证**: 严格验证每个转录记录和目标文件夹的所有权
2. **批量限制**: 限制单次操作最多100个转录记录，防止系统过载
3. **事务性**: 使用数据库事务确保数据一致性
4. **错误隔离**: 单个记录的错误不会影响其他记录的处理

## 与现有功能的关系

- **单个移动**: 复用了 `move_transcription_to_folder` 函数的核心验证逻辑
- **批量删除**: 参考了 `BatchDeleteTranscriptionResource` 的实现模式
- **文件夹管理**: 与现有的文件夹CRUD操作保持一致的权限验证机制

## 使用示例

### 前端JavaScript示例

```javascript
// 统一的移动函数（支持单个和批量）
const moveToFolder = async (transcriptionIds, folderId) => {
  // 确保 transcriptionIds 是数组
  const ids = Array.isArray(transcriptionIds) ? transcriptionIds : [transcriptionIds];

  try {
    const response = await fetch('/transcriptions/batch-folder', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        transcriptionIds: ids.map(id => id.toString()), // 转换为字符串
        folderId: folderId
      })
    });

    const result = await response.json();

    if (response.ok) {
      const fileCount = ids.length;
      const actionText = fileCount === 1 ? '个文件' : '个文件';
      showSuccess(`成功移动 ${result.moved} ${actionText}`);

      if (result.failed > 0) {
        showWarning(`${result.failed} 个文件移动失败`);
        console.log('失败详情:', result.errors);
      }

      // 刷新文件列表
      refreshFileList();
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError('移动失败，请重试');
  }
};

// 单个移动到指定文件夹
const moveSingleToFolder = async (transcriptionId, folderId) => {
  await moveToFolder([transcriptionId], folderId);
};

// 批量移动到指定文件夹
const batchMoveToFolder = async (transcriptionIds, folderId) => {
  await moveToFolder(transcriptionIds, folderId);
};

// 移动到未分类（支持单个和批量）
const moveToUncategorized = async (transcriptionIds) => {
  await moveToFolder(transcriptionIds, null);
};
```

### 前端UI集成示例

```javascript
// 单个文件操作按钮
<button onClick={() => moveSingleToFolder(fileId, selectedFolderId)}>
  移动到文件夹
</button>

<button onClick={() => moveToUncategorized(fileId)}>
  移动到未分类
</button>

// 批量操作按钮
<button
  disabled={selectedFiles.length === 0}
  onClick={() => batchMoveToFolder(selectedFiles, selectedFolderId)}
>
  移动选中的 {selectedFiles.length} 个文件到文件夹
</button>

<button
  disabled={selectedFiles.length === 0}
  onClick={() => moveToUncategorized(selectedFiles)}
>
  移动选中的 {selectedFiles.length} 个文件到未分类
</button>
```

## 测试建议

1. **正常流程测试**: 测试批量移动到不同文件夹的功能
2. **权限测试**: 测试移动其他用户的文件或移动到其他用户的文件夹
3. **边界测试**: 测试空列表、超过100个文件等边界情况
4. **部分失败测试**: 测试包含无效ID的混合请求
5. **并发测试**: 测试同时进行多个批量操作的情况

## 性能考虑

- 批量操作在单个数据库事务中完成，减少数据库连接开销
- 限制单次操作数量，避免长时间锁定数据库
- 错误处理不会中断整个批量操作，提高用户体验
