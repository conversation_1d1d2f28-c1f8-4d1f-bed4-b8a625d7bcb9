# Redis Streams 内存管理

## 问题背景

自 7.5 改用 Redis Streams 做异步任务队列后，Redis 内存使用增长很快：

- Redis 内存限制：1GB
- 问题原因：Redis Streams 默认保留所有历史消息
- 解决方案：自动删除已确认的消息 + 一次性清理历史数据

## 🎯 解决方案

### 1. Go Consumer 自动删除（主要方案）

**修改位置**：`uniscribe-service/internal/queue/redis_consumer.go`

**修改内容**：在 `XACK` 确认消息后立即调用 `XDEL` 删除消息

```go
// 确认成功后立即删除消息以节省内存
err := c.client.XAck(ctx, streamName, c.groupName, message.ID).Err()
if err != nil {
    log.Printf("Error acknowledging message %s: %v", message.ID, err)
} else {
    // 立即删除消息
    delErr := c.client.XDel(ctx, streamName, message.ID).Err()
    if delErr != nil {
        log.Printf("Warning: failed to delete message %s: %v", message.ID, delErr)
    } else {
        log.Printf("Message %s acknowledged and deleted", message.ID)
    }
}
```

**效果**：

- ✅ 新消息处理完成后立即删除，不再积累
- ✅ 内存使用保持稳定
- ✅ 不影响现有功能

### 2. 安全清理历史数据

**脚本位置**：`uniscribe-backend/scripts/cleanup_redis_streams.py`

**安全策略**：

- ✅ 只删除已确认的消息（不在 pending 列表中）
- ✅ 保留所有未读取的消息
- ✅ 保留所有正在处理的消息（pending）
- ✅ 基于 `last-delivered-id` 确保安全边界

**使用方法**：

```bash
# 使用 Flask CLI 运行脚本

# 查看当前内存使用情况
flask cleanup-redis-streams --show-memory

# 预览清理效果（不实际删除）
flask cleanup-redis-streams --dry-run

# 执行清理，每个队列保留最新 500 条消息
flask cleanup-redis-streams --keep 500

# 执行清理，每个队列保留最新 1000 条消息（默认）
flask cleanup-redis-streams

# 组合使用：显示内存 + 预览清理
flask cleanup-redis-streams --show-memory --dry-run --keep 100
```

## 📊 清理效果预期

### 当前状态（清理前）

```
media_preprocessing: 34 条消息
transcription:high: 9 条消息
text:high: 69 条消息
text:low: 3 条消息
speaker_diarization:high: 3 条消息
总计: 118 条消息
```

### 清理后状态

- **Go Consumer 修改后**：新消息处理完立即删除，队列长度保持稳定
- **历史数据清理后**：根据 `--keep` 参数保留指定数量的最新消息

## 🚀 部署步骤

### 1. 立即执行（解决当前问题）

```bash
# 1. 清理历史数据
cd uniscribe-backend
flask cleanup-redis-streams --keep 100  # 每个队列只保留100条

# 2. 重新编译并重启 Go 服务
cd ../uniscribe-service
go build -o uniscribe-service .
# 重启服务...
```

### 2. 验证效果

```bash
# 检查内存使用
flask cleanup-redis-streams --show-memory

# 检查队列状态
curl http://localhost:8000/queue/stats | jq '.stats'
```

## ⚠️ 注意事项

1. **数据安全**：

   - 已确认的消息会被删除，无法恢复
   - 建议先在测试环境验证

2. **监控建议**：

   - 定期检查 Redis 内存使用率
   - 监控队列长度变化
   - 关注 Go 服务日志中的删除操作

3. **回滚方案**：
   - 如果出现问题，可以注释掉 Go 代码中的 `XDel` 调用
   - 重新编译部署即可恢复到只确认不删除的模式

## 📈 长期监控

### 内存使用监控

```bash
# 定期检查（可以加入 crontab）
cd /path/to/uniscribe-backend && flask cleanup-redis-streams --show-memory
```

### 队列健康检查

```bash
# API 监控
curl http://localhost:8000/queue/stats
```

### 告警阈值建议

- Redis 内存使用率 > 80%：警告
- Redis 内存使用率 > 90%：严重告警
- 单个队列消息数 > 1000：建议清理

## 🔧 故障排除

### 如果内存仍然增长

1. 检查 Go 服务是否正确重启
2. 查看 Go 服务日志确认删除操作正常
3. 检查是否有其他服务在使用 Redis Streams

### 如果任务处理异常

1. 检查 Go 服务日志中的错误信息
2. 确认 XACK 操作成功
3. 如有问题可临时禁用 XDEL 操作
