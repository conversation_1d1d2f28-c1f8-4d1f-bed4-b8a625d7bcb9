# 说话人识别功能实现

## 概述

本文档描述了说话人识别功能的完整实现方案。该功能允许用户在上传音频文件或提交 YouTube URL 时选择开启说话人识别，系统会并行处理转录和说话人识别任务，然后通过对齐算法将结果合并。

## 架构设计

### 数据流程

```
用户上传音频 + 选择说话人识别
           ↓
    创建两个并行任务
    ↙              ↘
转录任务           说话人识别任务
(fal/deepinfra)   (replicate)
    ↓              ↓
转录结果           说话人分段结果
    ↘              ↙
      对齐处理任务
         ↓
    最终结果存储
```

### 核心组件

1. **任务系统扩展**：新增 `speaker_diarization` 任务类型
2. **数据模型扩展**：支持说话人识别相关数据存储
3. **对齐服务**：实现转录结果与说话人识别结果的对齐
4. **队列系统**：使用 Redis Streams 管理对齐任务
5. **Worker 进程**：专门处理对齐任务的后台进程

## 数据库变更

### TranscriptionFile 表

新增字段：
- `enable_speaker_diarization` (BOOLEAN): 是否开启说话人识别

### TaskResult 表

新增字段：
- `diarization_segments` (JSON): 说话人识别分段数据
- `aligned_segments` (JSON): 对齐后的分段数据

### Task 表

新增任务类型：
- `speaker_diarization = 9`: 说话人识别任务

## API 接口变更

### 上传接口

**文件上传** (`POST /upload/generate-signed-url`)
```json
{
  "filename": "audio.mp3",
  "fileType": "mp3",
  "fileSize": 1024000,
  "contentMd5Base64": "...",
  "duration": 120.5,
  "languageCode": "en",
  "transcriptionType": "transcript",
  "enableSpeakerDiarization": true  // 新增参数
}
```

**YouTube 转录** (`POST /youtube/transcription`)
```json
{
  "url": "https://youtube.com/watch?v=...",
  "title": "Video Title",
  "duration": 300,
  "languageCode": "en",
  "transcriptionType": "transcript",
  "enableSpeakerDiarization": true  // 新增参数
}
```

### 结果接口

**获取转录结果** (`GET /transcriptions/{id}`)

响应中的 `segments` 字段会根据是否开启说话人识别返回不同格式：

**普通转录结果**：
```json
{
  "segments": [
    {
      "id": 0,
      "start_time": 0.0,
      "end_time": 5.2,
      "text": "Hello world"
    }
  ],
  "hasSpeakerInfo": false
}
```

**包含说话人信息的结果**：
```json
{
  "segments": [
    {
      "id": 0,
      "start_time": 0.0,
      "end_time": 5.2,
      "text": "Hello world",
      "speaker": "A"  // 新增字段
    }
  ],
  "hasSpeakerInfo": true
}
```

## 服务实现

### AlignmentService

负责转录结果与说话人识别结果的对齐：

```python
# 核心对齐方法
aligned_segments = AlignmentService.align_segments(
    transcription_segments,
    diarization_data
)
```

### AlignmentQueueService

使用 Redis Streams 管理对齐任务队列：

```python
# 入队
AlignmentQueueService.enqueue_alignment_task({
    "file_id": 123,
    "transcription_task_id": 456,
    "diarization_task_id": 789
})

# 出队
task = AlignmentQueueService.dequeue_alignment_task()
```

### AlignmentWorker

后台 Worker 进程，消费对齐任务：

```bash
# 启动 Worker
python workers/alignment_worker.py
```

## 部署步骤

### 1. 数据库迁移

```bash
python cli/migrate_speaker_diarization.py
```

### 2. 启动对齐 Worker

```bash
python workers/alignment_worker.py
```

### 3. Go 服务扩展

需要在 Go 服务中添加：
- 说话人识别任务处理器
- Replicate API 集成
- 结果回写逻辑

## 测试

运行测试脚本验证功能：

```bash
python cli/test_speaker_diarization.py
```

测试内容包括：
- 对齐算法测试
- 时间解析测试
- 队列服务测试
- 数据库字段验证

## 配置要求

### Redis

需要支持 Redis Streams 功能（Redis 5.0+）

### 外部服务

- **转录服务**：fal whisper, deepinfra whisper-large-v3-turbo
- **说话人识别**：replicate.com/meronym/speaker-diarization

## 监控和日志

### 关键指标

- 对齐任务队列长度
- 对齐任务处理时间
- 对齐成功率

### 日志位置

- Worker 日志：`logs/alignment_worker.log`
- 应用日志：标准应用日志

## 故障处理

### 常见问题

1. **对齐任务堆积**：检查 Worker 进程状态
2. **对齐失败**：检查转录和说话人识别数据格式
3. **Redis 连接问题**：检查 Redis 服务状态

### 恢复策略

- 对齐失败时保留原始转录结果
- 支持手动重新触发对齐任务
- 详细的错误日志和监控

## 性能优化

### 并行处理

- 转录和说话人识别任务完全并行
- 减少总体处理时间

### 资源使用

- 对齐算法内存优化
- 大文件分块处理

### 成本控制

- 只有用户明确选择时才启用说话人识别
- 可配置用户权限限制
