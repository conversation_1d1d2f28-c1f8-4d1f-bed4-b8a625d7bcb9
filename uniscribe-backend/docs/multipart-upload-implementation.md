# 分块上传后端实现文档

## 📋 概述

本文档详细描述了 Uniscribe 后端分块上传功能的设计、实现和使用方法。该功能支持大文件（>100MB）的分块上传和断点续传，为前端 Uppy 集成提供完整的后端支持。

## 🏗️ 系统架构

### 核心组件

1. **数据模型层**

   - `MultipartUpload` - 分块上传会话管理
   - `TranscriptionFile` - 转录文件记录（复用现有）

2. **存储抽象层**

   - `StorageInterface` - 存储接口定义
   - `S3Storage` - S3/Cloudflare R2 实现
   - `COSStorage` - 腾讯云COS实现（暂不支持分块上传）

3. **API资源层**

   - 5个RESTful端点处理分块上传生命周期

4. **业务逻辑层**
   - 复用现有的转录文件创建和完成逻辑
   - 集成配额检查和用户权限验证

### 技术栈

- **框架**: Flask + Flask-RESTful
- **数据库**: MySQL 8.0
- **存储**: Cloudflare R2 (S3兼容)
- **ORM**: SQLAlchemy
- **认证**: 现有的JWT认证系统

## 🗄️ 数据库设计

### multipart_uploads 表

```sql
CREATE TABLE `multipart_uploads` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `upload_id` varchar(255) NOT NULL COMMENT 'S3分块上传会话ID',
  `transcription_file_id` bigint NOT NULL COMMENT '关联的转录文件ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `file_key` varchar(255) NOT NULL COMMENT '存储系统中的文件键',
  `filename` varchar(100) NOT NULL COMMENT '原始文件名',
  `file_type` varchar(20) NOT NULL COMMENT '文件类型',
  `file_size` bigint NOT NULL COMMENT '文件总大小',
  `fingerprint` varchar(32) NOT NULL COMMENT '文件MD5指纹',
  `status` enum('active','completed','aborted') NOT NULL DEFAULT 'active' COMMENT '上传状态',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_upload_id` (`upload_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transcription_file_id` (`transcription_file_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分块上传会话表';
```

### 字段说明

- `upload_id`: S3返回的分块上传会话ID，全局唯一
- `transcription_file_id`: 关联的转录文件记录ID
- `fingerprint`: 文件MD5指纹，用于断点续传检测
- `status`: 上传状态（active/completed/aborted）

### 索引设计

- 主键索引：快速定位记录
- 唯一索引：确保会话ID唯一性
- 用户索引：支持用户维度查询
- 状态索引：支持状态过滤
- 时间索引：支持清理任务

## 🔧 存储层实现

### 接口定义

```python
class StorageInterface(ABC):
    @abstractmethod
    def create_multipart_upload(self, key, content_type=None):
        """创建分块上传会话"""
        pass

    @abstractmethod
    def list_multipart_parts(self, key, upload_id):
        """列出已上传的分块"""
        pass

    @abstractmethod
    def generate_presigned_url_for_multipart_part(self, key, upload_id, part_number, expired):
        """为分块生成预签名URL"""
        pass

    @abstractmethod
    def complete_multipart_upload(self, key, upload_id, parts):
        """完成分块上传"""
        pass

    @abstractmethod
    def abort_multipart_upload(self, key, upload_id):
        """取消分块上传"""
        pass
```

### S3Storage 实现要点

1. **创建分块上传**

   - 调用 `create_multipart_upload` API
   - 返回 S3 生成的 UploadId

2. **列出已上传分块**

   - 调用 `list_parts` API
   - 返回分块编号、ETag、大小信息

3. **生成分块预签名URL**

   - 使用 `upload_part` 操作
   - 支持 PUT 方法上传

4. **完成分块上传**

   - 调用 `complete_multipart_upload` API
   - 需要提供所有分块的 ETag 信息

5. **取消分块上传**
   - 调用 `abort_multipart_upload` API
   - 清理未完成的分块数据

## 📡 API 接口设计

### 1. 创建分块上传

**端点**: `POST /upload/multipart/create`

**请求参数**:

```json
{
  "filename": "large-video.mp4",
  "fileType": "mp4",
  "fileSize": 2147483648,
  "contentMd5Base64": "abc123...",
  "duration": 3600.5,
  "forceUpload": false,
  "languageCode": "en",
  "transcriptionType": "transcript",
  "enableSpeakerDiarization": false,
  "folderId": "123"
}
```

**响应**:

```json
{
  "uploadId": "multipart-session-id-123",
  "transcriptionFileId": "456",
  "key": "user123-abc123.mp4",
  "resumable": false
}
```

**功能特性**:

- 支持断点续传检测
- 集成现有的配额检查
- 复用转录文件创建逻辑

### 2. 列出已上传分块

**端点**: `GET /upload/multipart/{uploadId}/parts`

**响应**:

```json
{
  "parts": [
    {
      "PartNumber": 1,
      "ETag": "\"abc123...\"",
      "Size": 10485760
    },
    {
      "PartNumber": 2,
      "ETag": "\"def456...\"",
      "Size": 10485760
    }
  ]
}
```

### 3. 获取分块预签名URL

**端点**: `POST /upload/multipart/{uploadId}/part/{partNumber}/sign`

**响应**:

```json
{
  "signedUrl": "https://bucket.r2.cloudflarestorage.com/key?uploadId=...&partNumber=1&..."
}
```

**限制**:

- 分块编号范围：1-10000
- URL有效期：与现有上传接口一致

### 4. 完成分块上传

**端点**: `POST /upload/multipart/{uploadId}/complete`

**请求参数**:

```json
{
  "parts": [
    { "ETag": "\"abc123...\"", "PartNumber": 1 },
    { "ETag": "\"def456...\"", "PartNumber": 2 }
  ]
}
```

**响应**: 返回完整的转录文件信息（与现有上传接口一致）

### 5. 取消分块上传

**端点**: `POST /upload/multipart/{uploadId}/abort`

**响应**:

```json
{
  "message": "Upload aborted successfully"
}
```

## 🔐 安全设计

### 认证授权

1. **用户认证**: 所有端点都需要JWT认证
2. **权限验证**: 只能操作自己的上传会话
3. **会话验证**: 验证上传会话状态和所有权

### 数据验证

1. **参数验证**: 严格的请求参数类型和范围检查
2. **文件验证**: MD5指纹验证和文件大小检查
3. **状态验证**: 确保只能操作活跃状态的上传会话

### 错误处理

1. **统一错误格式**: 使用Flask-RESTful的标准错误响应
2. **详细错误日志**: 记录所有异常信息用于调试
3. **优雅降级**: S3操作失败时的数据库状态一致性保证

## 🚀 使用示例

### 前端集成示例

```javascript
// 1. 创建分块上传
const createResponse = await uploadService.createMultipartUpload(
  file,
  md5Hash,
  duration,
  false,
  "en",
  false,
  null
);
const { uploadId, transcriptionFileId, key } = createResponse.data;

// 2. 检查已上传分块（断点续传）
const partsResponse = await uploadService.listMultipartParts(uploadId);
const existingParts = partsResponse.data.parts;

// 3. 上传缺失的分块
for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
  if (!existingParts.find((p) => p.PartNumber === partNumber)) {
    // 获取预签名URL
    const signResponse = await uploadService.signMultipartPart(
      uploadId,
      partNumber
    );
    const signedUrl = signResponse.data.signedUrl;

    // 上传分块
    await uploadChunk(signedUrl, chunkData);
  }
}

// 4. 完成上传
const completeResponse = await uploadService.completeMultipartUpload(
  uploadId,
  parts
);
const transcriptionFile = completeResponse.data;
```

### 错误处理示例

```javascript
try {
  await uploadService.createMultipartUpload(/* ... */);
} catch (error) {
  if (error.response?.status === 409) {
    // 文件已存在
    console.log("File already exists");
  } else if (error.response?.status === 403) {
    // 配额不足或权限问题
    console.log("Quota exceeded or permission denied");
  } else {
    // 其他错误
    console.log("Upload failed:", error.message);
  }
}
```

## 🔄 状态流转

```
[创建] → active → [上传分块] → active → [完成] → completed
                              ↓
                           [取消] → aborted
```

### 状态说明

- **active**: 上传进行中，可以继续上传分块
- **completed**: 上传已完成，文件已合并
- **aborted**: 上传已取消，分块数据已清理

## 📊 监控和维护

### 关键指标

1. **上传成功率**: completed / (completed + aborted)
2. **断点续传使用率**: resumable sessions / total sessions
3. **平均上传时间**: 按文件大小分组统计
4. **存储空间使用**: 未完成上传占用的空间

### 清理任务

建议定期清理长时间未完成的上传会话：

```sql
-- 清理7天前创建但未完成的上传会话
DELETE FROM multipart_uploads
WHERE status = 'active'
AND created_time < DATE_SUB(NOW(), INTERVAL 7 DAY);
```

### 故障排查

1. **上传失败**: 检查S3连接和权限配置
2. **断点续传失败**: 验证文件指纹匹配
3. **数据不一致**: 检查事务处理和错误恢复逻辑

## 🔮 扩展计划

### 短期优化

1. **性能优化**: 分块上传并发控制
2. **监控增强**: 添加更多业务指标
3. **错误恢复**: 自动重试机制

### 长期规划

1. **多存储支持**: 完善腾讯云COS分块上传
2. **智能分块**: 根据网络状况动态调整分块大小
3. **压缩上传**: 支持分块级别的压缩传输

## 🛠️ 部署指南

### 数据库迁移

1. **执行迁移脚本**:

```bash
mysql -u username -p database_name < migrations/create_multipart_uploads.sql
```

2. **验证表创建**:

```sql
DESCRIBE multipart_uploads;
SHOW INDEX FROM multipart_uploads;
```

### 配置检查

确保以下配置正确：

1. **存储配置**: Cloudflare R2 访问密钥和端点
2. **数据库连接**: MySQL连接池配置
3. **认证系统**: JWT密钥配置

### 服务重启

```bash
# 重启后端服务以加载新的端点
sudo systemctl restart uniscribe-backend
# 或使用Docker
docker-compose restart uniscribe-backend
```

## 🧪 测试指南

### 单元测试

```python
# tests/test_multipart_upload.py
import pytest
from models.multipart_upload import MultipartUpload, MultipartUploadStatus

def test_create_multipart_upload():
    """测试创建分块上传记录"""
    upload = MultipartUpload(
        upload_id="test-session-123",
        transcription_file_id=1,
        user_id=1,
        file_key="test-key",
        filename="test.mp4",
        file_type="mp4",
        file_size=1000000,
        fingerprint="abc123"
    )
    assert upload.status == MultipartUploadStatus.ACTIVE.value

def test_get_by_session_id():
    """测试根据会话ID查询"""
    upload = MultipartUpload.get_by_upload_id("test-session-123")
    assert upload is not None
    assert upload.upload_id == "test-session-123"
```

### 集成测试

```python
# tests/test_multipart_api.py
def test_create_multipart_upload_api(client, auth_headers):
    """测试创建分块上传API"""
    data = {
        "filename": "test-video",
        "fileType": "mp4",
        "fileSize": 200000000,  # 200MB
        "contentMd5Base64": "dGVzdA==",
        "duration": 120.5
    }

    response = client.post(
        "/upload/multipart/create",
        json=data,
        headers=auth_headers
    )

    assert response.status_code == 200
    assert "uploadId" in response.json
    assert "transcriptionFileId" in response.json

def test_list_parts_api(client, auth_headers, upload_session):
    """测试列出分块API"""
    response = client.get(
        f"/upload/multipart/{upload_session.upload_id}/parts",
        headers=auth_headers
    )

    assert response.status_code == 200
    assert "parts" in response.json
```

### 性能测试

```bash
# 使用Apache Bench测试并发性能
ab -n 100 -c 10 -H "Authorization: Bearer <token>" \
   -p create_upload.json -T application/json \
   http://localhost:5000/upload/multipart/create

# 使用curl测试大文件上传
curl -X POST "http://localhost:5000/upload/multipart/create" \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d @large_file_request.json
```

## 🔍 故障排查

### 常见问题

#### 1. 创建分块上传失败

**症状**: 返回500错误，日志显示S3连接失败

**排查步骤**:

```bash
# 检查S3配置
grep -r "CLOUDFLARE_R2" config/
# 测试S3连接
python -c "
import boto3
client = boto3.client('s3', endpoint_url='your-endpoint')
print(client.list_buckets())
"
```

**解决方案**:

- 验证访问密钥和端点配置
- 检查网络连接和防火墙设置
- 确认存储桶权限配置

#### 2. 断点续传不工作

**症状**: 每次都创建新的上传会话，不能恢复之前的上传

**排查步骤**:

```sql
-- 检查是否有活跃的上传会话
SELECT * FROM multipart_uploads
WHERE user_id = ? AND fingerprint = ? AND status = 'active';

-- 检查文件指纹计算
SELECT fingerprint FROM transcription_file WHERE id = ?;
```

**解决方案**:

- 确认前端MD5计算一致性
- 检查数据库事务提交状态
- 验证用户ID匹配

#### 3. 分块上传超时

**症状**: 大分块上传时出现超时错误

**排查步骤**:

```python
# 检查boto3配置
from libs.storage.s3_storage import S3Storage
storage = S3Storage(config)
print(storage.regular_boto_config.read_timeout)
print(storage.upload_boto_config.read_timeout)
```

**解决方案**:

- 调整boto3超时配置
- 减小分块大小
- 增加重试次数

### 日志分析

#### 关键日志位置

```bash
# 应用日志
tail -f /var/log/uniscribe/app.log | grep -i multipart

# 错误日志
tail -f /var/log/uniscribe/error.log | grep -E "(multipart|upload)"

# 数据库慢查询日志
tail -f /var/log/mysql/slow.log | grep multipart_uploads
```

#### 日志示例

```
# 正常创建分块上传
[INFO] Creating multipart upload for user=123, file=test.mp4, size=200MB
[INFO] S3 multipart upload created: upload_id=abc123, key=123-hash.mp4
[INFO] Database record created: multipart_upload_id=456

# 断点续传检测
[INFO] Found existing active upload session for user=123, fingerprint=hash123
[INFO] Returning resumable upload session: upload_id=abc123

# 完成上传
[INFO] Completing multipart upload: upload_id=abc123, parts=20
[INFO] S3 multipart upload completed successfully
[INFO] Transcription file upload completed: file_id=789
```

## 📈 性能优化

### 数据库优化

1. **索引优化**:

```sql
-- 添加复合索引支持断点续传查询
CREATE INDEX idx_user_fingerprint_status
ON multipart_uploads(user_id, fingerprint, status);

-- 添加清理任务索引
CREATE INDEX idx_status_created_time
ON multipart_uploads(status, created_time);
```

2. **查询优化**:

```python
# 使用数据库连接池
from sqlalchemy.pool import QueuePool
engine = create_engine(
    database_url,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30
)
```

### 存储优化

1. **连接池配置**:

```python
# S3Storage配置优化
self.upload_boto_config = Config(
    max_pool_connections=10,  # 增加连接池大小
    retries={'max_attempts': 5},  # 增加重试次数
    read_timeout=120  # 增加读取超时
)
```

2. **分块大小优化**:

```javascript
// 前端分块大小建议
const CHUNK_SIZE = {
  small: 5 * 1024 * 1024, // 5MB - 网络较慢时
  medium: 10 * 1024 * 1024, // 10MB - 默认
  large: 25 * 1024 * 1024, // 25MB - 网络较快时
};
```

### 并发控制

```python
# 使用信号量控制并发上传
import asyncio
from asyncio import Semaphore

class MultipartUploadManager:
    def __init__(self, max_concurrent_uploads=5):
        self.semaphore = Semaphore(max_concurrent_uploads)

    async def upload_part(self, part_data):
        async with self.semaphore:
            # 执行分块上传
            pass
```

## 🔒 安全最佳实践

### 输入验证

```python
# 严格的参数验证
def validate_multipart_request(data):
    # 文件大小限制
    if data.get('fileSize', 0) > 5 * 1024 * 1024 * 1024:  # 5GB
        raise ValueError("File too large")

    # 文件类型白名单
    allowed_types = ['mp3', 'mp4', 'wav', 'flac', 'm4a']
    if data.get('fileType', '').lower() not in allowed_types:
        raise ValueError("Unsupported file type")

    # MD5格式验证
    import base64, binascii
    try:
        base64.b64decode(data.get('contentMd5Base64', ''))
    except binascii.Error:
        raise ValueError("Invalid MD5 format")
```

### 权限控制

```python
# 细粒度权限检查
def check_upload_permission(user, file_size, duration):
    # 检查用户状态
    if not user.is_active:
        raise PermissionError("User account is inactive")

    # 检查配额限制
    if user.remaining_quota < duration:
        raise PermissionError("Insufficient quota")

    # 检查并发上传限制
    active_uploads = MultipartUpload.query.filter_by(
        user_id=user.id,
        status='active'
    ).count()
    if active_uploads >= 3:  # 最多3个并发上传
        raise PermissionError("Too many concurrent uploads")
```

### 数据清理

```python
# 定期清理任务
from celery import Celery
from datetime import datetime, timedelta

@celery.task
def cleanup_abandoned_uploads():
    """清理超过7天的未完成上传"""
    cutoff_time = datetime.now() - timedelta(days=7)

    abandoned_uploads = MultipartUpload.query.filter(
        MultipartUpload.status == 'active',
        MultipartUpload.created_time < cutoff_time
    ).all()

    for upload in abandoned_uploads:
        try:
            # 清理S3分块数据
            storage.abort_multipart_upload(
                upload.file_key,
                upload.upload_id
            )
            # 更新数据库状态
            upload.mark_aborted()
            db.session.commit()
        except Exception as e:
            logger.error(f"Failed to cleanup upload {upload.id}: {e}")
```

---

**文档版本**: v1.0
**最后更新**: 2025-06-30
**维护者**: Backend Team
