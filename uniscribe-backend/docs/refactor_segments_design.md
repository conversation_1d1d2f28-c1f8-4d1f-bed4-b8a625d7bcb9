# 重构 Segments 数据结构设计

## 问题描述

当前的说话人识别功能使用了复杂的双字段设计：
- `segments`: 原始转录结果
- `aligned_segments`: 对齐后的结果（包含说话人信息）

这导致了以下问题：
1. 读取接口需要条件判断返回哪个字段
2. 编辑接口需要判断修改哪个字段
3. 逻辑复杂，难以维护
4. 扩展性差

## 新设计方案

### 数据结构

```python
class TaskResult:
    segments = db.Column(db.JSON)  # 最终的、可编辑的结果
    original_segments = db.Column(db.JSON)  # 原始转录结果（用于debug）
    diarization_segments = db.Column(db.JSON)  # 说话人识别原始结果（用于debug）
```

### 字段说明

1. **segments** (主要字段)
   - 存储最终的、可编辑的转录结果
   - 所有读取和编辑操作都基于这个字段
   - 如果开启了说话人识别，包含 speaker 信息
   - 如果没有开启说话人识别，就是普通的转录结果

2. **original_segments** (调试字段)
   - 存储第一次转录的原始结果
   - 用于调试和问题排查
   - 不参与业务逻辑，只用于对比和分析

3. **diarization_segments** (调试字段，保持不变)
   - 存储说话人识别的原始结果
   - 用于调试说话人识别的准确度
   - 只有开启说话人识别时才有数据

### 数据流程

#### 普通转录流程
```
转录完成 → segments = 转录结果
         → original_segments = 转录结果
```

#### 说话人识别转录流程
```
转录完成 → original_segments = 转录结果
         → segments = 转录结果 (临时)

说话人识别完成 → diarization_segments = 说话人识别结果

对齐完成 → segments = 对齐后的结果 (最终)
```

### API 接口简化

#### 读取接口
```python
# 简化前（复杂的条件判断）
if transcription_file.enable_speaker_diarization and task_result.aligned_segments:
    transcription_file.result.segments = task_result.aligned_segments
else:
    # 复杂的条件判断逻辑

# 简化后（直接返回，移除无用的 has_speaker_info 字段）
transcription_file.result.segments = task_result.segments
```

#### 编辑接口
```python
# 简化前（需要判断修改哪个字段）
if transcription_file.enable_speaker_diarization and task_result.aligned_segments:
    segments = task_result.aligned_segments
    segments_field = "aligned_segments"
else:
    segments = task_result.segments
    segments_field = "segments"

# 简化后（统一操作）
segments = task_result.segments
# 直接修改 segments 字段
```

## 迁移策略

### 数据迁移
1. 添加 `original_segments` 字段
2. 将现有数据迁移到新结构：
   - 如果有 `aligned_segments`，将其复制到 `segments`，原 `segments` 复制到 `original_segments`
   - 如果没有 `aligned_segments`，保持 `segments` 不变，复制到 `original_segments`
3. 删除 `aligned_segments` 字段

### 代码迁移
1. 更新数据模型
2. 简化读取接口
3. 简化编辑接口
4. 更新对齐服务逻辑
5. 更新任务处理逻辑
6. 更新测试用例

## 优势

1. **逻辑简化**: 所有操作都基于单一的 `segments` 字段
2. **易于维护**: 消除了复杂的条件判断逻辑
3. **扩展性好**: 未来的功能扩展不需要考虑多字段同步
4. **保留调试能力**: 原始数据仍然保存，便于问题排查
5. **向后兼容**: API 接口保持不变，前端无需修改

## 风险评估

1. **数据迁移风险**: 需要仔细测试迁移脚本，确保数据完整性
2. **回滚复杂性**: 一旦迁移完成，回滚需要额外的脚本
3. **测试覆盖**: 需要全面测试所有相关功能

## 实施计划

1. 创建数据库迁移脚本
2. 更新数据模型
3. 重构读取和编辑接口
4. 更新对齐服务和任务处理逻辑
5. 更新测试用例
6. 部署和验证
