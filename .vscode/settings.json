{
    // ===== Go 相关配置 =====
    "go.toolsEnvVars": {
        // 启用 Go modules 包管理
        "GO111MODULE": "on",
        // 使用中国区代理，加速依赖下载
        "GOPROXY": "https://goproxy.cn,direct"
    },
    // Go 语言服务器配置
    "gopls": {
        "build.directoryFilters": [
            "-uniscribe-backend" // 排除 Python 项目目录，避免 gopls 分析 Python 代码
        ],
        // 启用语义高亮
        "ui.semanticTokens": true,
        // 使用 gofumpt 格式化工具（比 gofmt 更严格）
        "formatting.gofumpt": true
    },
    // 使用 gopls 作为语言服务器
    "go.useLanguageServer": true,
    // 使用 golangci-lint 做代码检查
    "go.lintTool": "golangci-lint",
    // lint 使用快速模式
    "go.lintFlags": [
        "--fast"
    ],
    // ===== Python 相关配置 =====
    // 指定虚拟环境 Python 解释器路径
    "python.defaultInterpreterPath": "./uniscribe-backend/.venv/bin/python",
    // 添加额外的 Python 模块搜索路径
    "python.analysis.extraPaths": [
        "./uniscribe-backend"
    ],
    // Python 文件特定配置
    "[python]": {
        // 使用 Black 作为默认格式化工具
        "editor.defaultFormatter": "ms-python.black-formatter",
        // 保存时自动格式化
        "editor.formatOnSave": true
    },
    // 启用基本类型检查
    "python.analysis.typeCheckingMode": "basic",
    // 启用 Pylint 代码检查
    "pylint.enabled": true,
    // ===== 通用配置 =====
    // 在 VS Code 文件浏览器中隐藏以下文件
    "files.exclude": {
        // 隐藏 Python 缓存目录
        "**/__pycache__": true,
        // 隐藏 Python 编译文件
        "**/*.pyc": true
    },
    // 排除文件监视以提升性能
    "files.watcherExclude": {
        // 忽略 Git 相关目录
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true,
        // 忽略 Node.js 模块目录
        "**/node_modules/*/**": true,
        // 忽略 Python 虚拟环境目录
        "**/.venv/**": true,
        // 忽略 vendor 目录
        "**/vendor/**": true
    }
}