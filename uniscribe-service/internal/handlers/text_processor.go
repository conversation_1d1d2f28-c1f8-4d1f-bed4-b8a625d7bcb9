package handlers

import (
	"context"
	"fmt"
	"log"
	"math"
	"strings"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/sashabaranov/go-openai"
	"github.com/sashabaranov/go-openai/jsonschema"

	"uniscribe-service/internal/constants"
	openaipkg "uniscribe-service/pkg/openai"
)

const GPTModel = openai.GPT4oMini

// TextProcessor 接口定义了文本处理器应该实现的方法
type TextProcessor interface {
	Process(ctx context.Context, text string) (interface{}, error)
}

// SummaryResult 存储文本摘要结果
type SummaryResult struct {
	Explanation string `json:"explanation"`
	Output      string `json:"output"`
}

// OutlineResult 存储大纲生成结果
type OutlineResult struct {
	Explanation string `json:"explanation"`
	Output      string `json:"output"`
}

type QAResult struct {
	Explanation string   `json:"explanation"`
	Output      []QAPair `json:"output"`
}

type QAPair struct {
	Question string `json:"question"`
	Answer   string `json:"answer"`
}

// SummaryProcessor 实现了文本摘要
type SummaryProcessor struct{}

func (p *SummaryProcessor) Process(ctx context.Context, text string) (interface{}, error) {
	schema, err := jsonschema.GenerateSchemaForType(SummaryResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}
	// 实现文本摘要逻辑
	resp, err := openaipkg.GetClient().CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:       GPTModel,
		Temperature: 0.4,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: "You are a text processing assistant. Your task is: Summarize the text",
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please summarize this text:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "summary_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}
	var result SummaryResult
	err = schema.Unmarshal(resp.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}
	return result, nil
}

// OutlineProcessor 实现了大纲生成
type OutlineProcessor struct{}

func (p *OutlineProcessor) Process(ctx context.Context, text string) (interface{}, error) {
	schema, err := jsonschema.GenerateSchemaForType(OutlineResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}

	// 修改系统提示词，明确指定 markdown 格式要求
	systemPrompt := `You are a text processing assistant. Your task is to generate an outline in markdown format.
Follow these strict formatting rules:
1. Use only '#', '##', '###' for headings (no A., B., 1., 2.)
2. Each level should start with the appropriate number of '#'
3. Keep the structure simple and clear
4. Leave a blank line between each heading
Example format:
# Main Topic

## Subtopic 1

### Detail Point 1

### Detail Point 2

## Subtopic 2

### Detail Point 3`

	resp, err := openaipkg.GetClient().CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:       GPTModel,
		Temperature: 0.2,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: systemPrompt,
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please generate an outline in the original language of the text, strictly following the markdown format:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "outline_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}
	var result OutlineResult
	err = schema.Unmarshal(resp.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}
	return result, nil
}

// QAExtractionProcessor
type QAExtractionProcessor struct{}

func (p *QAExtractionProcessor) Process(ctx context.Context, text string) (interface{}, error) {
	schema, err := jsonschema.GenerateSchemaForType(QAResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}
	resp, err := openaipkg.GetClient().CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:       GPTModel,
		Temperature: 0.2,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: "You are a text processing assistant. Your task is: Generate Q&A",
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please generate Q&A:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "qa_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}
	var result QAResult
	err = schema.Unmarshal(resp.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}
	return result, nil
}

// ProcessTextTask 函数并行执行所有文本处理任务

type TextTaskProcessor interface {
	Process(ctx context.Context, task Task) error
}

type TextTaskProcessorImpl struct {
	resultSender ResultSender
	retryCount   int
	baseDelay    time.Duration
}

func NewTextTaskProcessor(resultSender ResultSender) TextTaskProcessor {
	return &TextTaskProcessorImpl{resultSender: resultSender, retryCount: 3, baseDelay: 5 * time.Second}
}

func (p *TextTaskProcessorImpl) Process(ctx context.Context, task Task) error {
	log.Printf("receive text task %v\n", task)

	// 1. 检查转录文本是否为空
	if strings.TrimSpace(task.TranscriptionText) == "" {
		err := fmt.Errorf("transcription text is empty for task %d", task.TaskID)
		log.Printf("Task validation failed: %v", err)

		// 更新状态为失败 - 使用 UpdateTaskStatus 上报错误
		statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
		if statusErr != nil {
			log.Printf("Failed to update task status to failed: %v", statusErr)
			return fmt.Errorf("failed to update task status: %w", statusErr)
		}
		return nil
	}

	// 2. 更新任务状态为处理中
	err := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusProcessing)
	if err != nil {
		log.Printf("Failed to update task status to processing: %v", err)
		// 不因为状态更新失败而终止任务处理
	}

	// 3. 选择处理器
	var processor TextProcessor
	switch task.TaskType {
	case constants.TaskTypeSummary:
		processor = &SummaryProcessor{}
	case constants.TaskTypeOutline:
		processor = &OutlineProcessor{}
	case constants.TaskTypeQAExtraction:
		processor = &QAExtractionProcessor{}
	default:
		err := fmt.Errorf("unknown task type: %v", task.TaskType)
		log.Printf("Task type validation failed: %v", err)

		// 更新状态为失败 - 使用 UpdateTaskStatus 上报错误
		statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
		if statusErr != nil {
			log.Printf("Failed to update task status to failed: %v", statusErr)
			return fmt.Errorf("failed to update task status: %w", statusErr)
		}
		return nil
	}

	// 4. 处理任务
	result, err := p.retryTask(ctx, task, processor)
	if err != nil {
		// 上报错误到 Sentry
		sentry.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "text_processor")
			scope.SetTag("task_type", fmt.Sprintf("%d", task.TaskType))
			scope.SetExtra("task_id", task.TaskID)
			scope.SetExtra("transcription_text_length", len(task.TranscriptionText))
			sentry.CaptureException(err)
		})

		// 更新状态为失败 - 使用 UpdateTaskStatus 上报错误
		statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
		if statusErr != nil {
			log.Printf("Failed to update task status to failed: %v", statusErr)
			return fmt.Errorf("failed to update task status: %w", statusErr)
		}
		return nil
	}

	// 5. 发送成功结果
	switch task.TaskType {
	case constants.TaskTypeSummary:
		err = p.resultSender.Send(&SummaryResultPayload{
			TaskID:   task.TaskID,
			TaskType: task.TaskType,
			Summary:  result.(SummaryResult).Output,
		})
	case constants.TaskTypeOutline:
		err = p.resultSender.Send(&OutlineResultPayload{
			TaskID:   task.TaskID,
			TaskType: task.TaskType,
			Outline:  result.(OutlineResult).Output,
		})
	case constants.TaskTypeQAExtraction:
		err = p.resultSender.Send(&QAResultPayload{
			TaskID:       task.TaskID,
			TaskType:     task.TaskType,
			QAExtraction: result.(QAResult).Output,
		})
	}

	if err != nil {
		log.Printf("Failed to send task result: %v", err)

		// 发送结果失败时，更新状态为失败 - 使用 UpdateTaskStatus 上报错误
		statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
		if statusErr != nil {
			log.Printf("Failed to update task status to failed: %v", statusErr)
			return fmt.Errorf("failed to update task status: %w", statusErr)
		}
		return nil
	}

	return nil
}

func (p *TextTaskProcessorImpl) retryTask(ctx context.Context, task Task, processor TextProcessor) (interface{}, error) {
	var result interface{}
	var err error

	for attempt := 0; attempt < p.retryCount; attempt++ {
		result, err = processor.Process(ctx, task.TranscriptionText)
		if err == nil {
			return result, nil
		}
		// 指数退避
		sleepTime := p.baseDelay * time.Duration(math.Pow(2, float64(attempt)))
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(sleepTime):
		}
	}
	return nil, fmt.Errorf("failed to process task after %d attempts: %w", p.retryCount, err)
}
