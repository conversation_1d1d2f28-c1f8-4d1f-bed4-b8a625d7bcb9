package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"uniscribe-service/internal/constants"
	"uniscribe-service/internal/service/speech/stt/providers"

	"github.com/getsentry/sentry-go"
)

// TaskStatusUpdatePayload 任务状态更新载荷
type TaskStatusUpdatePayload struct {
	TaskID int64                  `json:"taskId"`
	Status constants.TaskStatus   `json:"status"`
	Error  string                 `json:"error,omitempty"`
}

func (r TaskStatusUpdatePayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.Status == 0 {
		return fmt.Errorf("status is required")
	}
	return nil
}

// TranscriptionResultPayload
type TranscriptionResultPayload struct {
	TaskID           int64                      `json:"taskId"`
	TaskType         constants.TaskType         `json:"taskType"`
	Text             string                     `json:"transcriptionText"`
	Duration         float64                    `json:"duration"`
	DetectedLanguage string                     `json:"detectedLanguage"`
	Segments         []providers.UnifiedSegment `json:"segments"`
	ServiceProvider  string                     `json:"serviceProvider"`
}

func (r TranscriptionResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeTranscription {
		return fmt.Errorf("task_type must be transcription")
	}
	if r.Text == "" {
		return fmt.Errorf("The transcript is empty")
	}
	return nil
}



// SummaryResultPayload
type SummaryResultPayload struct {
	TaskID   int64              `json:"taskId"`
	TaskType constants.TaskType `json:"taskType"`
	Summary  string             `json:"summary"`
}

func (r SummaryResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeSummary {
		return fmt.Errorf("task_type must be summary")
	}
	if r.Summary == "" {
		return fmt.Errorf("summary is required")
	}
	return nil
}



// OutlineResultPayload
type OutlineResultPayload struct {
	TaskID   int64              `json:"taskId"`
	TaskType constants.TaskType `json:"taskType"`
	Outline  string             `json:"outline"`
}

func (r OutlineResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeOutline {
		return fmt.Errorf("task_type must be outline")
	}
	if r.Outline == "" {
		return fmt.Errorf("outline is required")
	}
	return nil
}

type QAResultPayload struct {
	TaskID       int64              `json:"taskId"`
	TaskType     constants.TaskType `json:"taskType"`
	QAExtraction []QAPair           `json:"qaExtraction"`
}

func (r QAResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeQAExtraction {
		return fmt.Errorf("task_type must be qa")
	}
	if len(r.QAExtraction) == 0 {
		return fmt.Errorf("qa_extraction is required")
	}
	return nil
}

// SpeakerDiarizationResultPayload 说话人识别结果
type SpeakerDiarizationResultPayload struct {
	TaskID              int64              `json:"taskId"`
	TaskType            constants.TaskType `json:"taskType"`
	DiarizationSegments interface{}        `json:"diarizationSegments"`
}

func (r SpeakerDiarizationResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeSpeakerDiarization {
		return fmt.Errorf("task_type must be speaker_diarization")
	}
	if r.DiarizationSegments == nil {
		return fmt.Errorf("diarization_segments is required")
	}
	return nil
}

// 任何任务类型失败，都可以用这个
type TaskFailedPyload struct {
	TaskID       int64              `json:"taskId"`
	TaskType     constants.TaskType `json:"taskType"`
	ErrorMessage string             `json:"errorMessage"`
}

func (r TaskFailedPyload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.ErrorMessage == "" {
		return fmt.Errorf("error_message is required")
	}
	return nil
}

type ResultPayload interface {
	Validate() error
}

type ResultSender interface {
	Send(result ResultPayload) error
	UpdateTaskStatus(taskID int64, status constants.TaskStatus, errorMsg ...string) error
}

type HTTPResultSender struct {
	client *http.Client
	host   string
}

func NewHTTPResultSender(host string) *HTTPResultSender {
	return &HTTPResultSender{
		host:   host,
		client: &http.Client{},
	}
}

func (s *HTTPResultSender) Send(result ResultPayload) error {
	apiUrl := s.host + "/tasks/result"
	if err := result.Validate(); err != nil {
		return fmt.Errorf("validation failed: %v", err)
	}

	requestBody, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %v", err)
	}

	req, err := http.NewRequest("POST", apiUrl, bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.client.Do(req)
	if err != nil {
		sentry.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "result_sender")
			scope.SetTag("operation", "send_result")
			scope.SetExtra("url", apiUrl)
			scope.SetExtra("payload_type", fmt.Sprintf("%T", result))
			sentry.CaptureException(err)
		})
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read response body: %v", err)
		}

		httpErr := fmt.Errorf("unexpected status code: %d, %s", resp.StatusCode, body)
		sentry.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "result_sender")
			scope.SetTag("operation", "send_result")
			scope.SetExtra("url", apiUrl)
			scope.SetExtra("status_code", resp.StatusCode)
			scope.SetExtra("response_body", string(body))
			scope.SetExtra("payload_type", fmt.Sprintf("%T", result))
			sentry.CaptureException(httpErr)
		})
		return httpErr
	}

	return nil
}

// UpdateTaskStatus 更新任务状态
func (s *HTTPResultSender) UpdateTaskStatus(taskID int64, status constants.TaskStatus, errorMsg ...string) error {
	payload := TaskStatusUpdatePayload{
		TaskID: taskID,
		Status: status,
	}

	// 如果有错误信息，添加到载荷中
	if len(errorMsg) > 0 && errorMsg[0] != "" {
		payload.Error = errorMsg[0]
	}

	if err := payload.Validate(); err != nil {
		return fmt.Errorf("invalid payload: %v", err)
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %v", err)
	}

	// 发送到 Backend 的状态更新端点
	url := s.host + "/tasks/status"
	resp, err := s.client.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		sentry.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "result_sender")
			scope.SetTag("operation", "update_status")
			scope.SetExtra("url", url)
			scope.SetExtra("task_id", taskID)
			scope.SetExtra("status", status)
			sentry.CaptureException(err)
		})
		return fmt.Errorf("failed to send status update: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read response body: %v", err)
		}

		httpErr := fmt.Errorf("status update failed: %d, %s", resp.StatusCode, body)
		sentry.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "result_sender")
			scope.SetTag("operation", "update_status")
			scope.SetExtra("url", url)
			scope.SetExtra("task_id", taskID)
			scope.SetExtra("status", status)
			scope.SetExtra("status_code", resp.StatusCode)
			scope.SetExtra("response_body", string(body))
			sentry.CaptureException(httpErr)
		})
		return httpErr
	}

	return nil
}
