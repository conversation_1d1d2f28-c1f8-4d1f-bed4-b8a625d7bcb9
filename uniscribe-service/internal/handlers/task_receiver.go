package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"

	"uniscribe-service/internal/constants"
)

type Task struct {
	TaskID                   int64              `json:"taskId"`
	FileUrl                  string             `json:"fileUrl"`
	TaskType                 constants.TaskType `json:"taskType"`
	TranscriptionFileId      int64              `json:"transcriptionFileId"`
	TranscriptionText        string             `json:"transcriptionText"`
	FileDuration             float64            `json:"fileDuration"`
	Language                 string             `json:"language"`
	LanguageCode             string             `json:"languageCode"`
	TranscriptionType        string             `json:"transcriptionType,omitempty"`
	RequestedServiceProvider string             `json:"requestedServiceProvider,omitempty"`
}

// String 实现 fmt.Stringer 接口，返回适合日志输出的简化字符串，截断长文本字段
func (t Task) String() string {
	// 截断长文本字段，只显示长度和前面一部分
	transcriptionPreview := ""
	if len(t.TranscriptionText) > 0 {
		if len(t.TranscriptionText) <= 100 {
			transcriptionPreview = t.TranscriptionText
		} else {
			transcriptionPreview = t.TranscriptionText[:97] + "..."
		}
	}

	return fmt.Sprintf("Task{ID:%d, Type:%v, FileID:%d, Duration:%.1fs, Lang:%s/%s, "+
		"Provider:%s, TranscriptionType:%s, TranscriptionText:[%d chars]%q}",
		t.TaskID, t.TaskType, t.TranscriptionFileId, t.FileDuration,
		t.Language, t.LanguageCode, t.RequestedServiceProvider, t.TranscriptionType,
		len(t.TranscriptionText), transcriptionPreview)
}

type ErrorNoTask struct{}

func (e ErrorNoTask) Error() string {
	return "no task available"
}

func FetchNextTask(apiUrl string) (Task, error) {
	// 从 api 获取任务
	var task Task
	resp, err := http.Post(apiUrl, "application/json", nil)
	if err != nil {
		return task, fmt.Errorf("failed to fetch task: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return task, fmt.Errorf("failed to read response body: %v", err)
		}

		if resp.StatusCode == http.StatusNotFound {
			log.Printf("%s", body)
			return task, ErrorNoTask{}
		}
		return task, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	if err := json.NewDecoder(resp.Body).Decode(&task); err != nil {
		return task, fmt.Errorf("failed to decode response: %v", err)
	}

	return task, nil
}
