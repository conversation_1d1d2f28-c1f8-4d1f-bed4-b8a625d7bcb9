package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"uniscribe-service/internal/config"
	"uniscribe-service/internal/constants"
	"uniscribe-service/internal/service/speech/stt"
	"uniscribe-service/internal/service/speech/stt/providers"

	"github.com/getsentry/sentry-go"
)

// AudioTaskProcessor 接口定义了音频处理器应该实现的方法
type AudioTaskProcessor interface {
	Process(ctx context.Context, task Task) error
}

// AudioProcessorImpl 实现了 AudioProcessor 接口
type AudioTaskProcessorImpl struct {
	resultSender ResultSender
}

// 添加类型别名
type sttTask = providers.Task

func NewAudioTaskProcessor(resultSender ResultSender) AudioTaskProcessor {
	return &AudioTaskProcessorImpl{resultSender: resultSender}
}

func (p *AudioTaskProcessorImpl) Process(ctx context.Context, task Task) error {
	// 1. 更新任务状态为处理中
	err := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusProcessing)
	if err != nil {
		log.Printf("Failed to update task status to processing: %v", err)
		// 不因为状态更新失败而终止任务处理
	}

	// 2. 处理转录
	payload, err := p.processTranscription(ctx, task)
	if err != nil {
		return p.sendTaskFailedResult(task, err)
	}

	// 3. 发送转录结果（这会将状态设置为 completed）
	err = p.resultSender.Send(payload)
	if err != nil {
		return p.sendTaskFailedResult(task, err)
	}

	// 4. 创建后续任务
	err = p.createSubsequentTasks(ctx, task)
	if err != nil {
		return p.sendTaskFailedResult(task, err)
	}
	return nil
}

func (p *AudioTaskProcessorImpl) processTranscription(ctx context.Context, task Task) (*TranscriptionResultPayload, error) {
	// 使用 STT Service 单例
	sttService := stt.GetService()

	// 在使用时转换类型
	handlerTask := task // 原始的 Task 类型
	sttTaskInstance := providers.Task{
		TaskID:                   handlerTask.TaskID,
		FileUrl:                  handlerTask.FileUrl,
		TaskType:                 handlerTask.TaskType,
		TranscriptionFileId:      handlerTask.TranscriptionFileId,
		TranscriptionText:        handlerTask.TranscriptionText,
		FileDuration:             handlerTask.FileDuration,
		Language:                 handlerTask.Language,
		LanguageCode:             handlerTask.LanguageCode,
		TranscriptionType:        handlerTask.TranscriptionType,
		RequestedServiceProvider: handlerTask.RequestedServiceProvider,
	}

	// 使用支持 failover 的转录方法
	transcriptionResult, err := sttService.TranscribeWithFailover(ctx, sttTaskInstance)
	if err != nil {
		sentry.CaptureException(err)
		return nil, fmt.Errorf("failed to transcribe audio: %w", err)
	}

	payload := &TranscriptionResultPayload{
		TaskID:           task.TaskID,
		TaskType:         constants.TaskTypeTranscription,
		Text:             transcriptionResult.Text,
		DetectedLanguage: transcriptionResult.DetectedLanguage,
		Segments:         transcriptionResult.Segments,
		ServiceProvider:  string(transcriptionResult.UsedModel),
	}

	return payload, nil
}

func (p *AudioTaskProcessorImpl) createSubsequentTasks(ctx context.Context, task Task) error {
	apiUrl := config.Cfg.WebBackendHost + "/tasks/text"
	body := struct {
		TranscriptionFileId int64 `json:"transcriptionFileId"`
	}{
		TranscriptionFileId: task.TranscriptionFileId,
	}

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("failed to marshal body: %w", err)
	}

	resp, err := http.Post(apiUrl, "application/json", bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to post tasks/text: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to post tasks/text: %w", err)
	}

	return nil
}

func (p *AudioTaskProcessorImpl) sendTaskFailedResult(task Task, err error) error {
	// 1. 更新任务状态为失败
	statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
	if statusErr != nil {
		log.Printf("Failed to update task status to failed: %v", statusErr)
	}

	// 2. 添加错误追踪
	sentry.WithScope(func(scope *sentry.Scope) {
		scope.SetExtra("taskID", task.TaskID)
		scope.AddBreadcrumb(&sentry.Breadcrumb{
			Message: err.Error(),
			Level:   sentry.LevelError,
		}, 0)
		sentry.CaptureException(err)
	})

	// 3. 发送失败结果（保持向后兼容）
	payload := &TaskFailedPyload{
		TaskID:       task.TaskID,
		TaskType:     constants.TaskTypeTranscription,
		ErrorMessage: err.Error(),
	}
	return p.resultSender.Send(payload)
}
