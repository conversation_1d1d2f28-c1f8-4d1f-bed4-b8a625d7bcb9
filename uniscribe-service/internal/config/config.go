package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	WebBackendHost    string `json:"web_backend_host"`
	DashscopeApiKey   string `json:"dashscope_api_key"`
	ProxyURLStr       string `json:"proxy_url"`
	OpenaiApiKey      string `json:"openai_api_key"`
	ReplicateApiToken string `json:"replicate_api_token"`
	FalApiKey         string `json:"fal_api_key"`
	DeepInfraApiKey   string `json:"deepinfra_api_key"`
	RedisHost         string `json:"redis_host"`
	RedisPort         string `json:"redis_port"`
	RedisPassword     string `json:"redis_password"`
	RedisSSL          bool   `json:"redis_ssl"`
}

var Cfg Config

func LoadConfig() {
	err := godotenv.Load()
	if err != nil {
		log.Fatalf("Error loading .env file: %v", err)
	}
	log.Printf("Config loaded")

	webBackendHost := os.Getenv("WEB_BACKEND_HOST")
	if webBackendHost == "" {
		log.Fatalf("WEB_BACKEND_HOST environment variable is required")
	}

	dashscopeApiKey := os.Getenv("DASHSCOPE_API_KEY")
	if dashscopeApiKey == "" {
		log.Fatalf("DASHSCOPE_API_KEY environment variable is required")
	}

	proxyURLStr := os.Getenv("PROXY_URL")
	if proxyURLStr == "" {
		log.Printf("No proxy URL provided, using default http client")
	}

	openaiApiKey := os.Getenv("OPENAI_API_KEY")
	if openaiApiKey == "" {
		log.Fatalf("OPENAI_API_KEY environment varialbe is required")
	}

	replicateApiToken := os.Getenv("REPLICATE_API_TOKEN")
	if replicateApiToken == "" {
		log.Fatalf("REPLICATE_API_TOKEN environment variable is required")
	}

	falApiKey := os.Getenv("FAL_API_KEY")
	if falApiKey == "" {
		log.Fatalf("FAL_KEY environment variable is required")
	}

	deepInfraApiKey := os.Getenv("DEEPINFRA_API_KEY")
	if deepInfraApiKey == "" {
		log.Printf("DEEPINFRA_API_KEY environment variable is not set, DeepInfra provider will not be available")
		deepInfraApiKey = ""
	}

	// Redis 配置 - 必须显式设置，不提供默认值
	redisHost := os.Getenv("REDIS_HOST")
	if redisHost == "" {
		log.Fatal("REDIS_HOST environment variable is required")
	}

	redisPort := os.Getenv("REDIS_PORT")
	if redisPort == "" {
		log.Fatal("REDIS_PORT environment variable is required")
	}

	redisPassword := os.Getenv("REDIS_PASSWORD")
	// Redis 密码可以为空，但如果需要认证必须设置

	// Redis SSL 配置
	redisSSL := false
	if sslStr := os.Getenv("REDIS_SSL"); sslStr != "" {
		redisSSL = sslStr == "true" || sslStr == "1"
	}

	Cfg = Config{
		WebBackendHost:    webBackendHost,
		DashscopeApiKey:   dashscopeApiKey,
		ProxyURLStr:       proxyURLStr,
		OpenaiApiKey:      openaiApiKey,
		ReplicateApiToken: replicateApiToken,
		FalApiKey:         falApiKey,
		DeepInfraApiKey:   deepInfraApiKey,
		RedisHost:         redisHost,
		RedisPort:         redisPort,
		RedisPassword:     redisPassword,
		RedisSSL:          redisSSL,
	}
}
