package stt

import (
	"context"
	"fmt"
	"log"
	"sync"

	"uniscribe-service/internal/service/speech/stt/providers"

	"github.com/getsentry/sentry-go"
)

// sttService 实现了 Service 接口
type sttService struct {
	providers map[providers.AudioModel]providers.Provider
	mu        sync.RWMutex

	// failover 配置
	primaryModel providers.AudioModel
	backupModels []providers.AudioModel
}

var (
	defaultService *sttService
	once           sync.Once
)

// GetService 获取全局单例
func GetService() *sttService {
	once.Do(func() {
		defaultService = &sttService{
			providers: make(map[providers.AudioModel]providers.Provider),
			// 设置默认的主备模型（备用模型是已注册模型的子集）
			primaryModel: providers.FalWizper,
			backupModels: []providers.AudioModel{
				providers.DeepInfraWhisperTurbo,
				providers.ReplicateWhisperX,
				providers.FalWhisper,
			},
		}
		defaultService.initializeAllProviders()
	})
	return defaultService
}

// RegisterProvider 注册一个新的语音识别提供者
func (s *sttService) RegisterProvider(model providers.AudioModel, provider providers.Provider) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if provider == nil {
		return fmt.Errorf("provider cannot be nil")
	}

	if _, exists := s.providers[model]; exists {
		return fmt.Errorf("provider for model %s already registered", model)
	}

	s.providers[model] = provider
	return nil
}

// GetProvider 获取指定名称的提供者
func (s *sttService) GetProvider(name providers.AudioModel) (providers.Provider, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	provider, exists := s.providers[name]
	if !exists {
		sentry.CaptureMessage(fmt.Sprintf("Provider %s not found", name))
		return nil, fmt.Errorf("provider %s not found", name)
	}

	return provider, nil
}

// Transcribe 使用指定的提供者执行语音识别
func (s *sttService) Transcribe(ctx context.Context, providerName providers.AudioModel, task providers.Task) (providers.TranscriptionResult, error) {
	provider, err := s.GetProvider(providerName)
	if err != nil {
		return providers.TranscriptionResult{}, fmt.Errorf("failed to get provider: %w", err)
	}

	result, err := provider.Transcribe(ctx, task)
	if err != nil {
		return providers.TranscriptionResult{}, fmt.Errorf("transcription failed with provider %s: %w", providerName, err)
	}

	return result, nil
}

// TranscribeWithFailover 执行转录，支持故障转移
func (s *sttService) TranscribeWithFailover(ctx context.Context, task providers.Task) (providers.TranscriptionResult, error) {
	// 使用默认的模型配置
	primaryModel := s.primaryModel
	backupModels := s.backupModels

	// 如果有请求的服务提供商，优先使用请求的服务提供商
	if task.RequestedServiceProvider != "" {
		requestServiceProvider := providers.AudioModel(task.RequestedServiceProvider)
		// 检查请求的服务提供商是否有效
		if _, err := s.GetProvider(requestServiceProvider); err == nil {
			primaryModel = requestServiceProvider
		}
	}

	// 首先尝试主模型
	result, err := s.tryTranscribe(ctx, primaryModel, task)
	if err == nil {
		// 设置实际使用的模型
		result.UsedModel = primaryModel
		return result, nil
	}

	// 主模型失败，记录错误
	firstErr := fmt.Errorf("primary model %s failed: %w, task: %v", primaryModel, err, task)
	log.Printf("Transcription failed with primary model %s: %v, task: %v", primaryModel, err, task)
	sentry.CaptureException(firstErr)

	// 依次尝试备用模型
	for _, model := range backupModels {
		result, err := s.tryTranscribe(ctx, model, task)
		if err == nil {
			// 设置实际使用的模型
			result.UsedModel = model
			return result, nil
		}
	}

	return providers.TranscriptionResult{}, fmt.Errorf("all models failed, first error: %w", firstErr)
}

// tryTranscribe 尝试使用指定模型进行转录
func (s *sttService) tryTranscribe(ctx context.Context, model providers.AudioModel, task providers.Task) (providers.TranscriptionResult, error) {
	provider, err := s.GetProvider(model)
	if err != nil {
		return providers.TranscriptionResult{}, err
	}

	return provider.Transcribe(ctx, task)
}

func (s *sttService) initializeAllProviders() {
	// 注册所有可用的模型（与备用模型配置解耦）
	allAvailableModels := []providers.AudioModel{
		// 基础转录模型
		// providers.OpenAIWhisper,
		// providers.AliyunParaformer,
		providers.FalWhisper,
		providers.FalWizper,
		providers.ReplicateWhisperX,
		providers.DeepInfraWhisperTurbo,
		providers.DeepInfraWhisper,

		// 说话人识别模型
		providers.FalWhisperDiarization,
		providers.ReplicateWhisperXDiarization,
		providers.ReplicateWhisperDiarization,

		// Word 级别模型
		providers.DeepInfraWhisperTurboWord,
		providers.DeepInfraWhisperWord,
	}

	// 注册所有模型
	for _, model := range allAvailableModels {
		if provider, err := NewProvider(model); err == nil {
			s.RegisterProvider(model, provider)
		}
	}

	// 验证备用模型配置的有效性
	s.validateBackupModels()
}

// validateBackupModels 验证备用模型是否都已注册
func (s *sttService) validateBackupModels() {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 检查主模型
	if _, exists := s.providers[s.primaryModel]; !exists {
		sentry.CaptureMessage(fmt.Sprintf("Primary model %s is not registered", s.primaryModel))
	}

	// 检查备用模型
	for _, model := range s.backupModels {
		if _, exists := s.providers[model]; !exists {
			sentry.CaptureMessage(fmt.Sprintf("Backup model %s is not registered", model))
		}
	}
}

// GetRegisteredModels 获取所有已注册的模型列表
func (s *sttService) GetRegisteredModels() []providers.AudioModel {
	s.mu.RLock()
	defer s.mu.RUnlock()

	models := make([]providers.AudioModel, 0, len(s.providers))
	for model := range s.providers {
		models = append(models, model)
	}
	return models
}

// GetBackupModels 获取备用模型列表
func (s *sttService) GetBackupModels() []providers.AudioModel {
	return s.backupModels
}

// GetPrimaryModel 获取主模型
func (s *sttService) GetPrimaryModel() providers.AudioModel {
	return s.primaryModel
}
