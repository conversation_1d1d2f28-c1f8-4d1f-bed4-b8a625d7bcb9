package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	replicatepkg "uniscribe-service/pkg/replicate"

	"github.com/replicate/replicate-go"
)

type ReplicateWhisperDiarizationProvider struct {
	client  *replicate.Client
	model   string
	version string
}

func NewReplicateWhisperDiarizationProvider() *ReplicateWhisperDiarizationProvider {
	return &ReplicateWhisperDiarizationProvider{
		client:  replicatepkg.GetClient(),
		model:   "thomasmol/whisper-diarization",
		version: "1495a9cddc83b2203b0d8d3516e38b80fd1572ebc4bc5700ac1da56a9b3ed886",
	}
}

func (r *ReplicateWhisperDiarizationProvider) Name() string {
	return string(ReplicateWhisperDiarization)
}

type ReplicateWhisperDiarizationOutput struct {
	Language string                               `json:"language"`
	Segments []ReplicateWhisperDiarizationSegment `json:"segments"`
}

type ReplicateWhisperDiarizationSegment struct {
	Start      float64                           `json:"start"`
	End        float64                           `json:"end"`
	Text       string                            `json:"text"`
	Speaker    string                            `json:"speaker"`
	Duration   float64                           `json:"duration"`
	AvgLogprob float64                           `json:"avg_logprob"`
	Words      []ReplicateWhisperDiarizationWord `json:"words"`
}

type ReplicateWhisperDiarizationWord struct {
	Start       float64 `json:"start"`
	End         float64 `json:"end"`
	Word        string  `json:"word"`
	Speaker     string  `json:"speaker"`
	Probability float64 `json:"probability"`
}

func (r *ReplicateWhisperDiarizationProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	input := replicate.PredictionInput{
		"file_url":       task.FileUrl,
		"translate":      false,
		"group_segments": true,
	}

	// 设置语言参数
	if task.LanguageCode != "" {
		if task.LanguageCode == "zh_tw" {
			input["language"] = "zh"
		} else {
			input["language"] = task.LanguageCode
		}
	} else {
		input["language"] = "en" // 默认英语
	}

	var predictionOutput replicate.PredictionOutput
	var err error

	if r.version != "" {
		predictionOutput, err = r.client.Run(ctx, r.model+":"+r.version, input, nil)
	} else {
		predictionOutput, err = r.client.Run(ctx, r.model, input, nil)
	}

	if err != nil {
		return TranscriptionResult{}, err
	}

	text, unifiedSegments, detectedLanguage, err := r.convertToUnified(predictionOutput)
	if err != nil {
		return TranscriptionResult{}, err
	}

	return TranscriptionResult{
		Text:             text,
		Segments:         unifiedSegments,
		DetectedLanguage: detectedLanguage,
		UsedModel:        AudioModel(r.Name()),
	}, nil
}

func (r *ReplicateWhisperDiarizationProvider) convertToUnified(predictionOutput replicate.PredictionOutput) (string, []UnifiedSegment, string, error) {
	var output ReplicateWhisperDiarizationOutput
	jsonData, err := json.Marshal(predictionOutput)
	if err != nil {
		return "", nil, "", fmt.Errorf("无法序列化预测输出: %v", err)
	}
	err = json.Unmarshal(jsonData, &output)
	if err != nil {
		return "", nil, "", fmt.Errorf("无法解析预测输出: %v", err)
	}

	var text strings.Builder
	var unifiedSegments []UnifiedSegment

	for i, segment := range output.Segments {
		text.WriteString(segment.Text)

		unifiedSegment := UnifiedSegment{
			ID:        i,
			StartTime: segment.Start,
			EndTime:   segment.End,
			Text:      segment.Text,
			Speaker:   segment.Speaker, // 直接使用模型返回的说话人信息
		}

		// 转换 words 信息
		for _, word := range segment.Words {
			unifiedWord := Word{
				StartTime: word.Start,
				EndTime:   word.End,
				Text:      word.Word,
				Speaker:   word.Speaker, // 词级别的说话人信息
			}
			unifiedSegment.Words = append(unifiedSegment.Words, unifiedWord)
		}

		unifiedSegments = append(unifiedSegments, unifiedSegment)
	}

	return text.String(), unifiedSegments, output.Language, nil
}
