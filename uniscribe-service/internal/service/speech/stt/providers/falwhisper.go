package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"uniscribe-service/pkg/fal"
)

const falWhisperAppID = "fal-ai/whisper"

type FalWhisperProvider struct {
	client            *fal.Client
	enableDiarization bool // 是否启用说话人识别
}

type FalWhisperChunk struct {
	Timestamp []float64 `json:"timestamp"`
	Text      string    `json:"text"`
	Speaker   string    `json:"speaker,omitempty"`
}

type FalWhisperResult struct {
	Text                string               `json:"text"`
	Chunks              []FalWhisperChunk    `json:"chunks"`
	InferredLanguages   []string             `json:"inferred_languages"`
	DiarizationSegments []DiarizationSegment `json:"diarization_segments,omitempty"`
}

type DiarizationSegment struct {
	Timestamp []float64 `json:"timestamp"`
	Speaker   string    `json:"speaker"`
}

func NewFalWhisperProvider() *FalWhisperProvider {
	return &FalWhisperProvider{
		client:            fal.GetClient(),
		enableDiarization: false,
	}
}

// NewFalWhisperDiarizationProvider 创建一个启用说话人识别的 FAL Whisper provider
func NewFalWhisperDiarizationProvider() *FalWhisperProvider {
	return &FalWhisperProvider{
		client:            fal.GetClient(),
		enableDiarization: true,
	}
}

func (f *FalWhisperProvider) Name() string {
	if f.enableDiarization {
		return string(FalWhisperDiarization)
	}
	return string(FalWhisper)
}

func (f *FalWhisperProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	payload := map[string]interface{}{
		"audio_url":   task.FileUrl,
		"task":        "transcribe",
		"diarize":     f.enableDiarization,
		"chunk_level": "segment",
		"version":     "3",
		"batch_size":  64,
	}

	// 只有在 LanguageCode 不为空时才添加 language 字段
	if task.LanguageCode != "" {
		if task.LanguageCode == "zh_tw" {
			payload["language"] = "zh"
		} else {
			payload["language"] = task.LanguageCode
		}
	}

	// Submit request using the generic client
	resp, err := f.client.QueueRequest(falWhisperAppID, payload)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to submit transcription request: %w", err)
	}

	result, err := f.pollForResult(ctx, resp.RequestID)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to get transcription result: %w", err)
	}

	segments := f.convertToUnifiedSegments(result)

	transcriptionResult := TranscriptionResult{
		Text:             result.Text,
		Segments:         segments,
		DetectedLanguage: strings.Join(result.InferredLanguages, ","),
		UsedModel:        AudioModel(f.Name()),
	}

	// 如果启用了说话人识别，添加相关数据
	if f.enableDiarization && len(result.DiarizationSegments) > 0 {
		transcriptionResult.DiarizationSegments = result.DiarizationSegments
	}

	return transcriptionResult, nil
}

func (f *FalWhisperProvider) pollForResult(ctx context.Context, requestID string) (*FalWhisperResult, error) {
	// 使用通用的轮询函数
	resultBytes, err := pollFalResult(ctx, f.client, falWhisperAppID, requestID)
	if err != nil {
		return nil, err
	}

	// 解析结果
	var result FalWhisperResult
	if err := json.Unmarshal(resultBytes, &result); err != nil {
		return nil, fmt.Errorf("failed to parse transcription result: %w", err)
	}

	log.Printf("FalWhisper transcription completed, text length: %d characters, segments: %d", len(result.Text), len(result.Chunks))
	return &result, nil
}

func (f *FalWhisperProvider) convertToUnifiedSegments(result *FalWhisperResult) []UnifiedSegment {
	segments := make([]UnifiedSegment, len(result.Chunks))
	for i, chunk := range result.Chunks {
		segments[i] = UnifiedSegment{
			ID:        i,
			StartTime: chunk.Timestamp[0],
			EndTime:   chunk.Timestamp[1],
			Text:      chunk.Text,
			Speaker:   chunk.Speaker, // 直接使用 FAL Whisper 返回的说话人信息
		}
	}
	return segments
}
