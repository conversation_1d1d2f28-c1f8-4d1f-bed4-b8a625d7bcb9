package providers

import (
	"context"
	"errors"
	"fmt"
	"log"

	"uniscribe-service/pkg/dashscope"

	"github.com/devinyf/dashscopego"
	"github.com/devinyf/dashscopego/paraformer"
)

// ParaformerProvider 实现了 Provider 接口
type ParaformerProvider struct {
	client *dashscopego.TongyiClient
}

// NewParaformerProvider 创建一个新的 Paraformer provider
func NewParaformerProvider() *ParaformerProvider {
	return &ParaformerProvider{
		client: dashscope.GetClient(),
	}
}

// Name 返回 provider 的名称
func (p *ParaformerProvider) Name() string {
	return string(AliyunParaformer)
}

// Transcribe 实现语音识别
func (p *ParaformerProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	transcripts, err := p.transcribe(ctx, task.FileUrl)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to transcribe audio: %w", err)
	}

	if len(transcripts) == 0 {
		return TranscriptionResult{}, errors.New("no transcription results")
	}

	transcript := transcripts[0]
	unifiedSegments := p.convertParaformerToUnified(transcript.Sentences)

	return TranscriptionResult{
		Text:     transcript.Text,
		Segments: unifiedSegments,
	}, nil
}

// transcribe 调用 Paraformer API 进行转录
func (p *ParaformerProvider) transcribe(ctx context.Context, fileUrl string) ([]paraformer.Transcript, error) {
	log.Printf("filePath: %s", fileUrl)
	req := &paraformer.AsyncTaskRequest{
		Model: dashscope.ParaformerV2,
		Input: paraformer.AsyncInput{
			FileURLs: []string{fileUrl},
		},
		Download: true, // 是否下载异步任务结果
	}

	resp, err := p.client.CreateVoiceFileToTextGeneration(ctx, req)
	if err != nil {
		return nil, err
	}

	if len(resp.FileResults) == 0 {
		return nil, errors.New("no file results")
	}

	return resp.FileResults[0].Transcripts, nil
}

// convertParaformerToUnified 将 Paraformer 的结果转换为统一格式
func (p *ParaformerProvider) convertParaformerToUnified(paraformerData []paraformer.Sentence) []UnifiedSegment {
	unified := make([]UnifiedSegment, len(paraformerData))
	for i, sent := range paraformerData {
		words := make([]Word, len(sent.Words))
		for j, w := range sent.Words {
			words[j] = Word{
				StartTime:   float64(w.BeginTime) / 1000, // 转换为秒
				EndTime:     float64(w.EndTime) / 1000,
				Text:        w.Text,
				Punctuation: w.Punctuation,
			}
		}
		unified[i] = UnifiedSegment{
			ID:        sent.SentenceID,
			StartTime: float64(sent.BeginTime) / 1000,
			EndTime:   float64(sent.EndTime) / 1000,
			Text:      sent.Text,
			Words:     words,
		}
	}
	return unified
}
