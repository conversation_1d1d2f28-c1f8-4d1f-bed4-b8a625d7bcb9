package providers

import (
	"context"

	"uniscribe-service/internal/constants"
)

type AudioModel string

const (
	OpenAIWhisper                AudioModel = "openai/whisper"                 // OpenAI 的 Whisper
	AliyunParaformer             AudioModel = "aliyun/paraformer"              // 阿里云的 Paraformer
	ReplicateWhisperX            AudioModel = "replicate/whisperx"             // Replicate 平台的 WhisperX
	ReplicateWhisperXDiarization AudioModel = "replicate/whisperx-diarization" // Replicate 平台的 WhisperX (说话人识别版本)
	ReplicateWhisperDiarization  AudioModel = "replicate/whisper-diarization"  // Replicate 平台的 Whisper Diarization (内置说话人识别)
	FalWhisper                   AudioModel = "fal-ai/whisper"                 // Fal.ai 平台的 Whisper
	FalWizper                    AudioModel = "fal-ai/wizper"                  // Fal.ai 平台的 Wizper
	FalWhisperDiarization        AudioModel = "fal-ai/whisper-diarization"     // Fal.ai 平台的 Whisper (说话人识别版本)
	DeepInfraWhisperTurbo        AudioModel = "deepinfra/whisper-turbo"        // DeepInfra 平台的 Whisper-Large-V3-Turbo
	DeepInfraWhisper             AudioModel = "deepinfra/whisper"              // DeepInfra 平台的 Whisper-Large-V3
	// Word 级别的 DeepInfra 模型，用于说话人识别等需要词级时间戳的场景
	DeepInfraWhisperTurboWord AudioModel = "deepinfra/whisper-turbo-word" // DeepInfra Whisper-Large-V3-Turbo (Word级别)
	DeepInfraWhisperWord      AudioModel = "deepinfra/whisper-word"       // DeepInfra Whisper-Large-V3 (Word级别)
)

// Word 表示单个词的时间戳和文本信息
type Word struct {
	StartTime   float64 `json:"start_time"`
	EndTime     float64 `json:"end_time"`
	Text        string  `json:"text"`
	Punctuation string  `json:"punctuation,omitempty"`
	Speaker     string  `json:"speaker,omitempty"` // 说话人标识
}

// UnifiedSegment 表示语音片段
type UnifiedSegment struct {
	ID        int     `json:"id"`
	StartTime float64 `json:"start_time"`
	EndTime   float64 `json:"end_time"`
	Text      string  `json:"text"`
	Speaker   string  `json:"speaker,omitempty"` // 说话人标识
	Words     []Word  `json:"words,omitempty"`
}

// Task 定义转录任务
type Task struct {
	TaskID                   int64              `json:"taskId"`
	FileUrl                  string             `json:"fileUrl"`
	TaskType                 constants.TaskType `json:"taskType"`
	TranscriptionFileId      int64              `json:"transcriptionFileId"`
	TranscriptionText        string             `json:"transcriptionText"`
	FileDuration             float64            `json:"fileDuration"`
	Language                 string             `json:"language"`
	LanguageCode             string             `json:"languageCode"`
	TranscriptionType        string             `json:"transcriptionType,omitempty"`
	RequestedServiceProvider string             `json:"requestedServiceProvider,omitempty"`
}

// TranscriptionResult 包含语音识别的结果
type TranscriptionResult struct {
	Text                string           `json:"text"`
	UsedModel           AudioModel       `json:"used_model"`
	Segments            []UnifiedSegment `json:"segments"`
	DetectedLanguage    string           `json:"detected_language"`
	DiarizationSegments interface{}      `json:"diarization_segments,omitempty"` // 说话人识别分段数据
}

// TranscriptionOptions 包含语音识别的配置选项
type TranscriptionOptions struct {
	Language string `json:"language,omitempty"`  // 目标语言
	TaskType string `json:"task_type,omitempty"` // 任务类型（实时/离线）
	Model    string `json:"model,omitempty"`     // 使用的模型
}

// Provider 定义了语音识别提供者需要实现的接口
type Provider interface {
	// Transcribe 执行语音识别
	Transcribe(ctx context.Context, task Task) (TranscriptionResult, error)

	// Name 返回提供者的名称
	Name() string
}

// Service 定义了 STT 服务的接口
type Service interface {
	// RegisterProvider 注册一个新的语音识别提供者
	RegisterProvider(provider Provider) error

	// GetProvider 获取指定名称的提供者
	GetProvider(name string) (Provider, error)

	// Transcribe 使用指定的提供者执行语音识别
	Transcribe(ctx context.Context, providerName string, task Task) (TranscriptionResult, error)
}
