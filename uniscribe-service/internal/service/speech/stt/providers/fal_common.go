package providers

import (
	"context"
	"fmt"
	"log"
	"time"

	"uniscribe-service/pkg/fal"
)

// pollFalResult 通用的 Fal 轮询函数
func pollFalResult(ctx context.Context, client *fal.Client, appID, requestID string) ([]byte, error) {
	const (
		pollInterval = 2 * time.Second  // 轮询间隔：2秒
		maxTimeout   = 10 * time.Minute // 最大超时时间：10分钟
		maxRetries   = 3                // 最大重试次数
	)

	// 创建带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, maxTimeout)
	defer cancel()

	ticker := time.NewTicker(pollInterval)
	defer ticker.Stop()

	var retryCount int

	log.Printf("Starting to poll task status, request ID: %s", requestID)

	// 用于取消远程请求的函数
	cancelRemoteRequest := func(reason string) {
		log.Printf("Attempting to cancel remote request %s, reason: %s", requestID, reason)
		if err := client.CancelRequest(appID, requestID); err != nil {
			log.Printf("Failed to cancel remote request: %v", err)
		} else {
			log.Printf("Remote request cancelled: %s", requestID)
		}
	}

	for {
		select {
		case <-timeoutCtx.Done():
			if timeoutCtx.Err() == context.DeadlineExceeded {
				cancelRemoteRequest("task timeout")
				return nil, fmt.Errorf("transcription task timeout (10 minutes), request ID: %s", requestID)
			}
			// 上下文被取消（例如用户主动停止）
			cancelRemoteRequest("context cancelled")
			return nil, timeoutCtx.Err()

		case <-ticker.C:
			// 检查任务状态
			status, err := client.GetRequestStatus(appID, requestID)
			if err != nil {
				retryCount++
				log.Printf("Failed to get task status (retry %d/%d): %v", retryCount, maxRetries, err)

				if retryCount >= maxRetries {
					cancelRemoteRequest("retry limit exceeded")
					return nil, fmt.Errorf("failed to get task status after %d retries: %w", maxRetries, err)
				}
				continue // 继续下次轮询
			}

			// 重置重试计数
			retryCount = 0

			log.Printf("Task status: %s (request ID: %s)", status.Status, requestID)

			switch status.Status {
			case "COMPLETED":
				// 任务完成，获取结果
				resultBytes, err := client.GetRequestResult(appID, requestID)
				if err != nil {
					return nil, fmt.Errorf("failed to get transcription result: %w", err)
				}

				log.Printf("Transcription completed, request ID: %s", requestID)
				return resultBytes, nil

			case "IN_PROGRESS", "IN_QUEUE":
				// 任务进行中或在队列中，继续轮询
				continue

			default:
				// 按照 Fal 文档，只有上述3种状态，如果出现其他状态可能是API错误
				log.Printf("Unexpected task status: %s, continuing polling", status.Status)
				continue
			}
		}
	}
}
