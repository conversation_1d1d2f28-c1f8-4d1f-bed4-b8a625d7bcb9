package providers

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"

	"uniscribe-service/internal/config"

	"github.com/sashabaranov/go-openai"
)

// WhisperProvider 实现了 Provider 接口
type WhisperProvider struct {
	client *openai.Client
	model  string
}

// NewWhisperProvider 创建一个新的 Whisper provider
func NewWhisperProvider() *WhisperProvider {
	return &WhisperProvider{
		client: openai.NewClient(config.Cfg.OpenaiApiKey),
		model:  openai.Whisper1,
	}
}

// Name 返回 provider 的名称
func (w *WhisperProvider) Name() string {
	return string(OpenAIWhisper)
}

// fetchAudio 从 URL 获取音频数据
func (w *WhisperProvider) fetchAudio(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch audio file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to fetch audio file, status: %d", resp.StatusCode)
	}

	return io.ReadAll(resp.Body)
}

// Transcribe 实现语音识别
func (w *WhisperProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	// 获取音频数据
	audioData, err := w.fetchAudio(task.FileUrl)
	if err != nil {
		return TranscriptionResult{}, err
	}

	// 准备请求参数
	req := openai.AudioRequest{
		Model:    w.model,
		Reader:   bytes.NewReader(audioData),
		Language: task.LanguageCode,
		Format:   openai.AudioResponseFormatVerboseJSON,
		Prompt:   "以下是普通话的句子，这是一段会议记录。", // TODO: update prompt
	}

	// 调用 Whisper API
	resp, err := w.client.CreateTranscription(ctx, req)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("whisper transcription failed: %w", err)
	}

	// 转换 segments 为统一格式
	segments := make([]UnifiedSegment, len(resp.Segments))
	for i, seg := range resp.Segments {
		segments[i] = UnifiedSegment{
			ID:        i,
			StartTime: seg.Start,
			EndTime:   seg.End,
			Text:      seg.Text,
		}
	}

	result := TranscriptionResult{
		Text:             resp.Text,
		Segments:         segments,
		DetectedLanguage: resp.Language,
	}

	return result, nil
}
