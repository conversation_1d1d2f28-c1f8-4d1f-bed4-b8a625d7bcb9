package providers

import (
	"context"
	"fmt"
	"log"
	"strings"

	"uniscribe-service/pkg/deepinfra"
)

const (
	deepInfraWhisperTurboModel = "openai/whisper-large-v3-turbo" // DeepInfra Whisper Turbo 模型
	deepInfraWhisperModel      = "openai/whisper-large-v3"       // DeepInfra Whisper 标准模型
	chunkLevelSegment          = "segment"                       // 段落级别的时间戳
	chunkLevelWord             = "word"                          // 单词级别的时间戳
	taskTypeTranscribe         = "transcribe"                    // 识别任务类型
	taskTypeTranslate          = "translate"                     // 翻译任务类型

	// 中文语音识别的初始提示定制
	chineseSimplifiedPrompt  = "请加入标点。"                                 // 大陆普通话
	chineseTraditionalPrompt = "請用正體中文（繁體中文）進行逐字稿，內容需包含完整的標點符號，請避免簡體字。" // 台湾普通话
	cantonesePrompt          = "請用正體中文轉錄，加入標點。"                         // 香港粤语
)

// DeepInfraWhisperProvider 实现了 Provider 接口
type DeepInfraWhisperProvider struct {
	client        *deepinfra.Client
	model         string
	taskType      string  // 任务类型：transcribe 或 translate
	chunkLevel    string  // 时间戳级别：segment 或 word
	chunkLengthS  int     // 分块长度（秒）
	temperature   float64 // 采样温度
	initialPrompt string  // 初始提示文本
}

// NewDeepInfraWhisperProvider 创建一个新的 DeepInfra Whisper provider
func NewDeepInfraWhisperProvider(modelName string) *DeepInfraWhisperProvider {
	if modelName == "" {
		modelName = deepInfraWhisperTurboModel // 默认使用 turbo 版本
	}
	return &DeepInfraWhisperProvider{
		client:        deepinfra.GetClient(),
		model:         modelName,
		taskType:      taskTypeTranscribe,
		chunkLevel:    chunkLevelSegment, // 默认使用段落级别的时间戳
		chunkLengthS:  30,                // 默认分块长度为 30 秒
		temperature:   0,                 // 默认温度为 0，确定性输出
		initialPrompt: "",                // 默认无初始提示
	}
}

// NewDeepInfraWhisperTurboProvider 创建一个使用 Turbo 模型的 DeepInfra Whisper provider
func NewDeepInfraWhisperTurboProvider() *DeepInfraWhisperProvider {
	return NewDeepInfraWhisperProvider(deepInfraWhisperTurboModel)
}

// NewDeepInfraWhisperStandardProvider 创建一个使用标准模型的 DeepInfra Whisper provider
func NewDeepInfraWhisperStandardProvider() *DeepInfraWhisperProvider {
	return NewDeepInfraWhisperProvider(deepInfraWhisperModel)
}

// NewDeepInfraWhisperTurboWordProvider 创建一个使用 Turbo 模型的 DeepInfra Whisper provider (Word级别)
func NewDeepInfraWhisperTurboWordProvider() *DeepInfraWhisperProvider {
	provider := NewDeepInfraWhisperProvider(deepInfraWhisperTurboModel)
	provider.SetChunkLevel(chunkLevelWord)
	return provider
}

// NewDeepInfraWhisperStandardWordProvider 创建一个使用标准模型的 DeepInfra Whisper provider (Word级别)
func NewDeepInfraWhisperStandardWordProvider() *DeepInfraWhisperProvider {
	provider := NewDeepInfraWhisperProvider(deepInfraWhisperModel)
	provider.SetChunkLevel(chunkLevelWord)
	return provider
}

// Name 返回 provider 的名称
func (d *DeepInfraWhisperProvider) Name() string {
	// 根据使用的模型和时间戳级别返回对应的名称
	if d.model == deepInfraWhisperTurboModel {
		if d.chunkLevel == chunkLevelWord {
			return string(DeepInfraWhisperTurboWord)
		}
		return string(DeepInfraWhisperTurbo)
	}
	// 标准模型
	if d.chunkLevel == chunkLevelWord {
		return string(DeepInfraWhisperWord)
	}
	return string(DeepInfraWhisper)
}

// SetTaskType 设置任务类型
func (d *DeepInfraWhisperProvider) SetTaskType(taskType string) *DeepInfraWhisperProvider {
	if taskType == taskTypeTranscribe || taskType == taskTypeTranslate {
		d.taskType = taskType
	}
	return d
}

// SetChunkLevel 设置时间戳级别
func (d *DeepInfraWhisperProvider) SetChunkLevel(level string) *DeepInfraWhisperProvider {
	if level == chunkLevelWord || level == chunkLevelSegment {
		d.chunkLevel = level
	}
	return d
}

// SetChunkLengthS 设置分块长度（秒）
func (d *DeepInfraWhisperProvider) SetChunkLengthS(seconds int) *DeepInfraWhisperProvider {
	if seconds >= 1 && seconds <= 30 {
		d.chunkLengthS = seconds
	}
	return d
}

// SetTemperature 设置采样温度
func (d *DeepInfraWhisperProvider) SetTemperature(temp float64) *DeepInfraWhisperProvider {
	if temp >= 0 {
		d.temperature = temp
	}
	return d
}

// SetInitialPrompt 设置初始提示文本
func (d *DeepInfraWhisperProvider) SetInitialPrompt(prompt string) *DeepInfraWhisperProvider {
	d.initialPrompt = prompt
	return d
}

// Transcribe 实现语音识别
func (d *DeepInfraWhisperProvider) Transcribe(ctx context.Context, task Task) (TranscriptionResult, error) {
	log.Printf("DeepInfra Whisper transcribing file: %s", task.FileUrl)

	// 从 URL 获取音频数据
	audioData, err := d.client.FetchAudio(task.FileUrl)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("failed to fetch audio file: %w", err)
	}

	// 从 URL 中提取文件名
	urlParts := strings.Split(task.FileUrl, "/")
	filename := urlParts[len(urlParts)-1]
	if filename == "" {
		filename = "audio.mp3" // 默认文件名
	}

	// 准备请求选项
	options := map[string]any{
		"chunk_level": d.chunkLevel, // 使用配置的时间戳级别
		// BUG: 对于 word level，如果传了默认参数 chunk_length_s, 可能会导致转录结果丢失内容，原因未知。所以不传。
		// "chunk_length_s": d.chunkLengthS, // 使用配置的分块长度
		"temperature": d.temperature, // 使用配置的温度
		"task":        d.taskType,    // 使用配置的任务类型
	}

	// 设置初始提示：优先使用用户设置的提示，否则根据语言自动选择
	initialPrompt := d.initialPrompt
	if initialPrompt == "" && task.LanguageCode != "" {
		// 根据语言代码自动选择中文优化的初始提示
		switch task.LanguageCode {
		case "zh":
			initialPrompt = chineseSimplifiedPrompt
		case "zh_tw":
			initialPrompt = chineseTraditionalPrompt
		case "yue":
			initialPrompt = cantonesePrompt
		}
	}
	if initialPrompt != "" {
		options["initial_prompt"] = initialPrompt
	}

	// 如果指定了语言，添加语言参数
	if task.LanguageCode != "" {
		// DeepInfra Whisper 使用两字母语言代码
		if task.LanguageCode == "zh_tw" || task.LanguageCode == "zh_cn" || task.LanguageCode == "zh" {
			options["language"] = "zh"
		} else if strings.Contains(task.LanguageCode, "_") {
			// 如果是形如 en_US 的格式，只取前面的语言代码
			options["language"] = strings.Split(task.LanguageCode, "_")[0]
		} else {
			options["language"] = task.LanguageCode
		}
	}

	// 调用 DeepInfra API
	resp, err := d.client.TranscribeAudioData(d.model, audioData, filename, options)
	if err != nil {
		return TranscriptionResult{}, fmt.Errorf("deepinfra whisper transcription failed: %w", err)
	}

	// 转换结果为统一格式
	unifiedSegments := d.convertToUnifiedSegments(resp.Segments, resp.Words)

	// 记录格式转换信息
	if len(resp.Words) > 0 && len(resp.Segments) > 0 && len(resp.Segments[0].Words) == 0 {
		log.Printf("DeepInfra Whisper: Using new top-level words format, converted %d words to %d segments",
			len(resp.Words), len(resp.Segments))
	} else if len(resp.Segments) > 0 && len(resp.Segments[0].Words) > 0 {
		log.Printf("DeepInfra Whisper: Using legacy nested words format")
	}

	return TranscriptionResult{
		Text:             resp.Text,
		Segments:         unifiedSegments,
		DetectedLanguage: resp.Language,
		UsedModel:        AudioModel(d.Name()),
	}, nil
}

// convertToUnifiedSegments 将 DeepInfra Whisper 的片段转换为统一格式
// 支持两种格式：1) 单词在片段内部 2) 单词在顶层数组中
func (d *DeepInfraWhisperProvider) convertToUnifiedSegments(segments []deepinfra.WhisperSegment, topLevelWords []deepinfra.WhisperWord) []UnifiedSegment {
	unifiedSegments := make([]UnifiedSegment, len(segments))

	for i, segment := range segments {
		// 创建统一格式的片段
		unifiedSegment := UnifiedSegment{
			ID:        segment.ID,
			StartTime: segment.Start,
			EndTime:   segment.End,
			Text:      segment.Text,
			Words:     []Word{},
		}

		// 优先使用片段内的单词（旧格式）
		if len(segment.Words) > 0 {
			words := make([]Word, len(segment.Words))
			for j, word := range segment.Words {
				// 优先使用 Word 字段（实际API返回），如果为空则使用 Text 字段（文档格式）
				wordText := word.Word
				if wordText == "" {
					wordText = word.Text
				}

				words[j] = Word{
					StartTime: word.Start,
					EndTime:   word.End,
					Text:      wordText,
				}
			}
			unifiedSegment.Words = words
		} else if len(topLevelWords) > 0 {
			// 使用顶层单词数组（新格式），根据时间戳匹配单词到片段
			segmentWords := d.assignWordsToSegment(segment, topLevelWords)
			unifiedSegment.Words = segmentWords
		}

		unifiedSegments[i] = unifiedSegment
	}
	return unifiedSegments
}

// assignWordsToSegment 将顶层单词数组中的单词分配给指定片段
// 使用改进的时间重叠算法，处理边界情况
func (d *DeepInfraWhisperProvider) assignWordsToSegment(segment deepinfra.WhisperSegment, topLevelWords []deepinfra.WhisperWord) []Word {
	var segmentWords []Word
	const overlapThreshold = 0.1 // 10% 重叠阈值

	for _, word := range topLevelWords {
		// 计算时间重叠
		overlapStart := max(word.Start, segment.Start)
		overlapEnd := min(word.End, segment.End)
		overlapDuration := overlapEnd - overlapStart

		if overlapDuration <= 0 {
			continue // 没有重叠
		}

		// 计算重叠比例（相对于单词持续时间）
		wordDuration := word.End - word.Start
		if wordDuration <= 0 {
			continue // 无效的单词时间
		}

		overlapRatio := overlapDuration / wordDuration

		// 如果重叠比例超过阈值，则认为该单词属于当前片段
		if overlapRatio >= overlapThreshold {
			// 优先使用 Word 字段（实际API返回），如果为空则使用 Text 字段（文档格式）
			wordText := word.Word
			if wordText == "" {
				wordText = word.Text
			}

			segmentWords = append(segmentWords, Word{
				StartTime: word.Start,
				EndTime:   word.End,
				Text:      wordText,
			})
		}
	}
	return segmentWords
}

// max 返回两个 float64 中的较大值
func max(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

// min 返回两个 float64 中的较小值
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
