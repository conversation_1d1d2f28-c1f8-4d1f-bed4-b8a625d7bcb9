package constants

type TaskType int
type TaskStatus int

// 任务状态定义 - 与 Python 端保持一致
const (
	TaskStatusPending    TaskStatus = 1 // 任务已创建，等待执行
	TaskStatusProcessing TaskStatus = 2 // 处理中
	TaskStatusCompleted  TaskStatus = 3 // 处理完成
	TaskStatusFailed     TaskStatus = 4 // 处理失败
)

// 任务类型定义
// 关键任务：转录
// 非关键任务：摘要、大纲生成、问答生成和翻译。
//
// 任务的依赖关系：
//  1. 转录任务依赖于音频文件
//  2. 其他任务依赖于转录任务，这些任务可以并行执行
//
// 注意：校对任务和关键词任务已废弃
const (
	TaskTypeTranscription      TaskType = iota + 1 // 转录任务
	TaskTypeCorrection                             // 校对任务 (已废弃)
	TaskTypeSummary                                // 摘要任务
	TaskTypeKeyword                                // 关键词提取任务 (已废弃)
	TaskTypeOutline                                // 大纲生成任务
	TaskTypeQAExtraction                           // 问答生成任务
	TaskTypeTranslation                            // 翻译任务
	TaskTypeYoutubeDownload                        // YouTube 下载任务
	TaskTypeSpeakerDiarization                     // 说话人识别任务
)

// Redis Stream 名称常量（支持优先级）
const (
	StreamTranscriptionHigh      = "tasks:transcription:high"
	StreamTranscriptionLow       = "tasks:transcription:low"
	StreamTextHigh               = "tasks:text:high"
	StreamTextLow                = "tasks:text:low"
	StreamSpeakerDiarizationHigh = "tasks:speaker_diarization:high"
	StreamSpeakerDiarizationLow  = "tasks:speaker_diarization:low"
	StreamRetry                  = "tasks:retry"
)
