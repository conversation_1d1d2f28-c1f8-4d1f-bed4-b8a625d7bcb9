package main

import (
	"context"
	"log"
	"os"

	openai "github.com/sashabaranov/go-openai"
)

// 全局 OpenAI 客户端
var openaiClient *openai.Client

func init() {
	// 初始化 OpenAI 客户端
	api_key := os.Getenv("OPENAI_API_KEY")
	config := openai.DefaultConfig(api_key)
	config.BaseURL = "https://f58f7bee34a5d9de8271b4e58d0ca304.api-forwards.com/v1"
	// config.BaseURL = "https://api.gptsapi.net/v1"
	openaiClient = openai.NewClientWithConfig(config)
}

func transcribeSegment(segmentPath string) (openai.AudioResponse, error) {
	// TODO: 实现音频片段的转写, use openai whisper api
	ctx := context.Background()
	req := openai.AudioRequest{
		Model:    openai.Whisper1,
		FilePath: segmentPath,
		Format:   openai.AudioResponseFormatVerboseJSON,
		Prompt:   "以下是普通话的句子，这是一段会议记录",
	}
	return openaiClient.CreateTranscription(ctx, req)
}

func main() {
	filePath := "/Users/<USER>/Downloads/slice_one_minute.mp3"
	r, err := transcribeSegment(filePath)
	if err != nil {
		log.Fatalf("transcribeSegment error: %v, %v", err, r)
	}
	log.Printf("transcribeSegment response: %v", r)
}
