# 定义变量
BINARY_DIR=bin
BINARY_NAME=uniscribe-service
BINARY_PATH=$(BINARY_DIR)/$(BINARY_NAME)
SRC=$(wildcard cmd/uniscribe-service/*.go) $(wildcard handler/*.go)
VERSION=$(shell git describe --tags --always --long --dirty)

# 默认目标
all: build

# 安装依赖项
deps:
	@echo "Setting GOPROXY..."
	export GOPROXY=https://goproxy.cn,direct
	@echo "Installing dependencies..."
	go mod tidy

# 实现 go build 命令
build:
	@echo "Building the project..."
	go build -o $(BINARY_PATH) $(SRC)


# 为 Linux 构建
build-linux:
	@echo "为 Linux 构建项目..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o $(BINARY_PATH)-linux-amd64 $(SRC)

# 打包 Linux 二进制
package-linux: build-linux
	@echo "打包 Linux 二进制..."
	tar -czvf $(BINARY_PATH)-linux-amd64-$(VERSION).tar.gz $(BINARY_PATH)-linux-amd64

scp-azure: package-linux
	scp -i ~/.ssh/azure-vm-linux-key.pem $(BINARY_PATH)-linux-amd64 chendahui@172.214.255.230:~/uniscribe-mono/uniscribe-service/bin/$(BINARY_NAME)-linux-amd64-$(VERSION)


# 实现 go run 命令
run:
	@echo "Running the project..."
	env $(cat .env) go run $(SRC)

# 测试目标
test:
	@echo "Running tests..."
	go test ./...

# 清理目标
clean:
	@echo "Cleaning up..."
	rm -f $(BINARY_PATH) $(BINARY_PATH)-linux-amd64 $(BINARY_PATH)-linux-amd64-*.tar.gz

# 伪目标，避免与文件名冲突
.PHONY: all build build-linux package-linux run test clean deps