package deepinfra

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"time"

	"uniscribe-service/internal/config"

	"github.com/hashicorp/go-retryablehttp"
)

const (
	baseURL = "https://api.deepinfra.com/v1/inference"
)

// Client 是 DeepInfra API 的客户端
type Client struct {
	retryableClient *retryablehttp.Client
	apiKey          string
}

// NewClient 创建一个新的 DeepInfra API 客户端
func NewClient(apiKey string) *Client {
	retryClient := retryablehttp.NewClient()

	// 配置重试策略
	retryClient.RetryMax = 3
	retryClient.RetryWaitMin = time.Second * 1
	retryClient.RetryWaitMax = time.Second * 30
	retryClient.HTTPClient.Timeout = time.Second * 60 * 20 // 20分钟超时，转录大文件可能需要较长时间

	// 配置重试条件：只对网络错误和5xx错误重试
	retryClient.CheckRetry = func(ctx context.Context, resp *http.Response, err error) (bool, error) {
		// 如果有网络错误，重试
		if err != nil {
			log.Printf("DeepInfra request failed with error: %v, will retry", err)
			return true, nil
		}

		// 对于5xx服务器错误重试
		if resp.StatusCode >= 500 {
			log.Printf("DeepInfra request failed with status %d, will retry", resp.StatusCode)
			return true, nil
		}

		// 其他情况不重试
		return false, nil
	}

	// 配置日志
	retryClient.Logger = &retryLogger{}

	return &Client{
		retryableClient: retryClient,
		apiKey:          apiKey,
	}
}

// retryLogger 实现 retryablehttp.Logger 接口
type retryLogger struct{}

func (l *retryLogger) Printf(format string, v ...interface{}) {
	log.Printf("[DeepInfra Retry] "+format, v...)
}

// GetClient 返回 DeepInfra 客户端的单例实例
func GetClient() *Client {
	return NewClient(config.Cfg.DeepInfraApiKey)
}

// WhisperResponse 表示 Whisper API 的响应
type WhisperResponse struct {
	Text     string            `json:"text"`
	Segments []WhisperSegment  `json:"segments"`
	Words    []WhisperWord     `json:"words,omitempty"` // 顶层单词数组，新的API响应格式
	Language string            `json:"language"`
	Status   WhisperStatusInfo `json:"inference_status"`
}

// WhisperSegment 表示 Whisper 转录的单个片段
type WhisperSegment struct {
	ID    int           `json:"id"`
	Text  string        `json:"text"`
	Start float64       `json:"start"`
	End   float64       `json:"end"`
	Words []WhisperWord `json:"words,omitempty"` // 单词级别的时间戳，仅当 chunk_level=word 时有效
}

// WhisperWord 表示单词级别的时间戳
type WhisperWord struct {
	Word  string  `json:"word"` // 实际API返回的字段名
	Text  string  `json:"text"` // 文档中的字段名，保持兼容性
	Start float64 `json:"start"`
	End   float64 `json:"end"`
}

// WhisperStatusInfo 包含推理状态信息
type WhisperStatusInfo struct {
	Status          string  `json:"status"`
	RuntimeMs       int     `json:"runtime_ms"`
	Cost            float64 `json:"cost"`
	TokensGenerated int     `json:"tokens_generated"`
	TokensInput     int     `json:"tokens_input"`
}

// FetchAudio 从 URL 获取音频数据，支持自动重试
func (c *Client) FetchAudio(url string) ([]byte, error) {
	req, err := retryablehttp.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.retryableClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch audio file: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to fetch audio file, status: %d", resp.StatusCode)
	}

	return io.ReadAll(resp.Body)
}

// TranscribeAudioData 使用 DeepInfra Whisper 模型转录音频数据，支持自动重试
func (c *Client) TranscribeAudioData(modelName string, audioData []byte, filename string, options map[string]any) (*WhisperResponse, error) {
	url := fmt.Sprintf("%s/%s", baseURL, modelName)

	// 创建一个 multipart 表单
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// 添加音频文件
	part, err := writer.CreateFormFile("audio", filename)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}
	if _, err := io.Copy(part, bytes.NewReader(audioData)); err != nil {
		return nil, fmt.Errorf("failed to copy audio data: %w", err)
	}

	// 添加其他选项
	for key, value := range options {
		// 将值转换为字符串
		var strValue string
		switch v := value.(type) {
		case string:
			strValue = v
		case float64:
			strValue = fmt.Sprintf("%f", v)
		case int:
			strValue = fmt.Sprintf("%d", v)
		case bool:
			strValue = fmt.Sprintf("%t", v)
		default:
			jsonValue, err := json.Marshal(v)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal value for field %s: %w", key, err)
			}
			strValue = string(jsonValue)
		}

		if err := writer.WriteField(key, strValue); err != nil {
			return nil, fmt.Errorf("failed to write field %s: %w", key, err)
		}
	}

	// 完成 multipart 表单
	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close multipart writer: %w", err)
	}

	// 创建请求
	req, err := retryablehttp.NewRequest(http.MethodPost, url, &requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", fmt.Sprintf("bearer %s", c.apiKey))

	// 发送请求
	resp, err := c.retryableClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 读取响应体内容用于日志记录
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 打印原始 JSON 响应内容
	log.Printf("DeepInfra API raw response: %s", string(respBody))

	// 解析响应
	var result WhisperResponse
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &result, nil
}
