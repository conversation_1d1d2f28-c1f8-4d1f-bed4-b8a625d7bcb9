package dashscope

import (
	"github.com/devinyf/dashscopego"
	"github.com/devinyf/dashscopego/paraformer"

	"uniscribe-service/internal/config"
)

const ParaformerV2 paraformer.ModelParaformer = "paraformer-v2"

var dashscopeClient *dashscopego.TongyiClient

func initClient() {
	dashscopeClient = dashscopego.NewTongyiClient(ParaformerV2, config.Cfg.DashscopeApiKey)
}

func GetClient() *dashscopego.TongyiClient {
	if dashscopeClient == nil {
		initClient()
	}
	return dashscopeClient
}
