package openai

import (
	"log"
	"net/http"
	"net/url"

	openai "github.com/sa<PERSON><PERSON>nov/go-openai"

	"uniscribe-service/internal/config"
)

// 全局 OpenAI 客户端
var openaiClient *openai.Client

func initClient() {
	// 从环境变量中获取代理 URL
	proxyURLStr := config.Cfg.ProxyURLStr

	var httpClient *http.Client

	// 初始化 OpenAI 客户端
	if proxyURLStr != "" {
		log.Printf("Using proxy URL: %s", proxyURLStr)
		proxyUrl, err := url.Parse(proxyURLStr)
		if err != nil {
			log.Fatalf("Invalid proxy URL: %v", err)
		}
		httpClient = &http.Client{
			Transport: &http.Transport{
				Proxy: http.ProxyURL(proxyUrl),
			},
		}
	} else {
		log.Printf("No proxy URL provided, using default http client")
		httpClient = &http.Client{}
	}

	api_key := config.Cfg.OpenaiApiKey
	config := openai.DefaultConfig(api_key)
	config.HTTPClient = httpClient
	// 直接使用 OpenAI API，不经过 wildcard 转发，因为转录接口通常会很耗时，会导致 wildcard 转发超时（504 gateway timeout）
	// 通过代理访问 OpenAI API
	openaiClient = openai.NewClientWithConfig(config)
}

func GetClient() *openai.Client {
	if openaiClient == nil {
		initClient()
	}
	return openaiClient
}
