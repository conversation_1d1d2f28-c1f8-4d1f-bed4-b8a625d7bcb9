package fal

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"uniscribe-service/internal/config"
)

const (
	baseURL = "https://queue.fal.run"
)

// Client 简化版 Fal API 客户端
type Client struct {
	httpClient *http.Client
	apiKey     string
}

// RequestStatus 表示请求状态
type RequestStatus struct {
	Status string `json:"status"`
}

// QueueResponse 表示队列响应
type QueueResponse struct {
	RequestID string `json:"request_id"`
}

// NewClient 创建新的 Fal 客户端
func NewClient(apiKey string) *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		apiKey: apiKey,
	}
}

// GetClient 返回 Fal 客户端单例
func GetClient() *Client {
	return NewClient(config.Cfg.FalApiKey)
}

// QueueRequest 提交请求到队列
func (c *Client) QueueRequest(appID string, payload map[string]interface{}) (*QueueResponse, error) {
	url := fmt.Sprintf("%s/%s", baseURL, appID)

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("marshal payload failed: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Key %s", c.apiKey))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var queueResp QueueResponse
	if err := json.NewDecoder(resp.Body).Decode(&queueResp); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &queueResp, nil
}

// GetRequestStatus 获取请求状态（简化版，替代 StreamRequestStatus）
func (c *Client) GetRequestStatus(appID, requestID string) (*RequestStatus, error) {
	url := fmt.Sprintf("%s/%s/requests/%s/status", baseURL, appID, requestID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Key %s", c.apiKey))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// 接受 200 (任务完成) 和 202 (任务进行中) 状态码
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var status RequestStatus
	if err := json.NewDecoder(resp.Body).Decode(&status); err != nil {
		return nil, fmt.Errorf("decode status failed: %w", err)
	}

	return &status, nil
}

// GetRequestResult 获取请求结果
func (c *Client) GetRequestResult(appID, requestID string) ([]byte, error) {
	url := fmt.Sprintf("%s/%s/requests/%s", baseURL, appID, requestID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Key %s", c.apiKey))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	return io.ReadAll(resp.Body)
}

// CancelRequest 取消正在运行的请求
func (c *Client) CancelRequest(appID, requestID string) error {
	url := fmt.Sprintf("%s/%s/requests/%s/cancel", baseURL, appID, requestID)

	req, err := http.NewRequest("POST", url, nil)
	if err != nil {
		return fmt.Errorf("create cancel request failed: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Key %s", c.apiKey))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("cancel request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("cancel request failed with status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}
